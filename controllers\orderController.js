const { ORDER, ORDER_TRADE, ORDER_RELATION } = require('../db').tableNames
const { resFilter, getKeysObj } = require('../utils/filter')
const { ORDER_STATUS, ORDER_RELATION_TYPE, ORDER_INFO_STATUS } = require('../constant/enum')
const dbController = require('./dbController')
const orderUpdateKeys = [
	'mobile',
	'countOnCreate',
	'priceOnCreate',
	'count',
	'price',
	'latitude',
	'longitude',
	'cityCode',
	'address',
	'status',
	'infoStatus',
	'fixedPrice',
	'collectorId',
	'collectorTime',
	'pickType',
	'pickProcess',
	'variety',
	'humidity',
	'forklift',
	'semitrailer',
	'overallVideos',
	'videos',
	'photos',
	'remark',
	'updateTime'
]
const orderCreateKeys = ['userId', 'cropId', 'cropChildId'].concat(orderUpdateKeys)

// 新建订单
function createOrder(reqData) {
	return new Promise((resolve, reject) => {
		let orderData = getKeysObj(reqData, orderCreateKeys)
		dbController.add(ORDER, orderData, (err, res) => {
			if (err) {
				reject(err)
			} else {
				// TODO: 新订单创建后执行
				// nzSubscribeController.sendBuyerNewOrderMessage(id);
				resolve({ orderId: res.insertId })
			}
		})
	})
}

// 记录用户点击某一个订单的电话
function addMobileClickLog(userId, orderId) {
	return new Promise(function (resolve, reject) {
		dbController.add(
			ORDER_RELATION,
			{
				userId,
				orderId,
				relationType: ORDER_RELATION_TYPE.telephone
			},
			(err, res) => {
				if (err) {
					reject(err)
				} else {
					resolve('已记录')
				}
			}
		)
	})
}

// 记录用户访问某一个订单
function addOrderViewLog(userId, orderId) {
	return new Promise(function (resolve, reject) {
		dbController.add(
			ORDER_RELATION,
			{
				userId,
				orderId,
				relationType: ORDER_RELATION_TYPE.view
			},
			(err, res) => {
				if (err) {
					reject(err)
				} else {
					resolve('已记录')
				}
			}
		)
	})
}

// 新增请求测量记录
function addOrderAsk(userId, orderId) {
	return new Promise((resolve, reject) => {
		dbController.query(
			ORDER_RELATION,
			{ userId, orderId, isDelete: 0, relationType: ORDER_RELATION_TYPE.ask },
			null,
			(err, rows) => {
				if (err) {
					reject(err)
				} else if (rows[0]) {
					resolve('已请求')
				} else {
					dbController.add(
						ORDER_RELATION,
						{
							userId,
							orderId,
							relationType: ORDER_RELATION_TYPE.ask
						},
						(err, res) => {
							if (err) {
								reject(err)
							} else {
								resolve('请求成功')
								setOrderInfoStatusToAsk(orderId)
							}
						}
					)
				}
			}
		)
	})
}

function setOrderInfoStatusToAsk(id) {
	dbController.query(ORDER, { id }, null, (err, rows) => {
		if (!err && rows[0] && rows[0].info_status === ORDER_INFO_STATUS.default) {
			dbController.update(
				ORDER,
				{ id },
				{ infoStatus: ORDER_INFO_STATUS.ask, updateTime: rows[0].update_time }
			)
		}
	})
}

function addOrderLike(userId, orderId) {
	return new Promise((resolve, reject) => {
		dbController.query(
			ORDER_RELATION,
			{ userId, orderId, isDelete: 0, relationType: ORDER_RELATION_TYPE.like },
			null,
			(err, rows) => {
				if (err) {
					reject(err)
				} else if (rows[0]) {
					resolve('已收藏')
				} else {
					dbController.add(
						ORDER_RELATION,
						{
							userId,
							orderId,
							relationType: ORDER_RELATION_TYPE.like
						},
						(err, res) => {
							if (err) {
								reject(err)
							} else {
								resolve('收藏成功')
							}
						}
					)
				}
			}
		)
	})
}

function cancelOrderLike(userId, orderId) {
	return new Promise((resolve, reject) => {
		dbController.query(
			ORDER_RELATION,
			{ userId, orderId, isDelete: 0, relationType: ORDER_RELATION_TYPE.like },
			null,
			(err, rows) => {
				if (err) {
					reject(err)
				} else if (rows[0]) {
					dbController.update(ORDER_RELATION, { id: rows[0].id }, { isDelete: 1 }, err => {
						if (err) {
							reject(err)
						} else {
							resolve('已取消收藏')
						}
					})
				} else {
					resolve('未收藏')
				}
			}
		)
	})
}

function queryUserOrderRelationList(userId, orderId) {
	return new Promise((resolve, reject) => {
		dbController.query(ORDER_RELATION, { userId, orderId, isDelete: 0 }, null, (err, rows) => {
			if (err) {
				reject(err)
			} else {
				resolve(rows.map(item => resFilter(item)))
			}
		})
	})
}

// 农民取消订单
function cancelOrder(id, userId) {
	return new Promise((resolve, reject) => {
		dbController.query(ORDER, { id }, null, (err, rows) => {
			if (err) {
				reject(err)
			} else if (rows[0]) {
				const { user_id } = rows[0]
				if (userId !== user_id) {
					reject('无权操作')
				} else {
					dbController.update(ORDER, { id }, { status: 0 }, err => {
						if (err) {
							reject(err)
						} else {
							resolve('订单取消成功')
						}
					})
				}
			} else {
				reject('订单不存在')
			}
		})
	})
}

function deleteOrder(id) {
	return new Promise((resolve, reject) => {
		dbController.delete(ORDER, { id }, (err, rows) => {
			if (err) {
				reject(err)
			} else {
				resolve('删除成功')
			}
		})
	})
}

// 农民或者信息收集员更新订单信息
function updateOrder(id, reqData) {
	return new Promise((resolve, reject) => {
		let orderData = getKeysObj(reqData, orderUpdateKeys)
		dbController.update(ORDER, { id }, orderData, (err, res) => {
			if (err) {
				reject(err)
			} else {
				resolve('订单更新成功')
				// TODO: 更新订单后执行
				// nzSubscribeController.sendBuyerNewOrderMessage(id);
			}
		})
	})
}

// 信息收集员更新订单信息
function updateOrderByCollector(id, reqData) {
	const { userId } = reqData
	return new Promise((resolve, reject) => {
		let orderData = getKeysObj(reqData, orderUpdateKeys)
		dbController.update(
			ORDER,
			{ id },
			Object.assign(orderData, { collectorId: userId, infoStatus: ORDER_INFO_STATUS.finish }),
			(err, res) => {
				if (err) {
					reject(err)
				} else {
					resolve('订单更新成功')
					// setOrderInfoStatusToFinish();
					// TODO: 更新订单后执行
					// nzSubscribeController.sendBuyerNewOrderMessage(id);
				}
			}
		)
	})
}

// 首页查询订单列表
function queryHomeOrders(pageIndex, pageSize) {
	return new Promise((resolve, reject) => {
		dbController.count(ORDER, { status: ORDER_STATUS.default }, (err, rows) => {
			if (err) {
				reject(err)
			} else {
				const total = rows[0]['count(*)']
				dbController.query(
					ORDER,
					{ status: ORDER_STATUS.default },
					{ pageIndex, pageSize, orderBy: 'updateTime' },
					(err, rows) => {
						if (err) {
							reject(err)
						} else {
							if (rows.length > 0) {
								const ids = rows.map(item => item.id)
								dbController.query(
									ORDER_RELATION,
									{ relationType: ORDER_RELATION_TYPE.telephone, orderId: ids },
									null,
									(err, rows1) => {
										if (err) {
											reject(err)
										} else {
											resolve({
												total,
												list: rows.map(item => {
													const userIds = []
													rows1.forEach(rItem => {
														if (rItem.order_id === item.id) {
															if (!userIds.includes(rItem.user_id)) {
																userIds.push(rItem.user_id)
															}
														}
													})
													return resFilter(Object.assign({ phoneCount: userIds.length }, item))
												})
											})
										}
									}
								)
							} else {
								resolve({
									total,
									list: []
								})
							}
						}
					}
				)
			}
		})
	})
}

// 首页查询已交易的订单列表
function queryHomeTradeOrders(pageIndex, pageSize) {
	return new Promise((resolve, reject) => {
		dbController.count(ORDER_TRADE, null, (err, rows) => {
			if (err) {
				reject(err)
			} else {
				const total = rows[0]['count(*)']
				dbController.query(
					ORDER_TRADE,
					null,
					{ pageIndex, pageSize, orderBy: 'id' },
					(err, trades) => {
						if (err) {
							reject(err)
						} else {
							const oIds = trades.map(item => item.order_id)
							dbController.query(ORDER, { id: oIds }, null, (err, orders) => {
								if (err) {
									reject(err)
								} else {
									const orderMap = {}
									orders.forEach(item => {
										orderMap[item.id] = resFilter(item)
									})
									const list = trades.map(item => {
										const newItem = resFilter(item)
										newItem.order = orderMap[newItem.orderId]
										delete newItem.orderId
										return newItem
									})
									resolve({
										total,
										list
									})
								}
							})
						}
					}
				)
			}
		})
	})
}

// 首页查询周边订单列表
function queryHomeNearbyOrders(longitude, latitude, pageIndex, pageSize) {
	return new Promise((resolve, reject) => {
		//地球半径
		const EARTH_RADIUS = 6378137.0 //单位M
		const EARTH_CIRCLE = EARTH_RADIUS * Math.PI * 2
		const maxAngle = ((50 * 1000) / EARTH_CIRCLE) * 360
		const minLongitude = Math.round((longitude * 1 - maxAngle) * 100000) / 100000
		const maxLongitude = Math.round((longitude * 1 + maxAngle) * 100000) / 100000
		const minLatitude = Math.round((latitude * 1 - maxAngle) * 100000) / 100000
		const maxLatitude = Math.round((latitude * 1 + maxAngle) * 100000) / 100000

		const arr = [
			'`longitude` > ' + minLongitude,
			'`longitude` < ' + maxLongitude,
			'`latitude` > ' + minLatitude,
			'`latitude` < ' + maxLatitude
		]
		const _special = arr.join(' AND ')
		dbController.count(ORDER, { status: ORDER_STATUS.default, _special }, (err, rows) => {
			if (err) {
				reject(err)
			} else {
				const total = rows[0]['count(*)']
				dbController.query(
					ORDER,
					{ status: ORDER_STATUS.default, _special },
					{ pageIndex, pageSize, orderBy: 'updateTime' },
					(err, rows) => {
						if (err) {
							reject(err)
						} else {
							if (rows.length > 0) {
								const ids = rows.map(item => item.id)
								dbController.query(
									ORDER_RELATION,
									{ relationType: ORDER_RELATION_TYPE.telephone, orderId: ids },
									null,
									(err, rows1) => {
										if (err) {
											reject(err)
										} else {
											resolve({
												total,
												list: rows.map(item => {
													const phoneCount = rows1.filter(
														rItem => rItem.order_id === item.id
													).length
													return resFilter(Object.assign({ phoneCount }, item))
												})
											})
										}
									}
								)
							} else {
								resolve({ total, list: [] })
							}
						}
					}
				)
			}
		})
	})
}

// 首页查询订单列表
function queryAskOrders(pageIndex, pageSize) {
	return new Promise((resolve, reject) => {
		dbController.count(ORDER, { infoStatus: ORDER_INFO_STATUS.ask }, (err, rows) => {
			if (err) {
				reject(err)
			} else {
				const total = rows[0]['count(*)']
				if (total > 0) {
					dbController.query(
						ORDER,
						{ infoStatus: ORDER_INFO_STATUS.ask },
						{ pageIndex, pageSize, orderBy: 'updateTime' },
						(err, rows) => {
							if (err) {
								reject(err)
							} else {
								const ids = rows.map(item => item.id)
								dbController.query(
									ORDER_RELATION,
									{ relationType: ORDER_RELATION_TYPE.telephone, orderId: ids },
									null,
									(err, rows1) => {
										if (err) {
											reject(err)
										} else {
											resolve({
												total,
												list: rows.map(item => {
													const phoneCount = rows1.filter(
														rItem => rItem.order_id === item.id
													).length
													return resFilter(Object.assign({ phoneCount }, item))
												})
											})
										}
									}
								)
							}
						}
					)
				} else {
					resolve({
						total,
						list: []
					})
				}
			}
		})
	})
}

function queryOrderById(id) {
	return new Promise((resolve, reject) => {
		dbController.query(ORDER, { id }, null, (err, rows) => {
			if (err) {
				reject(err)
			} else if (rows[0]) {
				dbController.count(
					ORDER_RELATION,
					{ relationType: ORDER_RELATION_TYPE.telephone, orderId: rows[0].id },
					(err, rows1) => {
						if (err) {
							reject(err)
						} else {
							const phoneCount = rows1[0]['count(*)']
							resolve(resFilter(Object.assign({ phoneCount }, rows[0])))
						}
					}
				)
			} else {
				reject('未查询到相关订单')
			}
		})
	})
}

function queryTradeById(id) {
	return new Promise((resolve, reject) => {
		dbController.query(ORDER_TRADE, { id }, null, (err, rows) => {
			if (err) {
				reject(err)
			} else if (rows[0]) {
				dbController.query(ORDER, { id: rows[0].order_id }, null, (err, rows1) => {
					if (err) {
						reject(err)
					} else {
						const trade = resFilter(rows[0])
						trade.order = resFilter(rows1[0])
						resolve(trade)
					}
				})
			} else {
				reject('未查询到相关订单')
			}
		})
	})
}

function queryUserOrders(userId, status) {
	return new Promise((resolve, reject) => {
		dbController.query(ORDER, { userId, status }, { orderBy: 'updateTime' }, (err, rows) => {
			if (err) {
				reject(err)
			} else {
				resolve(rows.map(item => resFilter(item)))
			}
		})
	})
}

function queryUserLikeOrders(userId) {
	return new Promise((resolve, reject) => {
		dbController.query(
			ORDER_RELATION,
			{ userId, relationType: ORDER_RELATION_TYPE.like, isDelete: 0 },
			null,
			(err, rows) => {
				if (err) {
					reject(err)
				} else {
					if (rows.length > 0) {
						let orderIds = rows.map(item => item.order_id)
						dbController.query(ORDER, { id: orderIds }, { orderBy: 'updateTime' }, (err, rows) => {
							if (err) {
								reject(err)
							} else {
								resolve(rows.map(item => resFilter(item)))
							}
						})
					} else {
						resolve([])
					}
				}
			}
		)
	})
}

function queryOrders(reqBody) {
	const {
		cropId,
		cropChildId,
		address,
		infoStatus,
		status,
		mobile,
		dateFrom,
		dateTo,
		pageIndex,
		pageSize
	} = reqBody
	let _special
	if (dateFrom && dateTo) {
		//设定了查询时间范围
		_special = `create_time between "${dateFrom}" and "${dateTo}"`
	}
	const queryOptions = {
		cropId,
		cropChildId,
		'address.like': address,
		infoStatus,
		status,
		'mobile.like': mobile,
		_special
	}
	return new Promise((resolve, reject) => {
		dbController.count(ORDER, queryOptions, (err, rows) => {
			if (err) {
				reject(err)
			} else {
				const total = rows[0]['count(*)']
				dbController.query(ORDER, queryOptions, { pageIndex, pageSize }, (err, rows) => {
					if (err) {
						reject(err)
					} else {
						resolve({
							total,
							list: rows.map(item => resFilter(item))
						})
					}
				})
			}
		})
	})
}

// 查询多条订单的交易记录
function queryOrderTrades(orderIdArr) {
	return new Promise((resolve, reject) => {
		dbController.query(ORDER_TRADE, { orderId: orderIdArr }, null, (err, rows) => {
			if (err) {
				reject(err)
			} else {
				resolve(rows)
			}
		})
	})
}

// 添加订单交易结果
function addOrderTrade(orderId, reqData) {
	const { userId, price, priceUnit, humidity, way } = reqData
	return new Promise((resolve, reject) => {
		dbController.add(
			ORDER_TRADE,
			{
				orderId,
				userId,
				price,
				priceUnit,
				humidity,
				way
			},
			(err, rows1) => {
				if (err) {
					reject(err)
				} else {
					dbController.update(
						ORDER,
						{ id: orderId },
						{ status: ORDER_STATUS.finish },
						(err, rows2) => {
							if (err) {
								reject(err)
							} else {
								resolve(rows1.insertId)
							}
						}
					)
				}
			}
		)
	})
}

module.exports = {
	createOrder,
	addMobileClickLog,
	addOrderViewLog,
	cancelOrder,
	deleteOrder,
	updateOrder,
	queryHomeOrders,
	queryHomeTradeOrders,
	queryHomeNearbyOrders,
	queryAskOrders,
	queryUserOrderRelationList,
	queryOrderById,
	queryTradeById,
	queryUserOrders,
	queryUserLikeOrders,
	queryOrders,
	queryOrderTrades,
	addOrderTrade,
	addOrderLike,
	cancelOrderLike,
	addOrderAsk,
	updateOrderByCollector
}
