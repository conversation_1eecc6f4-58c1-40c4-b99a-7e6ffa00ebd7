// 农舟农作物模块
const dbController = require('./dbController')
const { toHump, resFilter, getKeysObj } = require('../utils/filter')
const { CROP, CROP_COLLECTION, CROP_CATEGORY } = require('../db').tableNames
const cropTableKeys = [
	'parentId',
	'countUnitOnCreate',
	'priceUnitOnCreate',
	'priceMin',
	'priceMax',
	'countUnit',
	'priceUnit',
	'sortNumber',
	'collectionIds',
	'collectionJumpIds',
	'considerWayMinCount',
	'bigOrderMinCount',
	'image',
	'online',
	'showPrice',
	'regions',
	'excludeRegions',
	'sort',
	'dateSwitchDelay',
	'overallVideoMsg',
	'videoMsg',
	'photoMsg',
	'name',
	'defaultHumidity'
]
const collectionKeys = ['key', 'label', 'type', 'options', 'message']
const categoryKeys = ['cropId', 'name']

// 新建收购作物
function addCrop(reqData) {
	return new Promise((resolve, reject) => {
		dbController
			.query(CROP, { _special: '`id` < 100' }, { pageIndex: 0, pageSize: 1, orderBy: 'id' })
			.then(corpRes => {
				const lastItem = corpRes[0]
				const orderData = getKeysObj(reqData, cropTableKeys)
				orderData.id = lastItem.id + 1
				orderData.online = 0
				orderData.showPrice = 0
				dbController.add(CROP, orderData, err => {
					if (err) {
						reject(err)
					} else {
						resolve('创建成功')
					}
				})
			})
			.catch(err => {
				reject(err)
			})
	})
}

// 删除农作物
function deleteCrop(id) {
	return dbController.delete(CROP, { id })
}

// 更新收购作物
function updateCrop(id, reqData) {
	return new Promise((resolve, reject) => {
		let orderData = getKeysObj(reqData, cropTableKeys)
		dbController.update(CROP, { id }, orderData, (err, res) => {
			if (err) {
				reject(err)
			} else {
				resolve('更新成功')
			}
		})
	})
}

// 新建信息收集条目
function addCollection(reqData) {
	return new Promise((resolve, reject) => {
		let orderData = getKeysObj(reqData, collectionKeys)
		dbController.add(CROP_COLLECTION, orderData, (err, res) => {
			if (err) {
				reject(err)
			} else {
				resolve('创建成功')
			}
		})
	})
}

// 更新信息收集条目
function updateCollection(id, reqData) {
	return new Promise((resolve, reject) => {
		let orderData = getKeysObj(reqData, collectionKeys)
		dbController.update(CROP_COLLECTION, { id }, orderData, (err, res) => {
			if (err) {
				reject(err)
			} else {
				resolve('更新成功')
			}
		})
	})
}

// 新建农作物的品类
function addCategory(reqData) {
	return new Promise((resolve, reject) => {
		const data = getKeysObj(reqData, categoryKeys)
		dbController.add(CROP_CATEGORY, data, (err, res) => {
			if (err) {
				reject(err)
			} else {
				resolve('创建成功')
			}
		})
	})
}

// 更新农作物的品类
function updateCategory(id, reqData) {
	return new Promise((resolve, reject) => {
		const data = getKeysObj(reqData, collectionKeys)
		dbController.update(CROP_CATEGORY, { id }, data, (err, res) => {
			if (err) {
				reject(err)
			} else {
				resolve('更新成功')
			}
		})
	})
}

// 查询全部农作物
function queryCrops(online, showPrice, ignoreChildren) {
	return new Promise(function (resolve, reject) {
		const queryParams = { online, showPrice }
		if (ignoreChildren == 1) {
			queryParams.parentId = 0
		}
		dbController.query(CROP, queryParams, null, (err, rows) => {
			if (err) {
				reject(err)
			} else {
				resolve(rows.map(item => resFilter(item)))
			}
		})
	})
}

// 根据id查询农作物
function queryCropById(id) {
	return dbController.query(CROP, { id }, null).then(rows => {
		if (rows.length >= 1) {
			return resFilter(rows[0])
		} else {
			throw new Error('数据不存在')
		}
	})
}

// 查询价格页面的展示农作物
function queryPricePageCrops() {
	return new Promise(function (resolve, reject) {
		dbController.query(CROP, { show_price: 1 }, { orderBy: 'sort' }, (err, rows) => {
			if (err) {
				reject(err)
			} else {
				resolve(rows.map(item => resFilter(item)))
			}
		})
	})
}

// 查询全部农作物信息收集的条目
function queryCollections() {
	return new Promise(function (resolve, reject) {
		dbController.query(CROP_COLLECTION, null, null, (err, rows) => {
			if (err) {
				reject(err)
			} else {
				resolve(rows.map(item => resFilter(item)))
			}
		})
	})
}

// 查询农作物跟农作物所需要收集的信息
function queryCropsAndCollections() {
	return new Promise(function (resolve, reject) {
		dbController.query(CROP, null, null, (err, rows1) => {
			if (err) {
				reject(err)
			} else {
				dbController.query(CROP_COLLECTION, null, null, (err, rows2) => {
					if (err) {
						reject(err)
					} else {
						resolve({
							crops: rows1.map(item => resFilter(item)),
							collections: rows2.map(item => {
								item = resFilter(item)
								item.collectionKey = toHump(item.collectionKey)
								return item
							})
						})
					}
				})
			}
		})
	})
}

module.exports = {
	queryCrops,
	queryCropById,
	queryPricePageCrops,
	queryCollections,
	queryCropsAndCollections,
	addCrop,
	deleteCrop,
	updateCrop,
	addCollection,
	updateCollection,
	addCategory,
	updateCategory
}
