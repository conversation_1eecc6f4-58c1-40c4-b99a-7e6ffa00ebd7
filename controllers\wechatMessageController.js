const dbController = require('./dbController')
const { resFilter, getKeysObj } = require('../utils/filter')
const { WECHAT_RAW_MESSAGES } = require('../db').tableNames
const cropController = require('./cropController')
const cropPriceController = require('./cropPriceController')
const cropCompanyController = require('./cropCompanyController')
const transportController = require('./transportController')

const loadBaseData = async () => {
	const [cropArr, companyArr, transportCompanyArr] = await Promise.all([
		cropController.queryCrops(null, null, 1),
		cropCompanyController.queryAllCompanies(),
		transportController.queryAll()
	])

	if (!cropArr?.length || !companyArr?.length || !transportCompanyArr?.length) {
		throw new Error('公司或作物基础信息加载失败')
	}
	return { cropArr, companyArr, transportCompanyArr }
}

const handleCropPriceAndDetail =
	({ cropId, cropName, companyId, companyName, price, unit, date, detail }, content) =>
	async () => {
		try {
			const addPriceResult = await cropPriceController.addPrice(
				cropId,
				companyId,
				companyName,
				price,
				unit,
				date
			)
			const addDetailResult = await cropCompanyController.addCompanyDetail({
				companyId,
				cropId,
				desc: detail,
				photos: ''
			})
			return [
				{
					rawData: { cropId, cropName, companyId, companyName, price, unit, date },
					message: addPriceResult,
					successType: 'PRICE_ADDED'
				},
				{
					rawData: { cropId, cropName, companyId, companyName, detail },
					message: addDetailResult,
					successType: 'DETAIL_ADDED'
				}
			]
		} catch (error) {
			return Promise.reject(error)
		}
	}

const handleTransportData =
	({ id, factoryName, todayData, date }) =>
	async () => {
		try {
			const addPriceResult = await transportController.editTransportData(id, {
				factoryName,
				todayData,
				date
			})

			let successType = 'DATA_UPDATED'
			if (addPriceResult.includes('工厂名称更新成功')) {
				successType = 'NAME_UPDATED'
			} else if (addPriceResult.includes('数据更新成功')) {
				successType = 'DATA_UPDATED'
			}

			return {
				rawData: { id, factoryName, todayData, date },
				message: addPriceResult,
				successType
			}
		} catch (error) {
			return Promise.reject(error)
		}
	}

/**
 * 处理厂车数据
 * @param {Array} structuredInfo 结构化的消息数据
 * @param {Array} transportCompanyArr 厂车公司数据
 * @returns {Object} 包含处理结果和错误项
 */
const processTransportData = (structuredInfo, transportCompanyArr) => {
	const errorItems = []
	const mappedData = []

	structuredInfo.forEach(item => {
		const transportCompany = transportCompanyArr.filter(
			company => company.factoryName === item.factoryName
		)[0]

		if (!transportCompany) {
			errorItems.push({
				rawData: item,
				message: `未匹配到厂车工厂: ${item.factoryName}`,
				errorType: 'TRANSPORT_MATCH_ERROR'
			})
			return
		}

		mappedData.push({
			id: transportCompany.id,
			factoryName: item.factoryName,
			todayData: item.todayData,
			date: item.date
		})
	})

	return {
		mappedData,
		errorItems,
		operationPromises: mappedData.map(item => handleTransportData(item))
	}
}

/**
 * 处理农作物价格数据
 * @param {Array} structuredInfo 结构化的消息数据
 * @param {Array} cropArr 作物数据
 * @param {Array} companyArr 公司数据
 * @param {String} content 消息内容
 * @returns {Object} 包含处理结果和错误项
 */
const processCropData = (structuredInfo, cropArr, companyArr, content) => {
	const errorItems = []
	const mappedData = []

	const companyMap = new Map(companyArr.map(({ name, id }) => [name, id]))
	const cropMap = new Map(cropArr.map(({ name, id, priceUnit }) => [name, { id, name, priceUnit }]))

	structuredInfo.forEach(item => {
		try {
			const companyName = [...companyMap.keys()].filter(name => name.includes(item.company))[0]
			if (!companyName) {
				errorItems.push({
					rawData: item,
					message: `未匹配到公司信息: ${item.company}`,
					errorType: 'COMPANY_MATCH_ERROR'
				})
				return
			}

			const cropName = [...cropMap.keys()].filter(name => name.includes(item.crop))[0]
			if (!cropName) {
				errorItems.push({
					rawData: item,
					message: `未匹配到作物信息: ${item.crop}`,
					errorType: 'CROP_MATCH_ERROR'
				})
				return
			}

			const crop = cropMap.get(cropName)
			const companyId = companyMap.get(companyName)

			mappedData.push({
				cropId: crop.id,
				cropName: crop.name,
				companyId,
				companyName,
				price: item.price,
				unit: item.unit || crop.priceUnit,
				date: item.date,
				detail: item.detail
			})
		} catch (err) {
			errorItems.push({
				rawData: item,
				message: err?.message || '数据处理出错',
				errorType: 'PROCESS_ERROR'
			})
		}
	})

	return {
		mappedData,
		errorItems,
		operationPromises: mappedData.map(item => handleCropPriceAndDetail(item, content))
	}
}

/**
 * 处理仅录入详情数据
 * @param {Array} structuredInfo 结构化的消息数据
 * @param {Array} cropArr 作物数据
 * @param {Array} companyArr 公司数据
 * @param {String} content 消息内容
 * @returns {Object} 包含处理结果和错误项
 * @description 该函数专门处理只需要详情的业务逻辑，即使structuredInfo中存在价格信息也会被忽略
 * 只关注company、crop、date三个字段，将详情内容(content)添加到对应的作物-公司关联中
 */
const processDetailsOnly = (structuredInfo, cropArr, companyArr, content) => {
	const errorItems = []
	const mappedData = []

	const companyMap = new Map(companyArr.map(({ name, id }) => [name, id]))
	const cropMap = new Map(cropArr.map(({ name, id }) => [name, { id, name }]))

	structuredInfo.forEach(item => {
		try {
			if (!item.company || !item.crop) {
				errorItems.push({
					rawData: item,
					message: `缺少必要的信息: 需要公司和作物信息`,
					errorType: 'MISSING_REQUIRED_FIELDS'
				})
				return
			}

			const companyName = [...companyMap.keys()].filter(name => name.includes(item.company))[0]
			if (!companyName) {
				errorItems.push({
					rawData: item,
					message: `未匹配到公司信息: ${item.company}`,
					errorType: 'COMPANY_MATCH_ERROR'
				})
				return
			}

			const cropName = [...cropMap.keys()].filter(name => name.includes(item.crop))[0]
			if (!cropName) {
				errorItems.push({
					rawData: item,
					message: `未匹配到作物信息: ${item.crop}`,
					errorType: 'CROP_MATCH_ERROR'
				})
				return
			}

			const crop = cropMap.get(cropName)
			const companyId = companyMap.get(companyName)

			mappedData.push({
				cropId: crop.id,
				cropName: crop.name,
				companyId,
				companyName,
				detail: item.detail,
				date: item.date || new Date().toISOString().split('T')[0] // 使用提供的日期或当天日期
			})
		} catch (err) {
			errorItems.push({
				rawData: item,
				message: err?.message || '数据处理出错',
				errorType: 'PROCESS_ERROR'
			})
		}
	})

	return {
		mappedData,
		errorItems,
		operationPromises: mappedData.map(item => async () => {
			try {
				const addDetailResult = await cropCompanyController.addCompanyDetail({
					companyId: item.companyId,
					cropId: item.cropId,
					desc: item.detail,
					photos: '',
					date: item.date
				})
				return {
					rawData: item,
					message: addDetailResult,
					successType: 'DETAILS_ADDED'
				}
			} catch (error) {
				return Promise.reject(error)
			}
		})
	}
}

/**
 * 处理操作结果，收集错误信息和成功信息
 * @param {Array} operationResults 操作结果数组
 * @param {Array} mappedData 映射后的数据
 * @returns {Object} 包含错误项和成功项的对象
 */
const collectOperationResults = (operationResults, mappedData) => {
	const operationErrors = []
	const successResults = []

	operationResults.forEach((result, index) => {
		if (result.status === 'rejected') {
			operationErrors.push({
				rawData: mappedData[index],
				message: result.reason?.message || String(result.reason) || '未知错误',
				errorType: 'OPERATION_ERROR'
			})
		} else if (result.status === 'fulfilled') {
			const value = result.value

			if (value && !Array.isArray(value)) {
				successResults.push({
					rawData: value.rawData || mappedData[index],
					message: value.message || '操作成功',
					successType: 'DATA_UPDATED'
				})
			}

			// 处理可能返回数组的情况（如handleCropPriceAndDetail）
			if (Array.isArray(value)) {
				value.forEach(subResult => {
					if (subResult && subResult.status === 'rejected') {
						operationErrors.push({
							rawData: subResult.rawData || mappedData[index],
							message: subResult.reason?.message || String(subResult.reason) || '子操作失败',
							errorType: 'SUB_OPERATION_ERROR'
						})
					} else if (subResult) {
						successResults.push({
							rawData: subResult.rawData || mappedData[index],
							message: subResult.message || '子操作成功',
							successType: 'SUB_OPERATION_SUCCESS'
						})
					}
				})
			}
		}
	})

	return { operationErrors, successResults }
}

const dataProcessingStrategies = {
	transport: ({ structuredInfo, baseData, content }) => {
		const result = processTransportData(structuredInfo, baseData.transportCompanyArr)
		return {
			mappedData: result.mappedData,
			operationPromises: result.operationPromises,
			initialErrorItems: result.errorItems
		}
	},

	details_only: ({ structuredInfo, baseData, content }) => {
		const result = processDetailsOnly(
			structuredInfo,
			baseData.cropArr,
			baseData.companyArr,
			content
		)
		return {
			mappedData: result.mappedData,
			operationPromises: result.operationPromises,
			initialErrorItems: result.errorItems
		}
	},

	urea: ({ structuredInfo, baseData, content }) => {
		return dataProcessingStrategies.normal({ structuredInfo, baseData, content })
	},

	article: ({ structuredInfo, baseData, content }) => {
		return dataProcessingStrategies.normal({ structuredInfo, baseData, content })
	},

	normal: ({ structuredInfo, baseData, content }) => {
		const result = processCropData(structuredInfo, baseData.cropArr, baseData.companyArr, content)
		return {
			mappedData: result.mappedData,
			operationPromises: result.operationPromises,
			initialErrorItems: result.errorItems
		}
	}
}

/**
 * 策略选择器 - 根据dataType选择对应的处理策略
 * @param {string} dataType 数据类型，可选值：transport、details_only、urea、article、normal
 * @returns {Function} 对应的处理策略函数
 */
const getProcessingStrategy = dataType => {
	return dataProcessingStrategies[dataType] || dataProcessingStrategies.normal
}

module.exports = {
	async receiveMessage(req, res) {
		try {
			const {
				msgType,
				content,
				roomId,
				room,
				sender,
				senderId,
				senderName,
				sendTime,
				dataType = 'normal'
			} = req.body || {}
			const structuredInfo = JSON.parse(req.body.structuredInfo || '[]')
			if (!structuredInfo.length) {
				return res.status(400).json({ code: 400, message: '参数缺失', data: req.body })
			}

			const baseData = await loadBaseData()

			const rawData = {
				content,
				roomId,
				room,
				sender,
				senderId,
				senderName,
				sendTime,
				analysis_result: JSON.stringify(structuredInfo)
			}

			// 使用策略模式处理不同类型的数据
			const processingStrategy = getProcessingStrategy(dataType)
			const { mappedData, operationPromises, initialErrorItems } = processingStrategy({
				structuredInfo,
				baseData,
				content
			})

			const [addRawResult, ...operationResults] = await Promise.allSettled([
				dbController.add(WECHAT_RAW_MESSAGES, rawData),
				...operationPromises.map(fn => fn())
			])

			const allErrors = [...initialErrorItems]

			if (addRawResult.status === 'rejected') {
				console.error('原始消息保存失败:', addRawResult.reason)
				allErrors.push({
					message: `原始消息保存失败: ${
						addRawResult.reason?.message || String(addRawResult.reason)
					}`,
					errorType: 'RAW_MESSAGE_SAVE_ERROR'
				})
			}

			const { operationErrors, successResults } = collectOperationResults(
				operationResults,
				mappedData
			)
			allErrors.push(...operationErrors)

			const successCount = successResults.length
			const failedCount = allErrors.length

			res.json({
				code: 200,
				status: failedCount > 0 ? (successCount > 0 ? 'partial_success' : 'failed') : 'success',
				message: `消息处理完成: 成功${successCount}项，失败${failedCount}项`,
				data: {
					failedItems: allErrors,
					successItems: successResults,
					totalProcessed: mappedData.length
				}
			})
		} catch (err) {
			console.error('消息保存失败:', err)
			res.status(500).json({
				code: 500,
				status: 'error',
				message: err?.message || String(err),
				error: {
					type: 'PROCESSING_ERROR',
					details: err?.stack
				}
			})
		}
	}
}
