import http from '@/utils/http'

// 获取行政区域列表（带分页和搜索）
function getAreaList(params = {}) {
  return http.post('admin/area/list', params)
}

// 获取所有行政区域数据
function getAllAreas() {
  return http.get('admin/area/all')
}

// 获取所有省份
function getProvinces() {
  return http.get('admin/area/provinces')
}

// 根据省份代码获取城市列表
function getCitiesByProvince(provinceCode) {
  return http.get(`admin/area/cities/${provinceCode}`)
}

// 根据城市代码获取区县列表
function getCountiesByCity(cityCode) {
  return http.get(`admin/area/areas/${cityCode}`)
}

// 根据区县代码获取街道列表
function getStreetsByCounty(countyCode) {
  return http.get(`admin/area/streets/${countyCode}`)
}

// 通用下级查询接口
function getChildrenByParentCode(parentCode, level = null) {
  const url = `admin/area/children/${parentCode}`
  const params = level ? { level } : {}
  return http.get(url, { params })
}

function processUploadedAreaData(params) {
  return http.post('admin/area/process', params)
}

// 行政区划层级常量
const ADMINISTRATIVE_LEVELS = {
  PROVINCE: 1, // 省级
  CITY: 2, // 市级
  DISTRICT: 3 // 区/县级
}

export default {
  ADMINISTRATIVE_LEVELS,
  getAreaList,
  getAllAreas,
  getProvinces,
  getCitiesByProvince,
  getCountiesByCity,
  getStreetsByCounty,
  getChildrenByParentCode,
  processUploadedAreaData
}
