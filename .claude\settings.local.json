{"permissions": {"allow": ["Bash(npm run lint)", "Bash(npm run format:check:*)", "WebSearch", "Read(E:\\dev_workspace\\qianwancang\\node-backend\\routes\\admin/**)", "Read(E:\\dev_workspace\\qianwancang\\node-backend/**)", "Read(E:\\dev_workspace\\qianwancang\\node-backend\\routes/**)", "Read(E:\\dev_workspace\\qianwancang\\node-backend\\routes/**)", "Read(E:\\dev_workspace\\qianwancang\\node-backend\\controllers/**)", "Read(E:\\dev_workspace\\qianwancang\\node-backend/**)", "Read(E:\\dev_workspace\\qianwancang\\node-backend/**)", "Read(E:\\dev_workspace\\qianwancang\\node-backend\\controllers/**)", "Read(E:\\dev_workspace\\qianwancang\\node-backend\\controllers/**)", "Read(E:\\dev_workspace\\qianwancang\\node-backend\\controllers/**)", "Read(E:\\dev_workspace\\qianwancang\\node-backend\\controllers/**)", "Read(E:\\dev_workspace\\qianwancang\\node-backend/**)", "Read(E:\\dev_workspace\\qianwancang\\node-backend/**)", "Read(E:\\dev_workspace\\qianwancang\\node-backend/**)", "Read(E:\\dev_workspace\\qianwancang\\node-backend/**)", "Read(E:\\dev_workspace\\qianwancang\\node-backend/**)", "Read(E:\\dev_workspace\\qianwancang\\node-backend/**)", "Read(E:\\dev_workspace\\qianwancang\\node-backend/**)", "Read(E:\\dev_workspace\\qianwancang\\node-backend/**)", "Read(E:\\dev_workspace\\qianwancang\\node-backend\\routes/**)", "mcp__ide__getDiagnostics", "Read(E:\\dev_workspace\\qianwancang/**)", "Read(E:\\dev_workspace\\qianwancang\\node-backend\\routes/**)", "Read(E:\\dev_workspace\\qianwancang\\node-backend\\routes/**)", "Read(E:\\dev_workspace\\qianwancang\\node-backend/**)", "Read(E:\\dev_workspace\\qianwancang\\node-backend\\routes/**)", "Read(E:\\dev_workspace\\qianwancang\\node-backend\\routes/**)", "Read(E:\\dev_workspace\\qianwancang\\node-backend\\data/**)", "Read(E:\\dev_workspace\\qianwancang\\node-backend\\data/**)", "Bash(npm run dev:*)", "Bash(node:*)", "Bash(npm run:*)"], "deny": [], "ask": [], "additionalDirectories": ["E:\\dev_workspace\\qianwancang\\node-backend", "E:\\e\\dev_workspace\\qianwancang"]}}