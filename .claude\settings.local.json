{"permissions": {"allow": ["Bash(node:*)", "WebFetch(domain:lswz.hebei.gov.cn)", "Bash(rm:*)", "WebFetch(domain:www.chinafeed.org.cn)", "WebSearch", "WebFetch(domain:www.stats.gov.cn)", "WebFetch(domain:github.com)", "Read(/E:\\e\\dev_workspace\\qianwancang\\node-backend/**)", "Read(/E:\\e\\dev_workspace\\qianwancang\\node-backend/**)", "Read(/E:\\e\\dev_workspace\\qianwancang\\node-backend\\routes/**)", "Read(/E:\\e\\dev_workspace\\qianwancang\\node-backend/**)", "WebFetch(domain:data.stats.gov.cn)", "Read(/E:\\dev_workspace\\qianwancang\\admin-frontend\\src\\views\\price/**)", "Read(/E:\\dev_workspace\\qianwancang\\admin-frontend\\src\\views\\price/**)", "Read(/E:\\dev_workspace\\qianwancang\\admin-frontend\\src\\views\\price/**)", "Read(/E:\\dev_workspace\\qianwancang\\admin-frontend\\src\\views\\price/**)", "Read(/E:\\dev_workspace\\qianwancang\\admin-frontend\\src\\views\\price/**)", "Read(/E:\\dev_workspace\\qianwancang\\admin-frontend\\src\\mixins/**)", "Bash(npm start)", "<PERSON><PERSON>(curl:*)", "Read(/E:\\E\\dev_workspace\\qianwancang/**)", "Read(/E:\\E\\dev_workspace\\qianwancang/**)", "Read(/E:\\E\\dev_workspace\\qianwancang/**)", "Read(/E:\\E\\dev_workspace\\qianwancang/**)", "Read(/E:\\E\\dev_workspace\\qianwancang/**)", "Read(/E:\\dev_workspace\\qianwancang\\admin-frontend/**)", "Read(/E:\\dev_workspace\\qianwancang\\admin-frontend\\src\\mixins/**)", "Read(/E:\\dev_workspace\\qianwancang\\admin-frontend\\src\\mixins/**)", "Read(/E:\\dev_workspace\\qianwancang\\admin-frontend\\src\\mixins/**)", "Read(/E:\\dev_workspace\\qianwancang\\admin-frontend\\src\\views\\system/**)", "Read(/E:\\dev_workspace\\qianwancang\\admin-frontend\\src\\manager/**)"], "deny": [], "ask": []}}