<template>
  <div>
    <el-table
      v-loading="loading"
      :data="tableData"
      border
      fit
      highlight-current-row
      style="width: 100%"
      empty-text="暂无数据"
    >
      <el-table-column label="ID" prop="id" align="center" width="80"></el-table-column>
      <el-table-column label="标题" prop="title" align="center">
        <template slot-scope="{ row }">
          <el-link type="primary" :underline="false" :href="row.originalLink" target="_blank">{{
            row.title
          }}</el-link>
        </template>
      </el-table-column>
      <el-table-column label="来源" prop="source" align="center">
        <template slot-scope="{ row }">
          <div>{{ sourceLabels[row.source] }}</div>
          <div>{{ row.category }}</div>
        </template>
      </el-table-column>
      <el-table-column label="作物品类" prop="cropIds" align="center" width="120">
        <template slot-scope="{ row }">
          <el-tag v-for="tag in row.cropTags" :key="tag" size="mini">{{ tag }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="摘要" prop="summary" align="center"></el-table-column>

      <el-table-column label="发布时间" prop="publishTime" align="center" width="100">
        <template slot-scope="{ row }">{{
          new Date(row.publishTime).format('yyyy-MM-dd')
        }}</template>
      </el-table-column>
      <el-table-column label="更新时间" prop="updateTime" align="center" width="160">
        <template slot-scope="{ row }">{{
          new Date(row.updateTime).format('yyyy-MM-dd hh:mm:ss')
        }}</template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="220">
        <template slot-scope="{ row }">
          <span class="el_btn_box">
            <el-button size="small" @click="$emit('handleInfoDetail', row)">详情</el-button>
            <el-button size="small" @click="$emit('handleEditCrop', row)">编辑</el-button>
            <el-button type="danger" size="small" @click="$emit('handleDelete', row)"
              >删除</el-button
            >
          </span>
        </template>
      </el-table-column>
    </el-table>
    <Pagination
      v-if="total > 0"
      :total="total"
      :pageIndex.sync="currentPageIndex"
      :pageSize.sync="currentPageSize"
      @pagination="handlePagination"
    />
  </div>
</template>

<script>
  import Pagination from '@/components/Pagination'
  export default {
    name: 'InfoTable',
    components: { Pagination },
    props: {
      loading: {
        type: Boolean,
        default: false
      },
      tableData: {
        type: Array,
        default: () => []
      },
      sourceLabels: {
        type: Object,
        default: () => ({})
      },
      total: {
        type: Number,
        default: 0
      },
      pageIndex: {
        type: Number,
        default: 0
      },
      pageSize: {
        type: Number,
        default: 10
      }
    },
    data() {
      return {
        currentPageIndex: 0,
        currentPageSize: 10
      }
    },
    watch: {
      pageIndex: {
        handler(val) {
          this.currentPageIndex = val
        },
        immediate: true
      },
      pageSize: {
        handler(val) {
          this.currentPageSize = val
        },
        immediate: true
      },
      currentPageIndex(val) {
        this.$emit('update:pageIndex', val)
      },
      currentPageSize(val) {
        this.$emit('update:pageSize', val)
      }
    },
    methods: {
      handlePagination({ pageIndex, pageSize }) {
        this.$emit('pagination', { pageIndex, pageSize })
      }
    }
  }
</script>
