const axios = require('axios')
const cheerio = require('cheerio')

function convertToAbsoluteUrl(url, baseUrl) {
	try {
		const parsedUrl = new URL(url)
		return parsedUrl.href
	} catch (e) {
		if (baseUrl) {
			return new URL(url, baseUrl).href
		} else {
			throw new Error('Base URL is required when the input URL is relative.')
		}
	}
}

async function fetchAndParse(url, parser, options) {
	// 参数验证
	if (!url || typeof url !== 'string') {
		throw new Error('Invalid URL provided')
	}
	if (!parser || typeof parser !== 'function') {
		throw new Error('Invalid parser provided')
	}

	try {
		const { data } = await axios.get(url)
		const $ = cheerio.load(data, { xml: { decodeEntities: false }, ...options }, false)
		return parser($)
	} catch (error) {
		throw new Error(`Failed to fetch or parse data from ${url}: ${error.message || error}`)
	}
}

class ScraperAgri {
	async fetchAndParse(url, parser) {
		try {
			const { data } = await axios.get(url)
			const $ = cheerio.load(data, { xml: { decodeEntities: false } }, false)
			return parser($)
		} catch (error) {
			throw error
		}
	}

	scrapeDetailPage(url) {
		const parser = $ => {
			const content = $('.TRS_Editor').html()
			const author = $('.updateInfo_mess .mess_text')
				.last()
				.text()
				.replace(/来源：/, '')
				.trim()
			return { content, author }
		}
		return this.fetchAndParse(url, parser)
	}

	scrapeListPage(url) {
		const parser = $ => {
			const results = []
			$('.nxw_list_li .list_li_con').each((index, element) => {
				const title = $(element).find('.con_tit a').text().trim()
				const relativeLink = $(element).find('.con_tit a').attr('href')
				const originalLink = new URL(relativeLink, url).href
				const summary = $(element).find('.con_text').text().trim()
				const publishTime = $(element).find('.con_date_span').text().trim()
				results.push({
					title,
					originalLink,
					summary,
					source: 1, // 1:中国农业农村信息网
					publishTime
				})
			})
			return results
		}
		return this.fetchAndParse(url, parser)
	}

	/**
	 * 入口函数，用于启动爬虫并发送数据
	 * @param {string} url - 要爬取的 URL
	 * @param {string} type - 爬取类型 ('detail' 或 'list')
	 * @returns {Promise<Object>} API 请求的响应
	 */
	async startScraping(url, type) {
		if (!url) {
			throw new Error('缺少爬取目标地址')
		}
		try {
			let data
			if (type === 'list') data = await this.scrapeListPage(url)
			if (type === 'detail') data = await this.scrapeDetailPage(url)
			return data
		} catch (error) {
			console.error('[Error during scraping:]', error)
			throw error
		}
	}
}
class ScraperGrainmarket {
	async fetchAndParse(url, method, params) {
		try {
			const { data } = await axios.post(url, `param=${JSON.stringify(params)}`, {
				headers: {
					'Content-Type': 'application/x-www-form-urlencoded; charset=utf-8'
				}
			})
			return method(data)
		} catch (error) {
			throw error
		}
	}

	scrapeDetailPage(url) {
		const params = {
			m: 'tradeCenterNewsDetail',
			articleId: url.split('/').pop()
		}
		const method = data => ({ content: data.data.Content, author: data.data.ArticleFrom })
		return this.fetchAndParse('https://www.grainmarket.com.cn/centerweb/getData', method, params)
	}

	scrapeListPage(url, page) {
		const params = {
			m: 'tradeCenterOtherNewsList',
			indexid: page.toString() || '1',
			pagesize: '15',
			articleTypeID: '10'
		}
		const method = data =>
			data?.data.map(item => {
				return {
					title: item.title,
					originalLink: item.contentUrl,
					summary: '',
					source: 2, // 2:国家粮食交易中心
					publishTime: item.publishtime
				}
			}) || []
		return this.fetchAndParse(url, method, params)
	}

	/**
	 * 入口函数，用于启动爬虫并发送数据
	 * @param {string} url - 要爬取的 URL
	 * @param {string} type - 爬取类型 ('detail' 或 'list')
	 * @returns {Promise<Object>} API 请求的响应
	 */
	async startScraping(url, type, page) {
		if (!url) {
			throw new Error('缺少爬取目标地址')
		}
		try {
			let data
			if (type === 'list') data = await this.scrapeListPage(url, page)
			if (type === 'detail') data = await this.scrapeDetailPage(url)
			return data
		} catch (error) {
			console.error('[Error during scraping:]', error)
			throw error
		}
	}
}

class ScraperChinasoybean {
	constructor() {
		this.url = 'http://www.chinasoybean.org.cn'
	}
	async fetchAndParse(url, parser) {
		try {
			const { data } = await axios.get(url)
			const $ = cheerio.load(data, { xml: { decodeEntities: false } }, false)
			return parser($)
		} catch (error) {
			throw error
		}
	}

	scrapeDetailPage(url) {
		const parser = $ => {
			const content = $('#textarea').html()
			const author = $('#textarea p')
				.last()
				.text()
				.replace(/来源：/, '')
				.trim()
			return { content, author }
		}
		return this.fetchAndParse(url, parser)
	}

	scrapeListPage(queryUrl) {
		const parser = $ => {
			const results = []
			$('.new_r > ul > li').each((index, element) => {
				const title = $(element).find('h1').text().trim()
				const relativeLink = $(element).find('a').attr('href')
				const originalLink = new URL(relativeLink, this.url).href
				const sourceId = new URL(originalLink).searchParams.get('id')
				const publishTime = $(element).find('span').text().trim()
				results.push({
					title,
					originalLink,
					sourceId: parseInt(sourceId),
					publishTime
				})
			})
			return results
		}
		return this.fetchAndParse(`${this.url}/${queryUrl}`, parser)
	}
}

class ScraperChinaCotton {
	constructor() {
		this.url = 'https://www.china-cotton.org'
	}

	extractIdFromUrl(url) {
		const match = url.match(/\/(\d+)\.html$/)
		if (match && match[1]) {
			return match[1]
		}
		return null
	}

	convertToAbsoluteUrl(url) {
		try {
			const parsedUrl = new URL(url)
			return parsedUrl.href
		} catch (e) {
			return new URL(url, this.url).href
		}
	}

	async fetchAndParse(url, parser) {
		try {
			const { data } = await axios.get(url)
			const $ = cheerio.load(data, { xml: { decodeEntities: false } }, false)
			return parser($)
		} catch (error) {
			throw error
		}
	}

	scrapeDetailPage(url) {
		const parser = $ => {
			const container = $('.container')
			const title = $(container).find('.article > h1').text().trim()
			let content = $(container).find('.txt').html()
			content = content.replace(/<img\s+[^>]*src="([^"]*)"[^>]*>/gi, (match, p1) => {
				if (!p1.startsWith('http')) {
					const absoluteSrc = new URL(p1, this.url).href
					return match.replace(p1, absoluteSrc)
				}
				return match
			})
			const author = $(container)
				.find('.article-time > span')
				.first()
				.text()
				.replace(/出处：/, '')
				.trim()
			return { title, content, author }
		}
		return this.fetchAndParse(url, parser)
	}

	scrapeListPage(queryUrl) {
		const parser = $ => {
			const results = []
			$('.container > .u7-l-2 > .special > .special-li').each((index, element) => {
				const title = $(element).find('a').text()
				const relativeLink = $(element).find('a').attr('href')
				const originalLink = this.convertToAbsoluteUrl(relativeLink)
				const sourceId = this.extractIdFromUrl(originalLink)
				const publishTime = $(element).find('span').text().trim()
				if (sourceId) {
					results.push({
						title,
						originalLink,
						sourceId: parseInt(sourceId),
						publishTime
					})
				}
			})
			return results
		}
		return this.fetchAndParse(`${this.url}/${queryUrl}`, parser)
	}
}

class ScraperSDLscb {
	constructor() {
		this.url = 'http://lscb.shandong.gov.cn'
		this.headers = {
			'User-Agent':
				'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36'
		}
	}

	async fetchAndParse(url, parser) {
		try {
			const { data } = await axios.get(url, { headers: this.headers })
			const $ = cheerio.load(data, { xml: true })
			return parser($)
		} catch (error) {
			throw error
		}
	}

	extractIdFromUrl(url) {
		const match = url.match(/_(\d+)\.html$/)
		if (match && match[1]) {
			return match[1]
		}
		return null
	}

	scrapeDetailPage(url) {
		const parser = $ => {
			const container = $('#container')
			const tableEle = $(container).find('table').not('[class]').first()
			const title = $(tableEle).find('.fttt > p').text().trim()
			let content = $(tableEle)
				.find('tbody>tr:nth-child(2)>td>table>tbody>tr:nth-child(2) p')
				.html()

			return { title, content }
		}
		return this.fetchAndParse(url, parser)
	}

	scrapeListPage(queryUrl) {
		const parser = $ => {
			const results = []
			$('#container record')
				.contents()
				.each((index, element) => {
					const inner$ = cheerio.load(element.children[0].data, { xml: true })
					inner$('.pagedContent').each((i, recordElement) => {
						const title = $(recordElement).find('a').text()
						const relativeLink = $(recordElement).find('a').attr('href')
						const originalLink = convertToAbsoluteUrl(relativeLink, this.url)
						const sourceId = this.extractIdFromUrl(originalLink)
						const publishTime = $(recordElement).find('td:last').text().trim()
						if (sourceId && title.includes('收购进度')) {
							results.push({
								title,
								originalLink,
								sourceId: parseInt(sourceId),
								publishTime
							})
						}
					})
				})
			return results
		}
		return this.fetchAndParse(`${this.url}/${queryUrl}`, parser)
	}
}

class ScraperHBLswz {
	constructor() {
		this.url = 'https://lswz.hebei.gov.cn/lysc/hqsp/'
	}

	extractIdFromUrl(url) {
		const match = url.match(/_(\d+)\.html$/)
		if (match && match[1]) {
			return match[1]
		}
		return null
	}

	scrapeDetailPage(url) {
		const parser = $ => {
			const container = $('.contentbox')
			const title = $(container).find('h1').text().trim()
			let content = $(container).find('.contentmain .TRS_Editor').html()
			return { title, content }
		}
		return fetchAndParse(url, parser)
	}

	scrapeListPage(queryUrl) {
		const parser = $ => {
			const results = []
			$('.border_tabejbig .rgtbar_erji .list_rgterji li').each((index, element) => {
				const title = $(element).find('a').text()
				const relativeLink = $(element).find('a').attr('href')
				const originalLink = convertToAbsoluteUrl(relativeLink, this.url)
				const sourceId = this.extractIdFromUrl(originalLink)
				const publishTime = $(element).find('span').text().trim()
				if (sourceId && title.includes('粮油市场月度分析报告')) {
					results.push({
						title,
						originalLink,
						sourceId,
						publishTime
					})
				}
			})
			return results
		}
		return fetchAndParse(`${this.url}${queryUrl}`, parser)
	}
}

class ScraperJLLswz {
	constructor() {
		this.url = 'http://grain.jl.gov.cn/ztzl/jllssgxx/'
	}

	extractIdFromUrl(url) {
		const match = url.match(/_(\d+)\.html$/)
		if (match && match[1]) {
			return match[1]
		}
		return null
	}

	scrapeDetailPage(url) {
		const parser = $ => {
			const container = $('.content')
			const title = $(container).find('.biaoti_title').text().trim()
			// const author = $(container).find('.laiyuan').text().trim()
			const author = '粮食储备处'
			let content = $(container).find('.TRS_Editor').html()
			content = content.replace(/<img\b[^>]*\bsrc=["']([^"']*)["'][^>]*>/gi, (match, p1) => {
				if (!p1.startsWith('http')) {
					const absoluteSrc = new URL(p1, url).href
					return match.replace(p1, absoluteSrc)
				}
				return match
			})
			return { title, author, content }
		}
		return fetchAndParse(url, parser)
	}

	scrapeListPage(queryUrl) {
		const parser = $ => {
			const results = []
			$('.content .erji_content .ul_main li').each((index, element) => {
				const title = $(element).find('a').text()
				const relativeLink = $(element).find('a').attr('href')
				const originalLink = convertToAbsoluteUrl(relativeLink, this.url)
				const sourceId = this.extractIdFromUrl(originalLink)
				const publishTime = $(element).find('.time').text().trim()
				if (sourceId) {
					results.push({
						title,
						originalLink,
						sourceId,
						publishTime
					})
				}
			})
			return results
		}
		return fetchAndParse(`${this.url}${queryUrl}`, parser)
	}
}

class ScraperXJLswz {
	constructor() {
		this.url = 'https://lswz.xinjiang.gov.cn'
	}

	extractIdFromUrl(url) {
		const parts = url.split('/')
		const lastPart = parts[parts.length - 1]
		const identifier = lastPart.replace('.shtml', '')
		return identifier
	}

	scrapeDetailPage(url) {
		const parser = $ => {
			const container = $('.index_block')
			const title = $(container).find('.subtitle').text().trim()
			const author = $(container)
				.find('.from')
				.text()
				.replace(/来源：/, '')
				.trim()
			let content = $(container).find('.content_p').html()
			return { title, author, content }
		}
		return fetchAndParse(url, parser)
	}

	scrapeListPage(queryUrl) {
		const parser = $ => {
			const results = []
			$('.index_block .list li').each((index, element) => {
				const title = $(element).find('a').text()
				const relativeLink = $(element).find('a').attr('href')
				const originalLink = convertToAbsoluteUrl(relativeLink, this.url)
				const sourceId = this.extractIdFromUrl(originalLink)
				const publishTime = $(element).find('span').text().trim()
				if (sourceId) {
					results.push({
						title,
						originalLink,
						sourceId,
						publishTime
					})
				}
			})
			return results
		}
		return fetchAndParse(`${this.url}${queryUrl}`, parser)
	}
}

class ScraperNMGLswz {
	constructor() {
		this.url = 'https://lsj.nmg.gov.cn/scxx/dgdm/'
	}

	extractIdFromUrl(url) {
		const match = url.match(/_(\d+)\.html$/)
		if (match && match[1]) {
			return match[1]
		}
		return null
	}

	scrapeDetailPage(url) {
		const parser = $ => {
			const container = $('.box')
			const title = $(container).find('.xl_bt').text().trim()
			const author = $(container).find('#l_lypd').text().trim()
			let content = $(container).find('.trs_editor_view').html()
			return { title, author, content }
		}
		return fetchAndParse(url, parser)
	}

	scrapeListPage(queryUrl) {
		const parser = $ => {
			const results = []
			$('.yzgl_box .yzgl_list li').each((index, element) => {
				const title = $(element).find('a').text()
				const relativeLink = $(element).find('a').attr('href')
				const originalLink = convertToAbsoluteUrl(relativeLink, this.url)
				const sourceId = this.extractIdFromUrl(originalLink)
				const publishTime = $(element).find('span').text().trim()
				if (sourceId) {
					results.push({
						title,
						originalLink,
						sourceId,
						publishTime
					})
				}
			})
			return results
		}
		return fetchAndParse(`${this.url}${queryUrl}`, parser)
	}
}

module.exports = {
	ScraperAgri,
	ScraperGrainmarket,
	ScraperChinasoybean,
	ScraperChinaCotton,
	ScraperSDLscb,
	ScraperHBLswz,
	ScraperJLLswz,
	ScraperXJLswz,
	ScraperNMGLswz
}
