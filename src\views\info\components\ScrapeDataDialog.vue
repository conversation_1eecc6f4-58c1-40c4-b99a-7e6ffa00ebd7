<template>
  <el-dialog
    title="更新数据"
    width="900px"
    :visible.sync="visible"
    @close="$emit('update:visible', false)"
  >
    <div class="scrape-content">
      <div class="batch-actions">
        <el-button
          type="primary"
          icon="el-icon-refresh"
          :loading="allLoading"
          @click="handleBatchScrape"
        >
          全部更新
        </el-button>
        <el-button
          type="success"
          icon="el-icon-check"
          :disabled="selectedSources.length === 0"
          :loading="batchLoading"
          @click="handleSelectedScrape"
        >
          更新选中 ({{ selectedSources.length }})
        </el-button>
        <el-button type="info" icon="el-icon-refresh-left" @click="clearStatus">
          清除状态
        </el-button>
        <el-button
          v-if="allLoading || batchLoading"
          type="warning"
          icon="el-icon-close"
          @click="handleCancel"
        >
          取消操作
        </el-button>
      </div>

      <div class="source-list">
        <div v-for="(value, key) in sourceLabels" :key="key" class="source-item-wrapper">
          <div
            class="source-item"
            :class="{
              'is-loading': loadingStates[key],
              'is-success': statusMap[key] === 'success',
              'is-error': statusMap[key] === 'error'
            }"
          >
            <el-checkbox v-model="selectedSources" :label="key" :disabled="loadingStates[key]">
              {{ value }}
            </el-checkbox>

            <div class="source-actions">
              <el-button
                size="mini"
                type="primary"
                icon="el-icon-refresh"
                :loading="loadingStates[key]"
                @click="handleSingleScrape(key)"
              >
                更新
              </el-button>

              <i
                v-if="statusMap[key] === 'success'"
                class="el-icon-success status-icon success"
                title="更新成功"
              ></i>
              <i
                v-else-if="statusMap[key] === 'error'"
                class="el-icon-error status-icon error"
                title="更新失败"
              ></i>

              <i v-else-if="loadingStates[key]" class="el-icon-loading status-icon loading"></i>
            </div>
          </div>

          <div
            v-if="sourceProgress[key] && sourceProgress[key].show"
            class="source-progress"
            :class="sourceProgress[key].status"
          >
            <el-progress
              :percentage="sourceProgress[key].percentage"
              :status="sourceProgress[key].status"
              :show-text="false"
              :stroke-width="8"
            ></el-progress>
            <div class="progress-description">
              {{ sourceProgress[key].text }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <el-dialog title="更新结果" :visible.sync="resultDialogVisible" append-to-body width="600px">
      <div v-if="resultData" class="result-content">
        <div class="result-summary">
          <i :class="resultData.icon" :style="{ color: resultData.color }"></i>
          <span class="summary-text">{{ resultData.title }}</span>
        </div>

        <!-- 整体统计 -->
        <div class="stats-section">
          <p class="stats-line">
            📊 <strong>总计:</strong> {{ resultData.sourceText }} (成功:
            <span class="success-text">{{ resultData.successCount }}</span
            >, 失败: <span class="error-text">{{ resultData.failCount }}</span
            >)
          </p>
          <p class="stats-line">
            🔢 <strong>数据统计:</strong> 共爬取 {{ resultData.totalCrawled }} 条， 成功录入
            {{ resultData.totalSuccess }} 条， 忽略重复 {{ resultData.totalIgnored }} 条， 录入失败
            {{ resultData.totalFailed }} 条
          </p>
        </div>

        <!-- 详细结果 -->
        <div v-if="resultData.details.length > 0" class="details-section">
          <h4>详细结果</h4>
          <div class="detail-list">
            <div
              v-for="detail in resultData.details"
              :key="detail.source"
              class="detail-item"
              :class="detail.status"
            >
              <div class="detail-header">
                <span class="status-icon">{{ detail.status === 'success' ? '✅' : '❌' }}</span>
                <strong class="source-name">{{ detail.name }}</strong>
              </div>
              <div class="detail-content">
                <span v-if="detail.status === 'success'" class="success-detail">
                  爬取: {{ detail.total || 0 }} 条，录入: {{ detail.success || 0 }} 条， 忽略:
                  {{ detail.ignore || 0 }} 条，失败: {{ detail.fail || 0 }} 条
                </span>
                <span v-else class="error-detail"> 错误: {{ detail.error }} </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </el-dialog>
</template>

<script>
  export default {
    name: 'ScrapeDataDialog',
    props: {
      visible: {
        type: Boolean,
        default: false
      },
      sourceLabels: {
        type: Object,
        default: () => ({})
      }
    },
    data() {
      return {
        selectedSources: [], // 选中的数据源
        loadingStates: {}, // 各个数据源的loading状态
        statusMap: {}, // 各个数据源的状态 success/error
        sourceProgress: {}, // 各个数据源的进度状态
        allLoading: false, // 全部更新loading
        batchLoading: false, // 批量更新loading
        isCancelled: false, // 取消标识

        // 定时器管理
        progressTimers: {}, // 进度条隐藏定时器

        // 常量定义
        RESULT_DISPLAY_DELAY: 500, // 结果弹窗显示延迟
        PROGRESS_HIDE_DELAY: 3000, // 进度条隐藏延迟

        // 并发控制
        CONCURRENT_LIMIT: 3,

        // 结果显示状态
        resultDialogVisible: false,
        resultData: null
      }
    },
    watch: {
      visible(newVal) {
        if (newVal) {
          this.resetStatus()
        }
      }
    },
    beforeDestroy() {
      // 组件销毁时清理所有定时器
      this.clearAllTimers()
    },
    methods: {
      // 通用的数据源状态管理
      setSourceState(sourceKey, loading, status = '') {
        this.$set(this.loadingStates, sourceKey, loading)
        this.$set(this.statusMap, sourceKey, status)
      },

      // 通用的数据源执行包装器
      async executeSourceTask(sourceKey, isBatch = false, onSuccess = null, onError = null) {
        this.setSourceState(sourceKey, true, '')

        try {
          const result = await this.executeWithProgress(
            sourceKey,
            () => this.scrapeSourceWithRetry(sourceKey),
            isBatch
          )

          this.setSourceState(sourceKey, false, 'success')
          this.scheduleProgressHide(sourceKey)

          if (onSuccess) {
            onSuccess(result)
          }

          return result
        } catch (error) {
          this.setSourceState(sourceKey, false, 'error')
          const errorMessage = error?.message || error?.toString() || '未知错误'
          this.scheduleProgressHide(sourceKey)

          if (onError) {
            onError(errorMessage)
          }

          if (!isBatch) {
            console.error(`${this.sourceLabels[sourceKey]} 更新失败:`, error)
          }

          throw error
        }
      },
      updateSourceProgress(sourceKey, percentage, text, status = null) {
        this.$set(this.sourceProgress, sourceKey, {
          show: true,
          percentage: Math.max(0, Math.min(100, percentage)),
          text: text || '处理中...',
          status: status
        })
      },

      hideSourceProgress(sourceKey) {
        if (this.sourceProgress[sourceKey]) {
          this.$set(this.sourceProgress[sourceKey], 'show', false)
        }
      },

      // 重置单个数据源进度
      resetSourceProgress(sourceKey) {
        this.$set(this.sourceProgress, sourceKey, {
          show: false,
          percentage: 0,
          text: '',
          status: null
        })
      },

      // 进度执行器
      async executeWithProgress(sourceKey, taskFn, isBatch = false) {
        const progressSteps = [
          { percentage: 0, text: '准备开始更新...', delay: 0 },
          { percentage: 20, text: '正在连接数据源...', delay: isBatch ? 100 : 300 },
          { percentage: 40, text: '正在爬取数据...', delay: 0 },
          { percentage: 80, text: '正在处理数据...', delay: isBatch ? 50 : 200 },
          { percentage: 100, text: '更新完成', delay: 0 }
        ]

        try {
          for (let i = 0; i < 3; i++) {
            const step = progressSteps[i]
            this.updateSourceProgress(sourceKey, step.percentage, step.text)
            if (step.delay > 0) await this.delay(step.delay)
          }
          const result = await taskFn()
          for (let i = 3; i < progressSteps.length; i++) {
            const step = progressSteps[i]
            const status = i === progressSteps.length - 1 ? 'success' : null
            this.updateSourceProgress(sourceKey, step.percentage, step.text, status)
            if (step.delay > 0) await this.delay(step.delay)
          }

          return result
        } catch (error) {
          this.updateSourceProgress(sourceKey, 100, '更新失败', 'exception')
          throw error
        }
      },

      // 单个数据源更新 - 使用统一执行器
      async handleSingleScrape(sourceKey) {
        try {
          const result = await this.executeSourceTask(
            sourceKey,
            false, // 单个操作
            result => {
              // 成功回调
              const resultData = this.buildResultData(sourceKey, result, 'success')
              setTimeout(() => this.showUpdateResult(resultData, true), this.RESULT_DISPLAY_DELAY)
            },
            errorMessage => {
              // 失败回调
              const resultData = this.buildResultData(sourceKey, null, 'error', errorMessage)
              setTimeout(() => this.showUpdateResult(resultData, true), this.RESULT_DISPLAY_DELAY)
            }
          )
          return result
        } catch (error) {
          return null
        }
      },

      // 简化进度条隐藏调度
      scheduleProgressHide(sourceKey, delay = this.PROGRESS_HIDE_DELAY) {
        // 清除旧定时器
        if (this.progressTimers[sourceKey]) {
          clearTimeout(this.progressTimers[sourceKey])
        }

        this.progressTimers[sourceKey] = setTimeout(() => {
          this.hideSourceProgress(sourceKey)
          delete this.progressTimers[sourceKey]
        }, delay)
      },

      // 构建结果数据结构
      buildResultData(sourceKey, result, status, errorMessage = null) {
        const isSuccess = status === 'success'
        return {
          successCount: isSuccess ? 1 : 0,
          failCount: isSuccess ? 0 : 1,
          totalCrawled: result?.total || 0,
          totalSuccess: result?.success || 0,
          totalIgnored: result?.ignore || 0,
          totalFailed: result?.fail || 0,
          details: [
            {
              source: sourceKey,
              name: this.sourceLabels[sourceKey] || sourceKey,
              status,
              ...(isSuccess ? result : { error: errorMessage })
            }
          ]
        }
      },

      // 更新批量结果统计
      updateBatchResults(results, sourceKey, result, status, errorMessage = null) {
        const isSuccess = status === 'success'

        if (isSuccess) {
          results.successCount++
          results.totalCrawled += result?.total || 0
          results.totalSuccess += result?.success || 0
          results.totalIgnored += result?.ignore || 0
          results.totalFailed += result?.fail || 0
          results.details.push({
            source: sourceKey,
            name: this.sourceLabels[sourceKey],
            status: 'success',
            ...result
          })
        } else {
          results.failCount++
          results.details.push({
            source: sourceKey,
            name: this.sourceLabels[sourceKey],
            status: 'error',
            error: errorMessage
          })
        }
      },

      // 全部更新
      async handleBatchScrape() {
        const allSources = Object.keys(this.sourceLabels || {})
        if (allSources.length === 0) {
          this.$message.warning('没有可用的数据源')
          return
        }
        await this.executeBatchScrape(allSources, 'allLoading')
      },

      // 更新选中
      async handleSelectedScrape() {
        if (this.selectedSources.length === 0) {
          this.$message.warning('请先选择要更新的数据源')
          return
        }
        await this.executeBatchScrape(this.selectedSources, 'batchLoading')
      },

      // 执行批量更新 - 简化版本，只保留Item级进度
      async executeBatchScrape(sources, loadingKey) {
        this[loadingKey] = true
        this.isCancelled = false

        // 结果统计
        const results = {
          successCount: 0,
          failCount: 0,
          totalCrawled: 0,
          totalSuccess: 0,
          totalIgnored: 0,
          totalFailed: 0,
          details: []
        }

        // 并发控制配置
        const activeRequests = new Set()
        const queue = [...sources]

        // 执行单个任务的包装函数
        const executeTask = async sourceKey => {
          if (this.isCancelled) return null

          try {
            const result = await this.executeSourceTask(
              sourceKey,
              true, // 批量操作
              result => {
                // 批量成功回调
                this.updateBatchResults(results, sourceKey, result, 'success')
              },
              errorMessage => {
                // 批量失败回调
                this.updateBatchResults(results, sourceKey, null, 'error', errorMessage)
              }
            )
            return result
          } catch (error) {
            return null
          }
        }

        // 并发控制执行
        const processConcurrently = async () => {
          while (queue.length > 0 && !this.isCancelled) {
            while (activeRequests.size < this.CONCURRENT_LIMIT && queue.length > 0) {
              const sourceKey = queue.shift()
              const promise = executeTask(sourceKey)
              activeRequests.add(promise)

              promise.finally(() => {
                activeRequests.delete(promise)
              })
            }

            if (activeRequests.size > 0) {
              await Promise.race(activeRequests)
            }
          }

          // 等待所有请求完成
          await Promise.all(activeRequests)
        }

        await processConcurrently()

        // 显示最终结果
        this.showBatchResults(results)
        this[loadingKey] = false

        // 通知父组件重新加载数据
        if (results.successCount > 0) {
          this.$emit('refreshData')
        }
      },

      // 显示批量结果详情
      showBatchResults(results) {
        if (this.isCancelled) {
          this.$message.info('批量操作已取消')
          return
        }
        this.showUpdateResult(results, false)
      },

      showUpdateResult(results, isSingle = false) {
        const { successCount, failCount, totalCrawled, totalSuccess, totalIgnored, totalFailed } =
          results
        const isAllSuccess = failCount === 0
        const isAllFailed = successCount === 0

        // 确定标题和样式
        let title, icon, color
        if (isSingle) {
          title = isAllSuccess ? '数据源更新成功' : '数据源更新失败'
        } else {
          if (isAllSuccess) {
            title = '批量更新完成 - 全部成功'
          } else if (isAllFailed) {
            title = '批量更新失败 - 全部失败'
          } else {
            title = '批量更新完成 - 部分成功'
          }
        }

        if (isAllSuccess) {
          icon = 'el-icon-success'
          color = '#67c23a'
        } else if (isAllFailed) {
          icon = 'el-icon-error'
          color = '#f56c6c'
        } else {
          icon = 'el-icon-warning'
          color = '#e6a23c'
        }

        this.resultData = {
          title,
          icon,
          color,
          sourceText: isSingle ? '1 个数据源' : `${successCount + failCount} 个数据源`,
          successCount,
          failCount,
          totalCrawled,
          totalSuccess,
          totalIgnored,
          totalFailed,
          details: results.details || []
        }
        this.resultDialogVisible = true
      },

      // 带重试机制的爬取逻辑
      async scrapeSourceWithRetry(sourceKey, maxRetries = 3) {
        let lastError

        for (let attempt = 1; attempt <= maxRetries; attempt++) {
          try {
            const result = await this.scrapeSource(sourceKey)
            if (attempt > 1) {
              // 记录重试成功
              console.log(`${this.sourceLabels[sourceKey]} 在第 ${attempt} 次尝试后成功`)
            }
            return result
          } catch (error) {
            lastError = error

            if (attempt < maxRetries) {
              // 指数退避延迟
              const delay = Math.min(1000 * Math.pow(2, attempt - 1), 10000)
              const errorMessage = error?.message || error?.toString() || '未知错误'
              console.warn(
                `${this.sourceLabels[sourceKey]} 第 ${attempt} 次尝试失败，${delay}ms 后重试:`,
                errorMessage
              )

              await this.delay(delay)
            } else {
              console.error(
                `${this.sourceLabels[sourceKey]} 在 ${maxRetries} 次尝试后最终失败:`,
                error
              )
            }
          }
        }

        throw lastError
      },

      // 延迟工具函数
      delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms))
      },

      // 实际的爬取逻辑 - 直接调用API
      async scrapeSource(sourceKey) {
        const { default: infoManager } = await import('@/manager/infoManager')
        const result = await infoManager.scrapeInfo(sourceKey)

        // 确保返回的数据格式正确
        return {
          total: result?.total || 0,
          success: result?.success || 0,
          ignore: result?.ignore || 0,
          fail: result?.fail || 0,
          ...result
        }
      },

      async handleCancel() {
        return this.cancelBatchOperation()
      },

      async cancelBatchOperation() {
        try {
          await this.$confirm('确定要取消当前批量操作吗？已完成的数据源不会受影响。', '确认取消', {
            confirmButtonText: '确定取消',
            cancelButtonText: '继续操作',
            type: 'warning'
          })

          this.performCancellation()
        } catch {
          // 用户选择继续操作，不做任何处理
        }
      },

      performCancellation() {
        this.isCancelled = true
        this.allLoading = false
        this.batchLoading = false

        Object.keys(this.loadingStates).forEach(key => {
          if (this.loadingStates[key]) {
            this.$set(this.loadingStates, key, false)
            this.$set(this.statusMap, key, '')
            this.hideSourceProgress(key) // 隐藏对应的进度条
          }
        })

        this.clearAllTimers()
        this.$message.info('批量操作已取消')
      },

      clearStatus() {
        this.resetStatus(false)
      },

      resetStatus(includeLoading = true) {
        if (includeLoading) {
          this.loadingStates = {}
          this.allLoading = false
          this.batchLoading = false
        }

        this.statusMap = {}
        this.sourceProgress = {} // 重置所有数据源进度
        this.selectedSources = []
        this.isCancelled = false
        this.resultDialogVisible = false
        this.resultData = null

        this.clearAllTimers()
      },

      // 清理所有定时器
      clearAllTimers() {
        Object.values(this.progressTimers).forEach(timer => {
          if (timer) clearTimeout(timer)
        })
        this.progressTimers = {}
      }
    }
  }
</script>

<style scoped>
  .batch-actions {
    display: flex;
    gap: 12px;
    margin-bottom: 20px;
  }

  /* 通用按钮样式 */
  .batch-actions .el-button,
  .source-actions .el-button {
    transition: all 0.3s ease;
  }

  .batch-actions .el-button {
    flex: 1;
    max-width: 150px;
  }

  .source-actions .el-button {
    min-width: 60px;
  }

  /* 通用状态样式 */
  .source-item.is-loading,
  .source-item.is-success,
  .source-item.is-error {
    transition: all 0.3s ease;
  }

  .source-item.is-loading {
    background: #ecf5ff;
    border-color: #b3d8ff;
  }

  .source-item.is-success {
    background: #f0f9ff;
    border-color: #b3e5fc;
  }

  .source-item.is-error {
    background: #fef0f0;
    border-color: #fbc4c4;
  }

  .status-icon,
  .stat-item i {
    transition: all 0.3s ease;
  }

  .status-icon {
    font-size: 16px;
    width: 20px;
    text-align: center;
  }

  .status-icon.success,
  .stat-item.success i {
    color: #67c23a;
  }

  .status-icon.error,
  .stat-item.error i {
    color: #f56c6c;
  }

  .status-icon.loading {
    color: #409eff;
    animation: rotating 2s linear infinite;
  }

  .stat-item.info i {
    color: #409eff;
  }

  .source-list {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    background: #fafafa;
  }

  .source-item-wrapper {
    border-bottom: 1px solid #e4e7ed;
  }

  .source-item-wrapper:last-child {
    border-bottom: none;
  }

  .source-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 16px;
    background: #fff;
    transition: all 0.3s ease;
  }

  .source-item:hover {
    background: #f5f7fa;
  }

  .source-actions {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  @keyframes rotating {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  .source-item ::v-deep .el-checkbox {
    flex: 1;
    margin-right: 12px;
  }

  .source-item ::v-deep .el-checkbox__label {
    font-size: 14px;
    line-height: 1.5;
  }

  .source-progress {
    margin: 0;
    padding: 10px 16px;
    background: rgba(64, 158, 255, 0.03);
    /* border-top: 1px solid rgba(64, 158, 255, 0.1); */
  }

  .progress-description {
    margin-top: 6px;
    text-align: center;
    font-size: 12px;
    color: #666;
    animation: pulse 2s infinite;
  }

  @keyframes pulse {
    0% {
      opacity: 0.8;
    }
    50% {
      opacity: 1;
    }
    100% {
      opacity: 0.8;
    }
  }

  @media (max-width: 768px) {
    .batch-actions {
      flex-direction: column;
    }

    .batch-actions .el-button {
      max-width: none;
    }

    .source-item {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;
    }

    .source-actions {
      align-self: flex-end;
    }

    .source-progress {
      padding: 8px 12px;
    }
  }

  .source-list::-webkit-scrollbar {
    width: 6px;
  }

  .source-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  .source-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }

  .source-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }

  /* 结果对话框样式 */
  .result-content {
    margin-top: -20px;
  }

  .result-summary {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    padding: 15px;
    background: #f5f7fa;
    border-radius: 4px;
    font-size: 16px;
  }

  .result-summary i {
    font-size: 24px;
    margin-right: 10px;
  }

  .summary-text {
    font-weight: 500;
  }

  /* 统计区域 */
  .stats-section {
    margin-bottom: 10px;
  }

  .stats-section h4 {
    margin: 0 0 10px 0;
    color: #303133;
    font-size: 14px;
    font-weight: 500;
  }

  .stats-line {
    margin: 8px 0;
    font-size: 14px;
    line-height: 1.5;
  }

  .success-text {
    color: #67c23a;
    font-weight: 500;
  }

  .error-text {
    color: #f56c6c;
    font-weight: 500;
  }

  /* 详细结果区域 */
  .details-section h4 {
    color: #333;
    font-weight: 500;
  }

  .detail-list {
    max-height: 300px;
    overflow-y: auto;
    border-radius: 4px;
    background: #fafafa;
    padding: 0 10px;
  }

  .detail-item {
    margin: 8px 0;
    padding: 5px 10px;
    border-radius: 4px;
    background: white;
  }

  .detail-item.success {
    border-left: 3px solid #67c23a;
  }

  .detail-item.error {
    border-left: 3px solid #f56c6c;
  }

  .detail-header {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
  }

  .status-icon {
    margin-right: 8px;
    font-size: 14px;
  }

  .source-name {
    color: #333;
    margin-left: 0;
  }

  .detail-content {
    font-size: 12px;
    margin-left: 28px;
  }

  .success-detail {
    color: #909399;
  }

  .error-detail {
    color: #f56c6c;
  }
</style>
