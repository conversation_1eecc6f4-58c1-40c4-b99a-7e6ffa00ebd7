export default {
  methods: {
    // 根据省份代码获取业务区域名称
    getBusinessRegionNameByProvinceCode(provinceCode) {
      const businessRegions = this.$store.state.region.regionList
      const region = businessRegions.find(region => {
        if (region.provinceCodes === 'ALL') return false
        if (region.provinceCodes === 'OTHERS') return false
        const codes = region.provinceCodes.split(',')
        return codes.includes(provinceCode)
      })
      return region ? region.name : null
    },

    // 根据业务区域ID获取区域信息
    getBusinessRegionById(regionId) {
      const businessRegions = this.$store.state.region.regionList
      return businessRegions.find(region => region.id === regionId)
    },

    // 获取所有可编辑的业务区域
    getEditableBusinessRegions() {
      const businessRegions = this.$store.state.region.regionList
      return businessRegions.filter(region => region.editAble === 1)
    },

    // 获取所有业务区域（用于选择器）
    getAllBusinessRegions() {
      return this.$store.state.region.regionList
    },

    // 根据业务区域ID获取区域名称
    getBusinessRegionNameById(regionId) {
      const region = this.getBusinessRegionById(regionId)
      return region ? region.name : ''
    },

    // 检查业务区域是否包含指定省份代码
    businessRegionContainsProvince(regionId, provinceCode) {
      const region = this.getBusinessRegionById(regionId)
      if (!region) return false

      if (region.provinceCodes === 'ALL') return true
      if (region.provinceCodes === 'OTHERS') return false

      const codes = region.provinceCodes.split(',')
      return codes.includes(provinceCode)
    },

    // 获取业务区域的省份代码数组
    getBusinessRegionProvinceCodes(regionId) {
      const region = this.getBusinessRegionById(regionId)
      if (!region) return []

      if (region.provinceCodes === 'ALL' || region.provinceCodes === 'OTHERS') {
        return [region.provinceCodes]
      }

      return region.provinceCodes.split(',')
    }
  }
}
