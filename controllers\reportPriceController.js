const { REPORT_PRICE } = require('../db').tableNames;
const { resFilter, getKeysObj } = require('../utils/filter')
const dbController = require('./dbController')
const tableKeys = ['description', 'longitude', 'latitude', 'address', 'cropId']

// 新增上报价格
function createReportPrice (reqData) {
    return new Promise((resolve, reject) => {
        let newData = getKeysObj(reqData, ['userId', ...tableKeys]);
        dbController.add(REPORT_PRICE, newData, (err, res) => {
            if (err) {
                reject(err);
            } else {
                resolve('价格上报成功');
            }
        })
    })
}

// 更新上报价格
function updateReportPrice (id, reqData) {
    return new Promise((resolve, reject) => {
        let newData = getKeysObj(reqData, tableKeys);
        dbController.update(REPORT_PRICE, { id }, newData, (err, res) => {
            if (err) {
                reject(err);
            } else {
                resolve('价格信息更新成功');
            }
        })
    })
}

// 查询个人的价格信息
function queryMyReportPrice (pageIndex, pageSize, userId) {
    return queryReportPriceByCondition(pageIndex, pageSize, { userId, del: 0 });
}

// 分页查询上报的价格
function queryReportPrice (pageIndex, pageSize, cropId, longitude, latitude) {
    let condition = cropId ? { cropId, del: 0 } : { del: 0 };
    if (longitude && latitude && !isNaN(longitude) && !isNaN(latitude)) {
        //地球半径
        const EARTH_RADIUS = 6378137.0; //单位M
        const EARTH_CIRCLE = EARTH_RADIUS * Math.PI * 2;
        const maxAngle = 50 * 1000 / EARTH_CIRCLE * 360;
        const minLongitude = Math.round((longitude * 1 - maxAngle) * 100000) / 100000;
        const maxLongitude = Math.round((longitude * 1 + maxAngle) * 100000) / 100000;
        const minLatitude = Math.round((latitude * 1 - maxAngle) * 100000) / 100000;
        const maxLatitude = Math.round((latitude * 1 + maxAngle) * 100000) / 100000;
        const arr = [
            '`longitude` > ' + minLongitude,
            '`longitude` < ' + maxLongitude,
            '`latitude` > ' + minLatitude,
            '`latitude` < ' + maxLatitude
        ];
        condition._special = arr.join(' AND ');
    }
    return queryReportPriceByCondition(pageIndex, pageSize, condition);
}

// 删除特定的价格
function deleteReportPrice (id, userId) {
    return new Promise((resolve, reject) => {
        dbController.update(REPORT_PRICE, { id, userId }, { del: 1 }, (err, res) => {
            if (err) {
                reject(err);
            } else {
                if (res && res.affectedRows === 1) {
                    resolve('删除成功');
                } else {
                    reject('删除失败');
                }
            }
        })
    })
}


// 根据确定的查询条件分页查询上报价格
function queryReportPriceByCondition (pageIndex, pageSize, condition) {
    return new Promise((resolve, reject) => {
        dbController.count(REPORT_PRICE, condition, (err, rows) => {
            if (err) {
                reject(err)
            } else {
                const total = rows[0]['count(*)'];
                dbController.query(REPORT_PRICE, condition, { pageIndex, pageSize, orderBy: 'updateTime' }, (err, rows) => {
                    if (err) {
                        reject(err)
                    } else {
                        resolve({
                            total,
                            list: rows.map(item => resFilter(item))
                        })
                    }
                })
            }
        })
    })
}


module.exports = {
    createReportPrice,
    updateReportPrice,
    queryReportPrice,
    queryMyReportPrice,
    deleteReportPrice
}