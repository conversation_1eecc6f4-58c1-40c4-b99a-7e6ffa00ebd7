// 后台数据列表数据查询模块
const cropCompanyController = require('./cropCompanyController');
const cropOtherDailyController = require('./cropOtherDailyController');
const cropPriceController = require('./cropPriceController');
const logController = require('./logController');
const userController = require('./userController');

// 查询企业
function queryCropCompanies (cityCode, name, pageIndex, pageSize) {
    return new Promise(function (resolve, reject) {
        cropCompanyController.countCompanies(cityCode, name).then(total => {
            if (total > 0) {
                cropCompanyController.queryCompanies(cityCode, name, pageIndex, pageSize).then(list => {
                    resolve({
                        list,
                        total
                    })
                }).catch(err => {
                    reject(err)
                })
            } else {
                resolve({
                    list: [],
                    total: 0
                })
            }
        }).catch(err => {
            reject(err)
        })
    })
}

// 查询某一天的价格
function queryCropPrices (cropId, companyId, date, pageIndex, pageSize) {
    return new Promise(function (resolve, reject) {
        cropPriceController.countPrices(cropId, companyId, date).then(total => {
            if (total > 0) {
                cropPriceController.queryPrices(cropId, companyId, date, pageIndex, pageSize).then(list => {
                    resolve({
                        list,
                        total
                    })
                }).catch(err => {
                    reject(err)
                })
            } else {
                resolve({
                    list: [],
                    total: 0
                })
            }
        }).catch(err => {
            reject(err)
        })
    })
}

// 查询每日港口信息等
function queryCropOthers (date, pageIndex, pageSize) {
    return new Promise(function (resolve, reject) {
        cropOtherDailyController.countDaily().then(total => {
            if (total > 0) {
                cropOtherDailyController.queryDaily(date, pageIndex, pageSize).then(list => {
                    resolve({
                        list,
                        total
                    })
                }).catch(err => {
                    reject(err)
                })
            } else {
                resolve({
                    list: [],
                    total: 0
                })
            }
        }).catch(err => {
            reject(err)
        })
    })
}

// 查询日志
function queryLogs (key, userId, buyerId, orderId, pageIndex, pageSize) {
    return new Promise(function (resolve, reject) {
        logController.countLogs(key, userId, buyerId, orderId).then(total => {
            if (total > 0) {
                logController.queryLogs(key, userId, buyerId, orderId, pageIndex, pageSize).then(list => {
                    resolve({
                        list,
                        total
                    })
                }).catch(err => {
                    reject(err)
                })
            } else {
                resolve({
                    list: [],
                    total: 0
                })
            }
        }).catch(err => {
            reject(err)
        })
    })
}

module.exports = {
    queryCropCompanies,
    queryCropPrices,
    queryCropOthers,
    queryLogs,
}