const express = require('express')
const router = express.Router()
const areaController = require('../controllers/areaController')

// 获取所有省份
router.get('/provinces', async function (req, res) {
	try {
		const provinces = await areaController.getProvinces()
		res.sendSuccess(provinces)
	} catch (error) {
		console.error('获取省份列表失败:', error)
		res.sendMessage('获取省份列表失败')
	}
})

// 根据省份获取城市列表
router.get('/cities', async function (req, res) {
	try {
		const { provinceCode } = req.query
		if (!provinceCode) {
			return res.sendMessage('省份代码不能为空')
		}

		const cities = await areaController.getCitiesByProvince(provinceCode)
		res.sendSuccess(cities)
	} catch (error) {
		console.error('获取城市列表失败:', error)
		res.sendMessage('获取城市列表失败')
	}
})

// 根据城市获取区县列表
router.get('/areas', async function (req, res) {
	try {
		const { cityCode } = req.query
		if (!cityCode) {
			return res.sendMessage('城市代码不能为空')
		}

		const areas = await areaController.getCountiesByCity(cityCode)
		res.sendSuccess(areas)
	} catch (error) {
		console.error('获取区县列表失败:', error)
		res.sendMessage('获取区县列表失败')
	}
})

// 根据区县获取街道列表
router.get('/streets', async function (req, res) {
	try {
		const { countyCode } = req.query
		if (!countyCode) {
			return res.sendMessage('区县代码不能为空')
		}

		const streets = await areaController.getStreetsByCounty(countyCode)
		res.sendSuccess(streets)
	} catch (error) {
		console.error('获取街道列表失败:', error)
		res.sendMessage('获取街道列表失败')
	}
})

// 按需加载全部城市数据
router.get('/loadAllCities', async function (req, res) {
	try {
		// 返回所有级别 <= 3 的行政区划数据
		const allAreas = await areaController.queryAllAreas({ level: null, limit: 10000 })

		res.set('Cache-Control', 'public, max-age=3600') // 缓存1小时
		res.sendSuccess(allAreas)
	} catch (error) {
		console.error('加载所有城市数据失败:', error)
		res.sendMessage('加载所有城市数据失败')
	}
})

module.exports = router
