<template>
  <div class="area-management">
    <div class="action-panel">
      <el-card class="box-card">
        <div class="upload-section">
          <h4>上传行政区划数据文件</h4>
          <el-upload
            class="upload-demo"
            :action="uploadUrl"
            :before-upload="beforeUpload"
            :on-success="handleUploadSuccess"
            :on-error="handleUploadError"
            :show-file-list="false"
            accept=".json"
          >
            <el-button slot="trigger" size="small" type="primary" :loading="uploading">
              选择文件
            </el-button>
            <div slot="tip" class="el-upload__tip">
              只能上传 json 文件，上传后将自动处理并显示结果
            </div>
          </el-upload>
        </div>
      </el-card>
    </div>

    <div class="search-panel">
      <el-card>
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="级别">
            <el-select v-model="searchForm.level" placeholder="请选择级别" clearable>
              <el-option label="全部" value=""></el-option>
              <el-option label="省级" :value="1"></el-option>
              <el-option label="市级" :value="2"></el-option>
              <el-option label="区县级" :value="3"></el-option>
              <el-option label="街道级" :value="4"></el-option>
              <el-option label="村级" :value="5"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="关键词">
            <el-input v-model="searchForm.keyword" placeholder="输入名称或代码搜索" clearable />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <div class="table-panel">
      <el-card>
        <div class="table-toolbar">
          <div class="toolbar-left">
            <span>共 {{ total }} 条记录</span>
          </div>
          <div class="toolbar-right">
            <el-button-group>
              <el-button
                :type="viewMode === 'table' ? 'primary' : 'default'"
                icon="el-icon-menu"
                @click="viewMode = 'table'"
              >
                表格视图
              </el-button>
              <el-button
                :type="viewMode === 'tree' ? 'primary' : 'default'"
                icon="el-icon-s-data"
                @click="viewMode = 'tree'"
              >
                树形视图
              </el-button>
            </el-button-group>
          </div>
        </div>

        <el-table
          v-if="viewMode === 'table'"
          v-loading="loading"
          :data="tableData"
          stripe
          style="width: 100%"
        >
          <el-table-column prop="code" label="行政代码" width="160" />
          <el-table-column prop="name" label="名称" min-width="150" />
          <el-table-column prop="level" label="级别" width="160">
            <template slot-scope="scope">
              <el-tag :type="getLevelTagType(scope.row.level)">
                {{ getLevelText(scope.row.level) }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>

        <div v-if="viewMode === 'tree'" class="tree-view">
          <el-tree
            :data="treeData"
            :props="treeProps"
            :load="loadNode"
            lazy
            node-key="code"
            :expand-on-click-node="false"
            class="area-tree"
          >
            <span slot-scope="{ data }" class="tree-node">
              <span class="node-label">{{ data.name }}</span>
              <span class="node-code">({{ data.code }})</span>
              <el-tag size="mini" :type="getLevelTagType(data.level)">
                {{ getLevelText(data.level) }}
              </el-tag>
            </span>
          </el-tree>
        </div>

        <div v-if="viewMode === 'table'" class="pagination-container">
          <Pagination
            v-if="total > 0"
            :total="total"
            :pageIndex.sync="pageIndex"
            :pageSize.sync="pageSize"
            @pagination="loadData"
          />
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
  import areaManager from '@/manager/areaManager'
  import tableMixin from '@/mixins/tableMixin'
  import Pagination from '@/components/Pagination'

  export default {
    name: 'AreaList',
    components: {
      Pagination
    },
    mixins: [tableMixin],

    data() {
      return {
        uploading: false,
        uploadUrl: '/api/v1/common/upload/unified?type=areaData',

        searchForm: {
          level: '', // 默认显示所有级别
          keyword: ''
        },

        viewMode: 'table', // 'table' | 'tree'

        treeData: [],
        treeProps: {
          label: 'name',
          children: 'children',
          isLeaf: 'leaf'
        },

        levelDefinitions: {
          1: '省、直辖市、自治区',
          2: '地级市',
          3: '区县',
          4: '街道乡镇',
          5: '村级'
        }
      }
    },

    async mounted() {},

    methods: {
      async loadData() {
        this.loading = true
        try {
          const params = {
            pageIndex: this.pageIndex,
            pageSize: this.pageSize,
            level: this.searchForm.level || undefined,
            keyword: this.searchForm.keyword || undefined
          }

          const result = await areaManager.getAreaList(params)

          this.tableData = result.list || []
          this.total = result.total || 0
        } catch (error) {
          console.error('加载数据失败:', error)
          this.$message.error('加载数据失败: ' + error.message)
        } finally {
          this.loading = false
        }
      },

      async loadNode(node, resolve) {
        try {
          if (node.level === 0) {
            // 加载省级数据 - 树形视图不需要分页
            const provinces = await areaManager.getProvinces()
            resolve(
              provinces.map(item => ({
                ...item,
                leaf: false
              }))
            )
          } else {
            // 根据当前节点层级加载对应的下级数据
            let children = []
            const currentLevel = node.data.level

            if (currentLevel === 1) {
              // 省级节点，加载城市
              children = await areaManager.getCitiesByProvince(node.data.code)
            } else if (currentLevel === 2) {
              // 市级节点，加载区县
              children = await areaManager.getCountiesByCity(node.data.code)
            } else if (currentLevel === 3) {
              // 区县级节点，加载街道
              children = await areaManager.getStreetsByCounty(node.data.code)
            } else if (currentLevel === 4) {
              // 街道级节点，加载村级
              children = await areaManager.getChildrenByParentCode(node.data.code, 5)
            } else {
              // 村级及以下无子节点
              children = []
            }

            resolve(
              children.map(item => ({
                ...item,
                leaf: item.level >= 5 || children.length === 0 // 村级或无子节点为叶子节点
              }))
            )
          }
        } catch (error) {
          console.error('加载节点失败:', error)
          this.$message.error('加载节点失败: ' + error.message)
          resolve([])
        }
      },

      handleSearch() {
        this.pageIndex = 0
        this.loadData()
      },

      resetSearch() {
        this.searchForm = {
          level: '',
          keyword: ''
        }
        this.pageIndex = 0
        this.loadData()
      },

      // 获取级别标签类型
      getLevelTagType(level) {
        const types = {
          1: 'primary',
          2: 'success',
          3: 'warning',
          4: 'info',
          5: 'danger'
        }
        return types[level] || ''
      },

      // 获取级别文本
      getLevelText(level) {
        return this.levelDefinitions[level] || `级别${level}`
      },

      // 格式化日期
      formatDate(dateStr) {
        if (!dateStr) return '-'
        return new Date(dateStr).toLocaleString()
      },

      // 上传文件处理
      beforeUpload(file) {
        const isJSON = file.type === 'application/json' || file.name.endsWith('.json')
        const isLt30M = file.size / 1024 / 1024 < 30

        if (!isJSON) {
          this.$message.error('只能上传 JSON 文件!')
          return false
        }
        if (!isLt30M) {
          this.$message.error('上传文件大小不能超过 30MB!')
          return false
        }

        this.uploading = true
        return true
      },

      async handleUploadSuccess(response, _file) {
        if (response.code === 1) {
          const data = response.data || {}
          const result = await areaManager.processUploadedAreaData(data)
          let successMessage = result.message

          this.$message({
            message: successMessage,
            type: 'success',
            duration: 6000,
            showClose: true
          })

          this.loadData()
          this.uploading = false
        } else {
          this.$message.error(response.message || '文件上传失败!')
          this.uploading = false
        }
      },

      handleUploadError(error, _file) {
        this.uploading = false
        console.error('文件上传失败:', error)
        this.$message.error('文件上传失败: ' + (error.message || '未知错误'))
      }
    }
  }
</script>

<style scoped>
  .area-management {
    padding: 20px;
  }

  .action-panel,
  .search-panel,
  .table-panel {
    margin-bottom: 20px;
  }

  .upload-section h4 {
    margin-bottom: 15px;
    font-size: 14px;
    color: #333;
  }

  .search-form {
    margin: 0;
  }

  .table-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }

  .toolbar-left span {
    color: #666;
    font-size: 14px;
  }

  .tree-view {
    max-height: 600px;
    overflow-y: auto;
  }

  .tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 8px;
  }

  .node-label {
    font-weight: 500;
  }

  .node-code {
    color: #666;
    font-size: 12px;
    margin: 0 8px;
  }

  .area-tree .el-tree-node__content {
    height: 36px;
  }

  .dialog-footer {
    text-align: right;
  }
</style>
