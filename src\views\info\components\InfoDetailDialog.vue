<template>
  <el-dialog
    :title="infoDetail.title"
    top="5vh"
    width="80%"
    :visible="visible"
    @close="$emit('update:visible', false)"
  >
    <div v-loading="loadingInfoDetail" class="news-detail">
      <div class="news-header">
        <h3 class="news-title">{{ infoDetail.title || '--' }}</h3>
        <div class="news-meta">
          <div class="news-source">来源：{{ sourceLabels[infoDetail.source] }}</div>
          <div class="news-source">作者：{{ infoDetail.author || '--' }}</div>
          <div class="news-date">
            发布时间：{{ new Date(infoDetail.publishTime).format('yyyy-MM-dd') }}
          </div>
          <div class="news-source">
            原文链接：
            <a :href="infoDetail.originalLink" target="_blank" rel="noopener noreferrer">
              {{ infoDetail.originalLink || '--' }}
            </a>
          </div>
        </div>
      </div>
      <div v-if="infoDetail.photos" class="news-content">
        <img
          v-for="photo in infoDetail.photos.split(',')"
          :key="photo"
          :src="photo"
          class="info_photo"
          alt="qwc"
        />
      </div>
      <div v-if="infoDetail.content" class="news-content" v-html="infoDetail.content"></div>
    </div>
  </el-dialog>
</template>

<script>
	export default {
		name: 'InfoDetailDialog',
		props: {
			visible: {
				type: Boolean,
				default: false
			},
			infoDetail: {
				type: Object,
				default: () => ({})
			},
			sourceLabels: {
				type: Object,
				default: () => ({})
			},
			loadingInfoDetail: {
				type: Boolean,
				default: false
			}
		}
	}
</script>

<style scoped>
	::v-deep .el-dialog__body {
		max-height: calc(90vh - 120px);
		/* min-height: 300px; */
		overflow: auto;
	}

	.news-header {
		margin-bottom: 20px;
	}

	.news-title {
		font-size: 18px;
		font-weight: 700;
		margin-bottom: 10px;
		word-break: break-all;
	}

	.news-meta {
		font-size: 14px;
		color: #666;
		line-height: 1.6;
	}

	.news-date {
		margin-right: 10px;
	}

	.news-content {
		overflow: auto;
		/* font-size: 14px;
  line-height: 1.5; */
	}
	.info_photo {
		width: 100%;
	}
</style>
