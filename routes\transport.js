const express = require('express')
const router = express.Router()

const transportController = require('../controllers/transportController')

router.get('/list', function (req, res) {
	transportController
		.queryList(req.query)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

router.get('/stats', function (req, res) {
	transportController
		.getTransportStats()
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

router.get('/daily-summary', function (req, res) {
	transportController
		.getTransportDailySummary(req.query)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

module.exports = router
