const state = {
	cropArr: [],
	cropMap: {}
}

const mutations = {
	SET_CROP: (state, crops) => {
		let cropMap = {}
		crops.forEach(item => {
			cropMap[item.id] = item
		})
		state.cropArr = crops
		state.cropMap = cropMap
	}
}

const actions = {
	async getCropMap({ commit }) {
		const { default: cropManager } = await import('@/manager/cropManager')
		const res = await cropManager.queryCrops()
		commit('SET_CROP', res)
	}
}

export default {
	namespaced: true,
	state,
	mutations,
	actions
}
