const axios = require('axios')

// https://developers.weixin.qq.com/miniprogram/dev/api-backend/open-api/login/auth.code2Session.html
// 根据小程序的授权码获取微信的信息
function getQwcWxMiniSessionData (code) {
    const url = `https://api.weixin.qq.com/sns/jscode2session?appid=${process.env.APPID_MP_WEIXIN}&secret=${process.env.SECRET_MP_WEIXIN}&js_code=${code}&grant_type=authorization_code`;
    return new Promise((resolve, reject) => {
        axios.get(url).then(res => {
            if (res.status === 200) {
                const { session_key: sessionKey, openid: wxMiniOpenId, unionid: wxUnionId } = res.data;
                resolve({ sessionKey, wxMiniOpenId, wxUnionId })
                resolve(res.data)
            } else {
                reject(res.data.errmsg)
            }
        }).catch(err => {
            console.log('微信请求异常', err)
            reject(err)
        })
    })
}

// https://developers.weixin.qq.com/miniprogram/dev/api-backend/open-api/login/auth.code2Session.html
// 根据公众号授权码获取微信的信息
function getQwcWxOfficalSessionData (code) {
    const url = `https://api.weixin.qq.com/sns/oauth2/access_token?appid=${process.env.APPID_OFFICAL_ACCOUNT_QWC}&secret=${process.env.SECRET_OFFICAL_ACCOUNT_QWC}&code=${code}&grant_type=authorization_code`
    return new Promise((resolve, reject) => {
        axios.get(url).then(res => {
            if (res.status === 200) {
                const { access_token: accessToken, openid: wxOfficialOpenId, unionid: wxUnionId } = res.data;
                resolve({ accessToken, wxOfficialOpenId, wxUnionId })
            } else {
                reject('微信请求异常')
            }
        }).catch(err => {
            reject(err)
        })
    })
}

// https://developer.open-douyin.com/docs/resource/zh-CN/mini-app/develop/server/log-in/code-2-session
function getDyMiniSessionData (code) {
    const url = 'https://developer.toutiao.com/api/apps/v2/jscode2session'
    const data = {
        appid: process.env.APPID_MP_TOUTIAO,
        secret: process.env.SECRET_MP_TOUTIAO,
        anonymous_code: '',
        code
    };
    return new Promise((resolve, reject) => {
        axios.post(url, data).then(res => {
            if (res.status === 200) {
                const { session_key: sessionKey, openid: dyMiniOpenId, unionid: dyUnionId } = res.data;
                resolve({ sessionKey, dyMiniOpenId, dyUnionId })
            } else {
                reject('抖音请求异常')
            }
        }).catch(err => {
            reject(err)
        })
    })
}

// https://mp.kuaishou.com/docs/develop/server/code2Session.html
function getKsMiniSessionData (code) {
    const url = `https://open.kuaishou.com/oauth2/mp/code2session?app_id=${process.env.APPID_MP_KUAISHOU}&js_code=${code}&app_secret=${process.env.SECRET_MP_KUAISHOU}`
    return new Promise((resolve, reject) => {
        axios.post(url).then(res => {
            if (res.status === 200) {
                const { session_key: sessionKey, open_id: ksMiniOpenId, union_id: ksUnionId } = res.data;
                resolve({ sessionKey, ksMiniOpenId, ksUnionId })
            } else {
                reject('快手请求异常')
            }
        }).catch(err => {
            reject(err)
        })
    })
}



module.exports = {
    getQwcWxMiniSessionData,
    getQwcWxOfficalSessionData,
    getDyMiniSessionData,
    getKsMiniSessionData,
};