<template>
  <div class="search_wrapper">
    <el-input
      v-model="searchParams.title"
      placeholder="请输入标题"
      clearable
      style="width: 200px"
    ></el-input>
    <el-select
      v-model="searchParams.cropId"
      clearable
      placeholder="请选择作物"
      style="width: 200px"
      @change="$emit('loadData')"
    >
      <el-option
        v-for="crop in cropMaps"
        :key="crop.id"
        :label="crop.name"
        :value="crop.id" />
    </el-select>
    <el-select
      v-model="searchParams.source"
      multiple
      clearable
      collapse-tags
      placeholder="请选择来源"
      @change="$emit('loadData')"
    >
      <el-option
        v-for="(value, key) in sourceLabels"
        :key="key"
        :label="value"
        :value="key" />
    </el-select>
    <el-button type="primary" @click="$emit('loadData')">查询</el-button>
    <el-button @click="$emit('clear')">清空</el-button>
    <div style="margin-left: auto">
      <el-button
        type="primary"
        icon="el-icon-refresh"
        style="width: auto"
        @click="$emit('showScrapeDialog')"
      >更新数据</el-button
      >
    </div>
  </div>
</template>

<script>
	export default {
		name: 'SearchForm',
		props: {
			searchParams: {
				type: Object,
				default: () => ({})
			},
			cropMaps: {
				type: Array,
				default: () => []
			},
			sourceLabels: {
				type: Object,
				default: () => ({})
			}
		}
	}
</script>

<style scoped>
	.search_wrapper {
		display: flex;
		align-items: center;
	}
	.search_wrapper >>> .el-input {
		display: inline-block;
		max-width: 300px;
	}
</style>
