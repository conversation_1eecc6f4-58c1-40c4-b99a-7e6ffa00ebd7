export function AddDateFormatProto() {
    Date.prototype.format = function(fmt) { // author: meizz
        var o = {
            "M+": this.getMonth() + 1, // 月份
            "d+": this.getDate(), // 日
            "h+": this.getHours(), // 小时
            "m+": this.getMinutes(), // 分
            "s+": this.getSeconds(), // 秒
            "q+": Math.floor((this.getMonth() + 3) / 3), // 季度
            "S": this.getMilliseconds()
            // 毫秒
        };
        if (/(y+)/.test(fmt))
            fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
        for (var k in o)
            if (new RegExp("(" + k + ")").test(fmt))
                fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
        return fmt;
    }
}

//将文字描述时长转换成毫秒时长
//20s -> 20秒
//12m -> 12分钟
//12h -> 12小时
//30d -> 30天
export function unicodeTimeString(str) {
    const number = parseInt(str.replace(/[^0-9]/ig, ''));
    const type = str.replace(/[0-9]/ig, '');
    const map = {
        s: 1000,
        m: 1000 * 60,
        h: 1000 * 60 * 60,
        d: 1000 * 60 * 60 * 24
    }
    if(map[type]){
        return map[type] * number;
    }else{
        return 0;
    }
}