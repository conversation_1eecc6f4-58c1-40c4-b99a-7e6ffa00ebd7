const { INFO_SOURCE } = require('../../constant/enum')

/**
 * 爬虫策略工厂类
 * 负责创建和管理各种爬虫策略实例
 */
class ScraperStrategyFactory {
	constructor() {
		this.strategies = new Map()
		this.configs = new Map()
		this.initializeConfigs()
	}

	/**
	 * 初始化各数据源的配置 - 从外部文件加载
	 */
	initializeConfigs() {
		try {
			// 从外部配置文件加载
			const externalConfigs = require('./configs/sources')

			for (const [sourceId, config] of Object.entries(externalConfigs)) {
				this.configs.set(parseInt(sourceId), config)
			}

			console.log(`✅ 已从外部文件加载 ${this.configs.size} 个数据源配置`)
		} catch (error) {
			console.error('❌ 加载外部配置失败，请检查 configs/sources.js:', error.message)
			throw error
		}
	}

	/**
	 * 注册策略
	 * @param {number} sourceId - 数据源ID
	 * @param {ScraperStrategy} strategy - 策略实例
	 */
	registerStrategy(sourceId, strategy) {
		this.strategies.set(sourceId, strategy)
	}

	/**
	 * 获取策略实例
	 * @param {number} sourceId - 数据源ID
	 * @returns {ScraperStrategy} - 策略实例
	 */
	getStrategy(sourceId) {
		if (!this.strategies.has(sourceId)) {
			this.createStrategy(sourceId)
		}
		return this.strategies.get(sourceId)
	}

	/**
	 * 创建策略实例
	 * @param {number} sourceId - 数据源ID
	 */
	createStrategy(sourceId) {
		const config = this.configs.get(sourceId)
		if (!config) {
			throw new Error(`Unsupported source ID: ${sourceId}`)
		}

		let StrategyClass

		// 根据数据源ID动态加载对应的策略类
		switch (sourceId) {
			case INFO_SOURCE.ChineseAgriculturalSupplyAndDemandEstimates.id:
				StrategyClass = require('./strategies/AgriStrategy')
				break
			case INFO_SOURCE.NationalGrainTradeCenter.id:
				StrategyClass = require('./strategies/GrainmarketStrategy')
				break
			case INFO_SOURCE.ChinaSoybean.id:
				StrategyClass = require('./strategies/ChinaSoybeanStrategy')
				break
			case INFO_SOURCE.ChinaCotton.id:
			case INFO_SOURCE.HBLswz.id: // 河北省使用复杂策略支持多栏目
			case INFO_SOURCE.NMGLswz.id: // 内蒙古使用复杂策略支持多农产品
				StrategyClass = require('./strategies/ChinaCottonStrategy')
				break
			case INFO_SOURCE.ChinaFeedAnalysis.id:
			case INFO_SOURCE.ChinaCropIndex.id: // 中国农作物行情网使用省级策略
				StrategyClass = require('./strategies/ProvinceStrategy')
				break
			case INFO_SOURCE.SDLscb.id:
			case INFO_SOURCE.JLLswz.id:
			case INFO_SOURCE.XJLswz.id:
				StrategyClass = require('./strategies/ProvinceStrategy')
				break
			default:
				throw new Error(`No strategy class found for source ID: ${sourceId}`)
		}

		const strategy = new StrategyClass(config)
		this.registerStrategy(sourceId, strategy)
	}

	/**
	 * 获取所有支持的数据源
	 * @returns {Array} - 数据源列表
	 */
	getSupportedSources() {
		return Array.from(this.configs.keys()).map(sourceId => ({
			id: sourceId,
			name: this.configs.get(sourceId).name
		}))
	}

	/**
	 * 获取数据源配置
	 * @param {number} sourceId - 数据源ID
	 * @returns {Object} - 配置对象
	 */
	getSourceConfig(sourceId) {
		return this.configs.get(sourceId)
	}
}

// 单例模式
const factory = new ScraperStrategyFactory()

module.exports = factory
