const ORDER_STATUS = {
	default: 0, // 求购中
	finish: 1, // 已交易
	stop: 2, // 已暂停
	cancel: 3 // 已取消
}

const ORDER_INFO_STATUS = {
	default: 0, // 默认状态
	ask: 1, // 请求测量
	finish: 2 // 已上传测量数据
}

const ORDER_RELATION_TYPE = {
	like: 0, // 收藏
	ask: 1, // 请求测量
	telephone: 2, // 打电话
	message: 3, // 发信息留言
	view: 4 // 浏览
}

const USER_TYPE = {
	farmer: 0, // 普通用户
	buyer: 1, // 收购商
	grainDepot: 2, // 粮庄
	equipmentSupplier: 3, // 设备商
	collector: 10, // 信息收集员
	admin: 100, // 管理员
	operator: 101 // 运营人员
}

// 给 t_admin 表服务
const USER_ROLE = {
	superAdmin: 0,
	admin: 1,
	operator: 2, // 运营人员
	collector: 3 // 信息收集员
}

// 各平台渠道
const PLATFORM = {
	weixin: 'mp-weixin',
	douyin: 'mp-toutiao',
	kuaishou: 'mp-kuaishou'
}

const INFO_SOURCE = {
	ChineseAgriculturalSupplyAndDemandEstimates: { id: 1, name: '中国农产品供需形势分析' },
	NationalGrainTradeCenter: { id: 2, name: '国家粮食交易中心' },
	ChinaSoybean: { id: 3, name: '中国大豆产业协会' },
	ChinaCotton: { id: 4, name: '中国棉花协会' },
	SDLscb: { id: 5, name: '山东省粮食和物资储备局' },
	HBLswz: { id: 6, name: '河北省粮食和物资储备局' }, // 已合并通知公告栏目
	JLLswz: { id: 7, name: '吉林省粮食和物资储备局' },
	XJLswz: { id: 8, name: '新疆维吾尔自治区粮食和物资储备局' },
	NMGLswz: { id: 9, name: '内蒙古自治区粮食和物资储备局' },
	// HBLswzNotice: { id: 10, name: '河北省粮食和物资储备局通知公告' }, // 已合并到HBLswz
	ChinaFeedAnalysis: { id: 11, name: '中国饲料行业信息网行业分析' },
	ChinaCropIndex: { id: 12, name: '中国农作物行情网指数分析' }
}

function valueToEnumKey(enumObj, value) {
	for (const key in enumObj) {
		if (enumObj[key] === value) {
			return key
		}
	}
	return null
}

module.exports = {
	valueToEnumKey,
	ORDER_STATUS,
	ORDER_INFO_STATUS,
	ORDER_RELATION_TYPE,
	USER_TYPE,
	USER_ROLE,
	PLATFORM,
	INFO_SOURCE
}
