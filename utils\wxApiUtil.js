const fs = require('fs');
const path = require('path');
const request = require('request');
const md5 = require('md5');
const redisClient = require('./redisClientUtil');
const wxSecret = require('../wxSecret');
const WXBizDataCrypt = require('./WXBizDataCrypt');
const { getUuid } = require('./common');
const xmlParseString = require('xml2js').parseString

function resolvePath (dir) {
    return path.join(__dirname, dir);
}

function fetchAccessToken (appId) {
    return new Promise((resolve, reject) => {
        redisClient.get(`ACCESS_TOKEN_${appId}`).then(result => {
            resolve(result);
        }).catch(() => {
            requestAccessToken(appId).then(result => {
                resolve(result);
            }).catch(err => {
                reject(err)
            })
        })
    })
}

function requestAccessToken (appId) {
    const secret = wxSecret[appId];
    const url = `https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=${appId}&secret=${secret}`;
    return new Promise((resolve, reject) => {
        request({ url }, function (error, response, body) {
            if (!error && response.statusCode === 200) {
                body = JSON.parse(body);
                let accessToken = body.access_token;
                redisClient.set(`ACCESS_TOKEN_${appId}`, accessToken);
                resolve(accessToken);
            } else {
                reject(error)
            }
        })
    })
}

// https://developers.weixin.qq.com/miniprogram/dev/api-backend/open-api/login/auth.code2Session.html
function requestSessionData (appId, code) {
    const secret = wxSecret[appId];
    const url = `https://api.weixin.qq.com/sns/jscode2session?appid=${appId}&secret=${secret}&js_code=${code}&grant_type=authorization_code`;
    return new Promise((resolve, reject) => {
        request({ url: url }, function (error, response, body) {
            if (!error && response.statusCode === 200) {
                const sessionData = JSON.parse(body);
                // openid	string	用户唯一标识
                // session_key	string	会话密钥
                // unionid	string	用户在开放平台的唯一标识符，在满足 UnionID 下发条件的情况下会返回，详见 UnionID 机制说明。
                // errcode	number	错误码
                // errmsg	string	错误信息
                if (sessionData.openid && sessionData.session_key) {
                    resolve(sessionData)
                } else {
                    reject(errmsg)
                }
            } else {
                reject(error)
            }
        })
    })
}

/**
 *  获取微信敏感数据
 *
 * @param {string} appId
 * @param {string} code
 * @param {string} encryptedData // 微信端授权获取到的加密数据包
 * @param {string} iv // 加密算法的初始向量
 * @returns
 */
function requestWxEncryptedData (appId, code, encryptedData, iv) {
    return requestSessionData(appId, code).then(sessionData => {
        const { openid, session_key, unionid } = sessionData;
        if (encryptedData, iv) {
            return decryptWxData(appId, session_key, encryptedData, iv);
        } else {
            return { openId: openid, unionId: unionid };
        }
    })
}
/**
 *
 *
 * @param {string} code 微信订单号
 * @param {string} openid 
 * @param {number} amount 金额 单位分
 * @param {string} desc 订单描述
 * @returns
 */
function createCompanyPayment (code, openid, amount, desc) {
    const url = `https://api.mch.weixin.qq.com/mmpaymkttransfers/promotion/transfers`;
    const { merchantId, merchantNumber } = wxSecret;
    const merchantSecret = wxSecret[merchantId];
    let body = {
        mch_appid: merchantId,
        mchid: merchantNumber,
        nonce_str: getUuid(20),
        partner_trade_no: code,
        openid,
        amount,
        desc,
        check_name: 'NO_CHECK',
        spbill_create_ip: '**************'
    }
    let signString = '';
    let xmlString = '<xml>';
    for (const key in body) {
        const value = body[key];
        signString += `&${key}=${value}`
        xmlString += `<${key}>${value}</${key}>`
    }
    signString = signString.substr(1) + `&key=${merchantSecret}`;
    body.sign = md5(signString).toUpperCase();
    xmlString += `<sign>${body.sign}</sign>`;
    xmlString += '</xml>';
    return new Promise((resolve, reject) => {
        let key = fs.readFileSync(resolvePath('../1542368031_20191216/apiclient_key.pem'));
        let cert = fs.readFileSync(resolvePath('../1542368031_20191216/apiclient_cert.pem'));
        request({
            url: url,
            method: 'POST',
            headers: { 'content-type': 'text/xml; charset=utf-8' },
            key,
            cert,
            body: xmlString
        }, function (error, response, body) {
            if (!error && response.statusCode === 200) {
                xmlParseString(body, function (err, result) {
                    if (err) {
                        reject(err)
                    } else {
                        const { return_code, return_msg, payment_no } = result;
                        if (return_code === 'SUCCESS' && payment_no) {
                            resolve(payment_no)
                        } else {
                            reject(return_msg || '付款失败')
                        }
                    }
                });
            } else {
                reject(error)
            }
        })
    })
}


// 解密微信数据包
function decryptWxData (appId, sessionKey, encryptedData, iv) {
    const pc = new WXBizDataCrypt(appId, sessionKey);
    const decodeData = pc.decryptData(encryptedData, iv)
    return decodeData;
}



module.exports = {
    fetchAccessToken,
    requestAccessToken,
    requestSessionKey,
    requestWxEncryptedData,
    createCompanyPayment
};