const { QRCODE, USER } = require('../db').tableNames
const { resFilter, getKeysObj } = require('../utils/filter')
const dbController = require('./dbController')

const qrcodeKeys = [
	'userId',
	'userName',
	'userMobile',
	'userType',
	'qrCodeUrl',
	'description',
	'isActive'
]

/**
 * 查询二维码列表
 */
async function queryQrcodes(userName, userMobile, isActive, pageIndex, pageSize) {
	const options = {}
	if (userName) options['userName.like'] = userName
	if (userMobile) options['userMobile.like'] = userMobile
	if (isActive !== null && isActive !== undefined) options.isActive = isActive

	try {
		const totalResult = await dbController.count(QRCODE, options)
		const total = totalResult[0]['count(*)']

		if (total === 0) {
			return { total: 0, list: [] }
		}

		const limitOptions = {
			pageIndex,
			pageSize,
			orderBy: 'createTime',
			orderSort: 'DESC'
		}

		const list = await dbController.query(QRCODE, options, limitOptions)
		return { total, list: list.map(item => resFilter(item)) }
	} catch (error) {
		throw error
	}
}

/**
 * 创建新的二维码
 */
async function createQrcode(qrcodeData) {
	const insertData = getKeysObj(qrcodeData, qrcodeKeys)

	try {
		if (qrcodeData.userId) {
			const userInfo = await getUserInfo(qrcodeData.userId)
			Object.assign(insertData, {
				userName: userInfo.name,
				userMobile: userInfo.mobile,
				userType: userInfo.type
			})
		}

		await dbController.add(QRCODE, insertData)
		return '二维码创建成功'
	} catch (error) {
		throw error
	}
}

/**
 * 更新二维码信息
 */
async function updateQrcode(id, updateData) {
	const updateObj = getKeysObj(updateData, qrcodeKeys)
	try {
		await dbController.update(QRCODE, { id }, updateObj)
		return '二维码更新成功'
	} catch (error) {
		throw error
	}
}

/**
 * 删除二维码
 */
async function deleteQrcode(id) {
	try {
		await dbController.delete(QRCODE, { id })
		return '二维码删除成功'
	} catch (error) {
		throw error
	}
}

/**
 * 获取二维码详情
 */
async function getQrcodeDetail(options) {
	try {
		let result
		if (options.id) {
			const detail = await dbController.queryById(QRCODE, options.id)
			result = resFilter(detail)
		} else if (options.userId) {
			const results = await dbController.query(QRCODE, {
				userId: options.userId,
				isActive: options.isActive || 1
			})
			if (results.length > 0) {
				result = results.map(item => resFilter(item))
			}
		}

		if (!result) {
			throw new Error('二维码不存在')
		}
		return result
	} catch (error) {
		throw error
	}
}

/**
 * 批量更新二维码状态
 */
async function batchUpdateQrcodeStatus(ids, isActive) {
	if (!ids || ids.length === 0) {
		throw new Error('请选择要更新的二维码')
	}

	const sql = `UPDATE ${QRCODE} SET is_active = ?, update_time = CURRENT_TIMESTAMP WHERE id IN (?)`
	const params = [isActive, ids]

	try {
		await dbController.dbConnect(sql, params)
		return '批量更新成功'
	} catch (error) {
		throw error
	}
}

/**
 * 获取用户信息
 */
async function getUserInfo(userId) {
	try {
		const result = await dbController.queryById(USER, userId)
		if (!result) {
			throw new Error('用户不存在')
		}
		return {
			id: result.id,
			name: result.name,
			mobile: result.mobile,
			type: result.type
		}
	} catch (error) {
		throw error
	}
}

module.exports = {
	queryQrcodes,
	createQrcode,
	updateQrcode,
	deleteQrcode,
	getQrcodeDetail,
	batchUpdateQrcodeStatus
}
