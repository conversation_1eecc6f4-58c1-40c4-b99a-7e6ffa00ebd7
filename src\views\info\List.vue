<template>
  <div class="card">
    <SearchForm
      :searchParams="searchParams"
      :cropMaps="cropMaps"
      :sourceLabels="sourceLabels"
      @loadData="loadData"
      @clear="clear"
      @showScrapeDialog="scrapeDataDialogVisible = true"
    />
    <div class="card-body">
      <InfoTable
        :loading="loading"
        :tableData="tableData"
        :sourceLabels="sourceLabels"
        :total="total"
        :pageIndex.sync="pageIndex"
        :pageSize.sync="pageSize"
        @pagination="loadData"
        @handleInfoDetail="handleInfoDetail"
        @handleEditCrop="handleEditCrop"
        @handleDelete="handleDelete"
      />
    </div>
    <EditDialog
      :visible.sync="editDialogVisible"
      :editForm="editForm"
      :cropMaps="cropMaps"
      :editLoading="editLoading"
      @onEditBtnConfirm="onEditBtnConfirm"
    />
    <InfoDetailDialog
      :visible="infoDetailDialogVisible"
      :infoDetail="infoDetail"
      :sourceLabels="sourceLabels"
      :loadingInfoDetail="loadingInfoDetail"
      @update:visible="infoDetailDialogVisible = $event"
    />
    <ScrapeDataDialog
      :visible.sync="scrapeDataDialogVisible"
      :sourceLabels="sourceLabels"
      @refreshData="loadData"
    />
  </div>
</template>

<script>
  import infoManager from '@/manager/infoManager'
  import tableMixin from '@/mixins/tableMixin'
  import SearchForm from './components/SearchForm.vue'
  import InfoTable from './components/InfoTable.vue'
  import EditDialog from './components/EditDialog.vue'
  import InfoDetailDialog from './components/InfoDetailDialog.vue'
  import ScrapeDataDialog from './components/ScrapeDataDialog.vue'

  export default {
    name: 'InfoList',
    components: { SearchForm, InfoTable, EditDialog, InfoDetailDialog, ScrapeDataDialog },
    mixins: [tableMixin],
    data() {
      return {
        searchParams: {
          title: '',
          cropId: '',
          source: []
        },
        infoDetail: {},
        infoDetailDialogVisible: false,
        loadingInfoDetail: false,
        editDialogVisible: false,
        editForm: {},
        editLoading: false,
        rules: {
          cropIds: [{ required: true, message: '请选择作物品品类', trigger: ['blur', 'change'] }]
        },
        scrapeDataDialogVisible: false
      }
    },
    computed: {
      cropMaps() {
        return Object.values(this.$store.state.crop.cropMap).filter(item => item.parentId == 0)
      },
      sourceLabels() {
        const infoSource = this.$store.state.enums.enums.INFO_SOURCE || {}
        const result = {}
        Object.values(infoSource).forEach(item => {
          if (item && item.id && item.name) {
            result[item.id] = item.name
          }
        })
        return result
      }
    },
    methods: {
      /**
       * @description 清空搜索条件并重新加载数据
       */
      clear() {
        this.searchParams = {
          title: '',
          cropId: '',
          source: []
        }
        this.loadData()
      },
      async loadData(paginationParams = {}) {
        if (paginationParams.pageIndex !== undefined) {
          this.pageIndex = paginationParams.pageIndex
        }
        if (paginationParams.pageSize !== undefined) {
          this.pageSize = paginationParams.pageSize
        }

        this.loading = true
        try {
          const {
            searchParams: { title, source, cropId },
            pageIndex,
            pageSize
          } = this

          const params = {
            pageIndex,
            pageSize,
            title,
            cropId,
            source: source.length > 0 ? source.join(',') : ''
          }
          const res = await infoManager.getInfos(params)
          const { total, list } = res
          list.forEach(item => {
            const cropTags = []
            const cropIds = item.cropIds ? item.cropIds.split(',') : []
            cropIds.forEach(id => {
              parseInt(id) > 0 && cropTags.push(this.$store.state.crop.cropMap[parseInt(id)].name)
            })
            item.cropTags = cropTags
            return item
          })
          this.total = total
          this.tableData = list
        } catch (err) {
          this.$message.error(err)
        } finally {
          this.loading = false
        }
      },

      async handleDelete(item) {
        try {
          await this.$confirm(`将删除资讯：${item.title}，是否继续`, '删除提醒', {
            type: 'warning'
          })
          await infoManager.deleteInfo(item.id)
          this.$message.success('删除成功')
          this.loadData()
        } catch (err) {
          if (err !== 'cancel') {
            this.$message.error(err)
          }
        }
      },

      async handleInfoDetail(item) {
        this.infoDetailDialogVisible = true
        this.loadingInfoDetail = true
        try {
          this.infoDetail = await infoManager.getInfoDetail(item.id)
        } catch (err) {
          this.infoDetailDialogVisible = false
          this.$message.error(err)
        } finally {
          this.loadingInfoDetail = false
        }
      },

      handleEditCrop(item) {
        this.editDialogVisible = true
        this.editForm = {
          ...item,
          cropIds: item.cropIds
            ? item.cropIds
                .split(',')
                .map(id => Number(id))
                .filter(num => num != 0)
            : []
        }
      },

      async onEditBtnConfirm() {
        this.editLoading = true
        try {
          const params = { cropIds: this.editForm.cropIds.join(',') || '0' }
          await infoManager.updateInfo(this.editForm.id, params)
          this.$message.success('更新成功')
          this.editDialogVisible = false
          this.loadData()
          this.editForm = {}
        } catch (err) {
          this.$message.error(err)
        } finally {
          this.editLoading = false
        }
      }
    }
  }
</script>
