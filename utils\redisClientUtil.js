const redis = require('redis');
const client = redis.createClient();
client.on('error', function(err){
	console.error(err)
});

/** 
 * 添加string类型的数据 
 * @param key 键 
 * @params value 值  
 * @params expire (过期时间,单位秒;可为空，为空表示不过期) 
 */ 
function set(key, value, expire){
	return new Promise(function(resolve, reject){
		client.set(key, value, function(err, result){
			if(err){
				console.log(err);
				reject(err);
			}else{
				if(!isNaN(expire) && expire > 0){
					client.expire(key, parseInt(expire));
				}
				resolve(result);
			}
		})
	})
}

function get(key){
	return new Promise(function(resolve, reject){
		client.get(key, function(err, result){
			if(err){
				reject(err)
			}else{
				resolve(result)
			}
		})
	})
}
 
module.exports = {
    get,
    set
};