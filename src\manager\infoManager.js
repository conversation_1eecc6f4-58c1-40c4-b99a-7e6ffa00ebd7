import http from '@/utils/http'

function getInfos(params) {
	return http.post('admin/info/list', params)
}

function getInfoDetail(id) {
	return http.get(`admin/info/detail/${id}`)
}

// 管理员主动触发后端主动去爬取某一个资源的最新资讯
function scrapeInfo(source) {
	return http.get(`admin/info/crawling/${source}`)
}

function deleteInfo(id) {
	return http.delete(`admin/info/delete/${id}`)
}

function updateInfo(id, newInfo) {
	return http.put(`admin/info/update/${id}`, newInfo)
}

export default {
	getInfos,
	getInfoDetail,
	deleteInfo,
	scrapeInfo,
	updateInfo
}
