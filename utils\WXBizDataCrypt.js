var crypto = require('crypto')

function WXBizDataCrypt (appId, sessionKey) {
  this.appId = appId
  this.sessionKey = sessionKey
}

WXBizDataCrypt.prototype.decryptData = function (encryptedData, iv) {
  try {
    // base64 解码：使用 Buffer.from(data, encoding)
    const sessionKey = Buffer.from(this.sessionKey, 'base64');
    const encryptedBuf = Buffer.from(encryptedData, 'base64');
    const ivBuf = Buffer.from(iv, 'base64');

    // 创建解密器：aes-128-cbc
    const decipher = crypto.createDecipheriv('aes-128-cbc', sessionKey, ivBuf);
    decipher.setAutoPadding(true);

    // 解密数据
    // 注意：不要使用 'binary'，改用 'latin1'（等价于旧的 binary）
    let decoded = decipher.update(encryptedBuf, 'latin1', 'utf8');
    decoded += decipher.final('utf8');

    // 解析 JSON
    const result = JSON.parse(decoded);

    // 验证 AppID
    if (result.watermark?.appid !== this.appId) {
      throw new Error('Illegal Buffer');
    }

    return result;

  } catch (err) {
    // 捕获所有解密/解析错误
    throw new Error('Illegal Buffer');
  }
};

module.exports = WXBizDataCrypt