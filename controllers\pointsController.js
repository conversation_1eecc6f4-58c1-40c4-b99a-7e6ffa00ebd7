const dbController = require('./dbController')
const userController = require('./userController')
const { resFilter } = require('../utils/filter')
const { POINTS, SHARE_RECORDS, CONFIG } = require('../db').tableNames
const { v4: uuidv4 } = require('uuid')
const dateUtil = require('../utils/date')

function queryPoints(pageIndex, pageSize, userId, transactionType, orderBy = 'update_time') {
	const queryOptions = { userId, transactionType }
	return new Promise((resolve, reject) => {
		dbController.countId(POINTS, queryOptions).then(count => {
			if (count === 0) {
				resolve({
					total: 0,
					list: []
				})
			} else {
				dbController
					.query(POINTS, queryOptions, { pageIndex, pageSize, orderBy })
					.then(rows =>
						resolve({
							total: count,
							list: rows.map(item => resFilter(item))
						})
					)
					.catch(err => reject(err))
			}
		})
	})
}

async function addPointsRecord(params) {
	const { userId, transactionType, point, shareRecordId, description } = params
	try {
		const { points } = await userController.getUserInfo(userId)
		await dbController.add(POINTS, { userId, transactionType, point, shareRecordId, description })
		const currentPoints = points || 0
		let newPoints
		if (transactionType === 'gain') {
			newPoints = Number(currentPoints) + Number(point)
		} else if (transactionType === 'use') {
			newPoints = Number(currentPoints) - Number(point)
		} else {
			throw new Error('无效的 transactionType')
		}

		await userController.updateInfo(userId, { points: newPoints })

		return '积分记录添加成功'
	} catch (error) {
		throw new Error(`添加积分记录失败: ${error.message || error}`)
	}
}

function deletePointsRecord(id) {
	return dbController.deleteById(POINTS, id)
}

async function queryTodayUsedPoints(userId) {
	try {
		const startOfDay = dateUtil.format(new Date(Date.now()), 'yyyy-MM-dd')
		const queryOptions = {
			userId,
			transactionType: 'use',
			_special: '`update_time` >= "' + startOfDay + '"'
		}
		const rows = await dbController.query(POINTS, queryOptions, null)
		let totalUsedPoints = 0
		for (const row of rows) {
			totalUsedPoints += Number(row.point)
		}
		return totalUsedPoints
	} catch (error) {
		throw new Error(`查询本日使用积分数量失败: ${error.message || error}`)
	}
}

async function generateShareId(userId) {
	try {
		const shareId = uuidv4()
		await dbController.add(SHARE_RECORDS, { shareUserId: userId, shareId, used: 0 })
		return shareId
	} catch (error) {
		throw new Error(`生成分享ID失败: ${error.message || error}`)
	}
}

async function checkAndUseShareId(shareId, userId) {
	try {
		if (!shareId) return '无效的助力链接'

		const detailRows = await dbController.query(SHARE_RECORDS, { shareId }, null)
		const { id, used, shareUserId } = resFilter(detailRows[0])

		if (shareUserId === userId) return '不能为自己助力'
		if (used) return '已为好友助力'

		// await dbController.update(SHARE_RECORDS, { shareId }, { used: 1 })
		const config = await dbController.query(CONFIG, { id: 1 }, null)
		const { assistancePoints } = resFilter(config[0])

		await addPointsRecord({
			userId: shareUserId,
			transactionType: 'gain',
			point: assistancePoints || 1,
			description: '好友助力',
			shareRecordId: id
		})

		return '助力成功'
	} catch (error) {
		throw new Error(`处理分享ID失败: ${error.message || error}`)
	}
}

module.exports = {
	queryPoints,
	addPointsRecord,
	deletePointsRecord,
	queryTodayUsedPoints,
	generateShareId,
	checkAndUseShareId
}
