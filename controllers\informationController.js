//  千万仓咨询板块
const dbController = require('./dbController')
const { resFilter } = require('../utils/filter')
const { CROP, INFORMATION, INFORMATION_DETAIL } = require('../db').tableNames
const dateUtil = require('../utils/date')
const { ScraperAgri, ScraperGrainmarket } = require('../services/scraper')
const entities = require('entities')

const scraperAgri = new ScraperAgri()
const scraperGrainmarket = new ScraperGrainmarket()

async function matchAndInsertCrop (title) {
	try {
		const crops = await dbController.query(CROP, { parentId: 0 })
		const cropMap = new Map(crops.map(crop => [crop.name, crop]))
		for (const item of cropMap.keys()) {
			if (title.includes(item)) {
				return cropMap.get(item).id
			}
		}
		throw new Error('未找到匹配的作物')
	} catch (error) {
		throw new Error('匹配作物失败')
	}
}

// 新增资讯
function addInfo (newInfo) {
	const { title, originalLink, summary, publishTime, source, content } = newInfo
	const queryOptions = { title, originalLink }
	return Promise.all([dbController.query(INFORMATION, queryOptions), matchAndInsertCrop(title)])
		.then(([existingInfo, cropId]) => {
			if (existingInfo.length > 0) {
				return Promise.reject('资讯已存在')
			} else {
				const insertData = {
					source,
					title,
					content,
					publishTime,
					originalLink,
					summary,
					cropId: cropId || 0
				}
				return dbController.add(INFORMATION, insertData).then(res => '新增资讯成功')
			}
		})
		.catch(err => Promise.reject('资讯添加失败'))
}

// 批量新增资讯
async function batchAddInfo (infoList) {
	const titles = infoList.map(info => info.title)
	const originalLinks = infoList.map(info => info.originalLink)
	try {
		const existingInfos = await dbController.query(INFORMATION, {
			title: titles,
			originalLink: originalLinks
		})
		const existingInfosMap = new Map(existingInfos.map(info => [info.title, info.originalLink]))
		const filteredInfoList = infoList.filter(
			info => !existingInfosMap.has(info.title) && !existingInfosMap.has(info.originalLink)
		)

		if (filteredInfoList.length === 0) {
			return '所有资讯已存在'
		}
		const insertDataPromises = filteredInfoList.map(async info => {
			let cropId
			try {
				cropId = await matchAndInsertCrop(info.title)
			} catch (error) {
				console.error(`匹配作物失败: ${info.title}`, error)
				cropId = 0 // 返回默认值0，包含所有作物
			}
			return {
				source: info.source,
				title: info.title,
				content: info.content,
				publishTime: info.publishTime,
				originalLink: info.originalLink,
				summary: info.summary,
				crop_id: cropId
			}
		})
		const insertData = await Promise.all(insertDataPromises)
		await dbController.addMultiple(INFORMATION, insertData)
		return `批量新增资讯成功，共${insertData.length}条`
	} catch (error) {
		return '批量新增资讯失败'
	}
}

// 通过 info id 获取 info detail
async function getInfoById (infoId) {
	try {
		const [rows, detailRows] = await Promise.all([
			dbController.query(INFORMATION, { id: infoId }),
			dbController.query(INFORMATION_DETAIL, { infoId })
		])

		if (rows.length === 0) {
			throw new Error('信息未找到')
		}

		const info = resFilter(rows[0])
		const infoDetail = detailRows.length > 0 ? resFilter(detailRows[0]) : null

		// 如果没有找到详情，则进行抓取操作
		if (!infoDetail) {
			const { source, originalLink } = info
			let scrapedData
			if (source == 1) scrapedData = await scraperAgri.startScraping(originalLink, 'detail')
			if (source == 2) scrapedData = await scraperGrainmarket.startScraping(originalLink, 'detail')
			if (scrapedData && scrapedData.content) {
				// 更新转义 content 并保存到数据库
				const updateData = {
					infoId,
					author: scrapedData.author,
					content: entities.encodeHTML(scrapedData.content)
				}
				// 添加到数据库，且不等待返回结果
				dbController.add(INFORMATION_DETAIL, updateData).catch(console.error)
				return { ...info, ...scrapedData }
			} else {
				return `资讯详情为空`
			}
		}
		return { ...info, author: infoDetail.author, content: entities.decodeHTML(infoDetail.content) }
	} catch (err) {
		return `获取资讯详情失败: ${err.message || err}`
	}
}

// 编辑资讯
function editInfo (id, newInfo) {
	const { source, title, content } = newInfo
	return dbController
		.update(INFORMATION, { id }, { source, title, content })
		.then(res => '更新成功')
}
// 删除资讯
function deleteInfo (id) {
	return dbController
		.delete(INFORMATION, { id })
		.then(() => '删除成功')
		.catch(err => Promise.reject(err))
}
// 查询资讯
function queryInfo (
	pageIndex,
	pageSize,
	title,
	source,
	cropId,
	orderBy = 'public_time',
	orderSort = 'DESC'
) {
	cropId = cropId ? [cropId, 0] : [0]
	const queryOptions = { title, source, cropId }
	return new Promise((resolve, reject) => {
		dbController.count(INFORMATION, queryOptions, (err, rows) => {
			if (err) {
				reject(err)
			} else {
				const total = rows[0]['count(*)']
				if (total === 0) {
					resolve({
						total,
						list: []
					})
					return
				}
				dbController.query(
					INFORMATION,
					queryOptions,
					{ pageIndex, pageSize, orderBy, orderSort },
					(err, rows) => {
						if (err) {
							reject(err)
						} else {
							resolve({
								total,
								list: rows.map(item => resFilter(item))
							})
						}
					}
				)
			}
		})
	})
}

// 爬取资讯
async function scrapeInfo (source) {
	let allScrapedData = []
	try {
		if (!source) {
			throw new Error('参数错误')
		}
		const config = {
			1: {
				sourceURL: 'http://www.agri.cn/sj/gxxs/index',
				initialPage: 0,
				maxPages: 2,
				scraper: scraperAgri,
				method: 'startScraping'
			},
			2: {
				sourceURL: 'https://www.grainmarket.com.cn/centerweb/getData',
				initialPage: 1,
				maxPages: 2,
				scraper: scraperGrainmarket,
				method: 'startScraping'
			}
		}
		const { sourceURL, initialPage, maxPages, scraper, method } = config[source]

		for (let page = initialPage; page < maxPages; page++) {
			let url
			if (source == 1) url = `${sourceURL}${page > 0 ? '_' + page : ''}.htm`
			else url = sourceURL
			try {
				const scrapedData = await scraper[method](url, 'list', page)
				if (scrapedData.length === 0) {
					console.log(`爬取第 ${page + 1} 页没有数据，停止爬取`)
					break
				}
				allScrapedData.push(...scrapedData)
			} catch (error) {
				console.error(`爬取第 ${page + 1} 页发生错误:`, error)
				// 重试机制
				// await retryScrape(scraper, method, url, 'list', page);
			}
		}

		console.log('爬取完成', allScrapedData)
		const res = await batchAddInfo(allScrapedData)
		return res
	} catch (error) {
		console.error('爬取发生错误:', error)
		return error
	}
}

async function retryScrape (scraper, method, url, type, page, maxRetries = 3) {
	for (let attempt = 1; attempt <= maxRetries; attempt++) {
		try {
			const scrapedData = await scraper[method](url, type, page)
			return scrapedData
		} catch (error) {
			console.error(`第 ${attempt} 次重试爬取第 ${page + 1} 页发生错误:`, error)
			await new Promise(resolve => setTimeout(resolve, 1000)) // 等待1秒后重试
		}
	}
	throw new Error(`爬取第 ${page + 1} 页失败，达到最大重试次数`)
}

module.exports = {
	addInfo,
	batchAddInfo,
	getInfoById,
	editInfo,
	deleteInfo,
	queryInfo,
	scrapeInfo
}
