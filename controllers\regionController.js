const { REGION, CITY_CODE } = require('../db').tableNames;
const { resFilter, getKeysObj } = require('../utils/filter')
const dbController = require('./dbController')
const tableKeys = ['name', 'provinceCodes']

// 新增区域
function createRegion (reqData) {
    return new Promise((resolve, reject) => {
        let newData = getKeysObj(reqData, tableKeys);
        dbController.add(REGION, newData, (err, res) => {
            if (err) {
                reject(err);
            } else {
                resolve('新增区域成功');
            }
        })
    })
}

// 更新区域
function updateRegion (id, reqData) {
    return new Promise((resolve, reject) => {
        let newData = getKeysObj(reqData, tableKeys);
        dbController.query(REGION, { id }, null, (err, rows) => {
            if (err) {
                reject(err);
            } else if (rows && rows.length === 1) {
                if (rows[0].edit_able) { // 如果数据可以编辑
                    dbController.update(REGION, { id }, newData, (err, res) => {
                        if (err) {
                            reject(err);
                        } else {
                            resolve('区域更新成功');
                        }
                    })
                } else {
                    reject('默认区域，不允许编辑')
                }
            } else {
                reject('数据不存在')
            }
        })
    })
}
// 删除区域
function deleteRegion (id) {
    return new Promise((resolve, reject) => {
        dbController.query(REGION, { id }, null, (err, rows) => {
            if (err) {
                reject(err);
            } else if (rows && rows.length === 1) {
                if (rows[0].edit_able) { // 如果数据可以编辑
                    dbController.delete(REGION, { id }, (err, res) => {
                        if (err) {
                            reject(err);
                        } else {
                            resolve('删除成功');
                        }
                    })
                } else {
                    reject('默认区域，不允许删除')
                }
            } else {
                reject('数据不存在')
            }
        })
    })
}
// 根据确定的查询条件分页查询区域信息
function queryRegion (pageIndex, pageSize, condition) {
    return new Promise((resolve, reject) => {
        dbController.count(REGION, condition, (err, rows) => {
            if (err) {
                reject(err)
            } else {
                const total = rows[0]['count(*)'];
                dbController.query(REGION, condition, { pageIndex, pageSize, orderBy: 'id' }, (err, rows) => {
                    if (err) {
                        reject(err)
                    } else {
                        resolve({
                            total,
                            list: rows.map(item => resFilter(item))
                        })
                    }
                })
            }
        })
    })
}

function queryRegionByIds (idArr) {
    return new Promise((resolve, reject) => {
        dbController.query(REGION, { id: idArr }, null, (err, rows) => {
            if (err) {
                reject(err)
            } else {
                const map = {};
                rows.forEach(item => {
                    map[item.id] = resFilter(item)
                })
                const arr = [];
                idArr.forEach(id => {
                    map[id] && arr.push(map[id])
                })
                resolve(arr)
            }
        })
    })
}

function queryAllRegion () {
    return new Promise((resolve, reject) => {
        dbController.query(REGION, null, null, (err, rows) => {
            if (err) {
                reject(err)
            } else {
                resolve(rows.map(item => resFilter(item)))
            }
        })
    })
}

function queryProvincePreCode () {
    return new Promise((resolve, reject) => {
        dbController.query(CITY_CODE, { level: 1 }, null, (err, rows) => {
            if (err) {
                reject(err)
            } else {
                resolve(rows.map(item => {
                    let name = item.name.substring(0, 2)
                    if (name === '内蒙') {
                        name = '内蒙古'
                    } else if (name === '黑龙') {
                        name = '黑龙江'
                    }
                    const code = Math.floor(item.code / 10000000000)
                    return {
                        code,
                        name
                    }
                }))
            }
        })
    })
}


module.exports = {
    createRegion,
    updateRegion,
    deleteRegion,
    queryRegion,
    queryAllRegion,
    queryRegionByIds,
    queryProvincePreCode
}