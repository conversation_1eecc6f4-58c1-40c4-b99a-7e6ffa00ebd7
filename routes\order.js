var express = require('express');
var router = express.Router();
const orderController = require('../controllers/orderController')
const { ORDER_STATUS } = require('../constant/enum')
const dateUtil = require('../utils/date')

router.post('/add', function (req, res) {
    orderController.createOrder(req.body).then(data => {
        res.sendSuccess(data)
    }).catch(err => {
        res.sendMessage(err)
    })
});

router.put('/cancel/:id', function (req, res) {
    const { id } = req.params;
    orderController.updateOrder(id, { status: ORDER_STATUS.cancel }).then(data => {
        res.sendSuccess(data)
    }).catch(err => {
        res.sendMessage(err)
    })
});

router.put('/stop/:id', function (req, res) {
    const { id } = req.params;
    orderController.updateOrder(id, { status: ORDER_STATUS.stop }).then(data => {
        res.sendSuccess(data)
    }).catch(err => {
        res.sendMessage(err)
    })
});

router.put('/active/:id', function (req, res) {
    const { id } = req.params;
    orderController.updateOrder(id, { status: ORDER_STATUS.default }).then(data => {
        res.sendSuccess(data)
    }).catch(err => {
        res.sendMessage(err)
    })
});

router.put('/refresh/:id', function (req, res) {
    const { id } = req.params;
    const updateTime = dateUtil.format(new Date(), 'yyyy-MM-dd hh:mm:ss');
    orderController.updateOrder(id, { updateTime }).then((data) => {
        res.sendSuccess(data)
    }).catch(err => {
        res.sendMessage(err)
    })
});

router.get('/detail/:id', function (req, res) {
    orderController.queryOrderById(req.params.id).then(data => {
        res.sendSuccess(data)
    }).catch(err => {
        res.sendMessage(err)
    })
});

router.get('/tradeDetail/:id', function (req, res) {
    orderController.queryTradeById(req.params.id).then(data => {
        res.sendSuccess(data)
    }).catch(err => {
        res.sendMessage(err)
    })
});

// 用户请求测量某个订单
router.get('/addAsk/:id', function (req, res) {
    orderController.addOrderAsk(req.body.userId, req.params.id).then(data => {
        res.sendSuccess(data)
    }).catch(err => {
        res.sendMessage(err)
    })
});


// 用户收藏某个订单
router.get('/addLike/:id', function (req, res) {
    orderController.addOrderLike(req.body.userId, req.params.id).then(data => {
        res.sendSuccess(data)
    }).catch(err => {
        res.sendMessage(err)
    })
});

// 用户取消收藏某个订单
router.get('/cancelLike/:id', function (req, res) {
    orderController.cancelOrderLike(req.body.userId, req.params.id).then(data => {
        res.sendSuccess(data)
    }).catch(err => {
        res.sendMessage(err)
    })
});

// 查询用户与某条订单的交互行为历史
router.get('/relation/add/telephone/:id', function (req, res) {
    orderController.addMobileClickLog(req.body.userId, req.params.id).then(data => {
        res.sendSuccess(data)
    }).catch(err => {
        res.sendMessage(err)
    })
});

// 查询用户与某条订单的交互行为历史
router.get('/relation/add/view/:id', function (req, res) {
    orderController.addOrderViewLog(req.body.userId, req.params.id).then(data => {
        res.sendSuccess(data)
    }).catch(err => {
        res.sendMessage(err)
    })
});

// 查询用户与某条订单的交互行为历史
router.get('/relation/list/:id', function (req, res) {
    orderController.queryUserOrderRelationList(req.body.userId, req.params.id).then(data => {
        res.sendSuccess(data)
    }).catch(err => {
        res.sendMessage(err)
    })
});

// 用户查看首页订单列表
router.post('/list', function (req, res) {
    const { pageIndex, pageSize } = req.body;
    orderController.queryHomeOrders(pageIndex, pageSize).then(data => {
        res.sendSuccess(data)
    }).catch(err => {
        res.sendMessage(err)
    })
});

// 用户查看首页订单列表
router.post('/nearbyList', function (req, res) {
    const { longitude, latitude, pageIndex, pageSize } = req.body;
    orderController.queryHomeNearbyOrders(longitude, latitude, pageIndex, pageSize).then(data => {
        res.sendSuccess(data)
    }).catch(err => {
        res.sendMessage(err)
    })
});

// 用户查看首页订单列表
router.post('/tradeList', function (req, res) {
    const { pageIndex, pageSize } = req.body;
    orderController.queryHomeTradeOrders(pageIndex, pageSize).then(data => {
        res.sendSuccess(data)
    }).catch(err => {
        res.sendMessage(err)
    })
});

// 查询用户个人订单
router.post('/my', function (req, res) {
    const { userId, status } = req.body;
    orderController.queryUserOrders(userId, status).then(data => {
        res.sendSuccess(data)
    }).catch(err => {
        res.sendMessage(err)
    })
});

// 查询用户收藏的订单
router.post('/like', function (req, res) {
    const { userId } = req.body;
    orderController.queryUserLikeOrders(userId).then(data => {
        res.sendSuccess(data)
    }).catch(err => {
        res.sendMessage(err)
    })
});

// 用户更新某条订单信息
router.put('/update/:id', function (req, res) {
    const { id } = req.params;
    orderController.queryOrderById(id).then(oldOrder => {
        if (oldOrder.userId === req.body.userId) {
            orderController.updateOrder(id, req.body).then(data => {
                res.sendSuccess(data)
            }).catch(err => {
                res.sendMessage(err)
            })
        } else if (req.session.isCollector || req.session.isAdmin) {
            orderController.updateOrderByCollector(id, req.body).then(data => {
                res.sendSuccess(data)
            }).catch(err => {
                res.sendMessage(err)
            })
        } else {
            res.sendMessage('无权操作')
        }
    }).catch(err => {
        res.sendMessage(err)
    })
});

// 信息收集员查看待收集的订单列表
router.post('/collector/askList', function (req, res) {
    const { pageIndex, pageSize } = req.body;
    orderController.queryAskOrders(pageIndex, pageSize).then(data => {
        res.sendSuccess(data)
    }).catch(err => {
        res.sendMessage(err)
    })
});

// 信息收集员更新某条订单信息
router.put('/collector/update/:id', function (req, res) {
    const { id } = req.params;
    orderController.updateOrder(id, req.body).then(data => {
        res.sendSuccess(data)
    }).catch(err => {
        res.sendMessage(err)
    })
});


// 信息收集员更新某条订单信息
router.post('/addTrade/:id', function (req, res) {
    const { id } = req.params;
    orderController.addOrderTrade(id, req.body).then(data => {
        res.sendSuccess(data)
    }).catch(err => {
        res.sendMessage(err)
    })
});

router.delete('/delete/:id', function (req, res) {
    const { id } = req.params;
    orderController.deleteOrder(id).then(data => {
        res.sendSuccess(data)
    }).catch(err => {
        res.sendMessage(err)
    })
});

module.exports = router;
