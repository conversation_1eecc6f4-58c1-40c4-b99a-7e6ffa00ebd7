const express = require('express')
const router = express.Router()
const enumConstants = require('../constant/enum')

/**
 * 获取所有枚举数据
 */
router.get('/all', function (req, res) {
	try {
		res.sendSuccess(enumConstants)
	} catch (error) {
		res.sendMessage('获取枚举数据失败: ' + error.message)
	}
})

/**
 * 获取特定枚举数据
 */
router.get('/:enumName', function (req, res) {
	try {
		const { enumName } = req.params
		const enumData = enumConstants[enumName]

		if (!enumData) {
			return res.sendMessage(`枚举 ${enumName} 不存在`)
		}

		res.sendSuccess(enumData)
	} catch (error) {
		res.sendMessage('获取枚举数据失败: ' + error.message)
	}
})

module.exports = router
