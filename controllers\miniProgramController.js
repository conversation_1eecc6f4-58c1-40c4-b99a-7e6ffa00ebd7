// 农舟小程序模块组件
const { USER } = require('../db').tableNames;
const { resFilter } = require('../utils/filter')
const dbController = require('./dbController')
const wxCommonApi = require('../utils/wxCommonApi');
const { NZ_MINI } = require('../constant/appName');

// 获取用户信息
function getWxUser (code, encryptedData, iv) {
    return wxCommonApi.requestWxEncryptedData(NZ_MINI, code, encryptedData, iv);
}

// 更新用户的手机号
function updateMobile (id, mobile) {
    return new Promise((resolve, reject) => {
        dbController.updateWithoutEmpty(USER, { id }, { mobile }, (err, rows) => {
            if (err) {
                reject(err)
            } else {
                resolve(rows)
            }
        })
    })
}

// 更新用户的位置信息
function updateAddress (id, latitude, longitude, cityCode, address) {
    return new Promise((resolve, reject) => {
        dbController.updateWithoutEmpty(USER, { id }, { latitude, longitude, cityCode, address }, (err, rows) => {
            if (err) {
                reject(err)
            } else {
                resolve(rows)
            }
        })
    })
}

// 更新用户的信息，通用方法
function updateInfo (id, newInfo) {
    return new Promise((resolve, reject) => {
        dbController.updateWithoutEmpty(USER, { id }, newInfo, (err, rows) => {
            if (err) {
                reject(err)
            } else {
                resolve(rows)
            }
        })
    })
}


module.exports = {
    getWxUser,
    updateMobile,
    updateAddress,
    updateInfo
}