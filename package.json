{"name": "admin-frontend", "version": "1.0.0", "private": true, "scripts": {"dev": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "lint:fix": "vue-cli-service lint --fix", "format": "prettier --write \"src/**/*.{js,vue,ts,css,less,scss,json,md}\"", "format:check": "prettier --check \"src/**/*.{js,vue,ts,css,less,scss,json,md}\""}, "dependencies": {"axios": "^1.11.0", "core-js": "^3.45.0", "element-ui": "^2.15.14", "md5": "^2.3.0", "vue": "^2.7.16", "vue-router": "^3.6.5", "vuex": "^3.6.2"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.28.0", "@vue/cli-plugin-babel": "~5.0.8", "@vue/cli-plugin-eslint": "~5.0.8", "@vue/cli-service": "~5.0.8", "eslint": "^8.0.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.4", "eslint-plugin-vue": "^9.33.0", "prettier": "^3.6.2", "sass": "^1.90.0", "sass-loader": "^16.0.5", "vue-template-compiler": "^2.7.16"}, "engines": {"node": "20.19.4"}}