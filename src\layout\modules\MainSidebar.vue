<template>
  <!-- Main Sidebar Container -->
  <div class="sidebar-container" :class="{ collapse: collapse }">
    <!-- Brand Logo -->
    <div class="sidebar-logo-container" :class="{ collapse: collapse }">
      <router-link to="/" class="sidebar-logo-link">
        <img src="../../images/logo.png" class="sidebar-logo" alt="logo" />
        <h1 v-if="!collapse" class="sidebar-title">千万仓管理后台</h1>
      </router-link>
    </div>

    <!-- Sidebar -->
    <div class="sidebar-wrapper">
      <!-- Sidebar Menu -->
      <el-menu
        :default-active="activeMenu"
        :collapse="collapse"
        :background-color="variables.menuBg"
        :text-color="variables.menuText"
        :active-text-color="variables.menuActiveText"
        :unique-opened="false"
        :collapse-transition="false"
        :router="true"
        mode="vertical"
        class="sidebar-menu"
      >
        <template v-for="route in menus">
          <!-- 处理有多个子路由的情况 -->
          <el-submenu
            v-if="route.children && route.children.length > 1"
            :key="route.path"
            :index="route.path"
            class="submenu-item"
          >
            <template slot="title">
              <i :class="route.meta.icon"></i>
              <span slot="title">{{ route.meta.title }}</span>
            </template>
            <el-menu-item
              v-for="child in route.children.filter(c => !(c.meta && c.meta.hidden))"
              :key="child.path"
              :index="resolvePath(route.path, child.path)"
              class="submenu-item"
            >
              <i :class="child.meta.icon"></i>
              <span slot="title">{{ child.meta.title }}</span>
            </el-menu-item>
          </el-submenu>

          <!-- 处理只有一个子路由的情况，将其作为顶级菜单项 -->
          <el-menu-item
            v-else-if="
              route.children &&
                route.children.length === 1 &&
                !(route.children[0].meta && route.children[0].meta.hidden)
            "
            :key="route.path"
            :index="resolvePath(route.path, route.children[0].path)"
            class="menu-item"
          >
            <i :class="route.children[0].meta.icon || route.meta.icon"></i>
            <span slot="title">{{ route.children[0].meta.title }}</span>
          </el-menu-item>

          <!-- 处理没有子路由的情况，作为顶级菜单项 -->
          <el-menu-item
            v-else-if="!route.children && !(route.meta && route.meta.hidden)"
            :key="route.path"
            :index="route.path"
            class="menu-item"
          >
            <i :class="route.meta.icon"></i>
            <span slot="title">{{ route.meta.title }}</span>
          </el-menu-item>
        </template>
      </el-menu>
    </div>
  </div>
</template>

<script>
	const themeVariables = {
		menuBg: '#304156',
		menuText: '#bfcbd9',
		menuActiveText: '#409EFF'
	}

	export default {
		data() {
			return {
				variables: themeVariables
			}
		},
		computed: {
			menus() {
				// 获取所有需要显示在侧边栏的路由
				// 合并 constantRoutes 和 asyncRoutes 中需要显示的路由
				const { constantRoutes, asyncRoutes } = require('@/router/routes')
				const allRoutes = [...constantRoutes, ...asyncRoutes]
				return allRoutes.filter(route => {
					// 过滤掉隐藏的路由
					return route.meta && !route.meta.hidden && route.children && route.children.length > 0
				})
			},
			collapse() {
				return this.$store.state.layout.collapseMenu
			},
			activeMenu() {
				const route = this.$route
				const { meta, path } = route

				// 如果当前路由有父级路由，则使用父级路由的路径作为激活项
				if (meta.activeMenu) {
					return meta.activeMenu
				}
				return path
			}
		},
		methods: {
			toggleSidebar() {
				this.$store.commit('layout/SIDEBAR_COLLAPSE_CHANGE')
			},
			resolvePath(basePath, routePath) {
				if (/^(https?:|mailto:|tel:)/.test(routePath)) {
					return routePath
				}
				const separator = '/'
				const base = basePath.endsWith(separator) ? basePath.slice(0, -1) : basePath
				const route = routePath.startsWith(separator) ? routePath.slice(1) : routePath
				return base + separator + route
			}
		}
	}
</script>

<style lang="scss" scoped>
	@use '../../styles/variables.scss' as *;

	.sidebar-container {
		transition: width 0.28s;
		width: 210px !important;
		height: 100vh;
		position: relative;
		background-color: $menuBg;
		color: $menuText;
		flex-shrink: 0;

		&.collapse {
			width: 64px !important;
		}

		.sidebar-logo-container {
			position: relative;
			width: 100%;
			height: 60px;
			line-height: 60px;
			background: #2b3a4d;
			text-align: center;
			overflow: hidden;
			box-shadow: 2px 0 6px rgba(0, 21, 41, 0.35);
			transition: width 0.28s;

			&.collapse {
				.sidebar-logo {
					margin-right: 0;
				}
			}

			.sidebar-logo-link {
				height: 100%;
				width: 100%;
				display: flex;
				align-items: center;
				justify-content: center;
				text-decoration: none;

				.sidebar-logo {
					width: 38px;
					height: 38px;
					vertical-align: middle;
					margin-right: 12px;
					transition: margin-right 0.28s;
				}

				.sidebar-title {
					display: inline-block;
					margin: 0;
					color: #fff;
					font-weight: 600;
					line-height: 60px;
					font-size: 16px;
					font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;
					vertical-align: middle;
					transition: opacity 0.28s;
					opacity: 1;
					overflow: hidden;
					white-space: nowrap;
				}
			}
		}

		.sidebar-wrapper {
			height: calc(100% - 60px);
			overflow: auto;

			&::-webkit-scrollbar {
				width: 6px;
			}

			&::-webkit-scrollbar-thumb {
				background: rgba(0, 0, 0, 0.2);
				border-radius: 3px;

				&:hover {
					background: rgba(0, 0, 0, 0.3);
				}
			}

			&::-webkit-scrollbar-track {
				background: transparent;
			}

			.sidebar-menu {
				border: none;
				height: 100%;
				width: 100% !important;
			}
		}

		&.collapse {
			.sidebar-logo-container {
				.sidebar-logo-link {
					justify-content: center;

					.sidebar-title {
						opacity: 0;
						width: 0;
					}
				}
			}
		}
	}

	::v-deep .el-menu {
		border: none;
		height: 100%;
		width: 100% !important;
		background-color: $menuBg !important;

		.el-menu-item,
		.el-submenu__title {
			&:hover {
				background-color: rgba(0, 0, 0, 0.06) !important;
			}

			i {
				color: $menuText;
			}

			span {
				color: $menuText;
			}
		}

		.el-menu-item.is-active {
			background-color: rgba(64, 158, 255, 0.1) !important;
			color: $menuActiveText;

			i {
				color: $menuActiveText;
			}

			span {
				color: $menuActiveText;
			}
		}

		.el-submenu__title {
			&:hover {
				background-color: rgba(0, 0, 0, 0.06) !important;
			}

			i {
				color: $menuText;
			}

			span {
				color: $menuText;
			}
		}

		.el-submenu {
			&.is-active {
				> .el-submenu__title {
					color: $menuActiveText !important;

					i {
						color: $menuActiveText;
					}
				}
			}
		}
	}

	::v-deep .el-menu--collapse {
		.el-submenu {
			& > .el-submenu__title {
				& > span {
					opacity: 0;
					width: 0;
					overflow: hidden;
					transition: opacity 0.2s;
				}

				&:hover > span {
					opacity: 1;
					position: fixed;
					z-index: 9999;
					background: #304156;
					color: #bfcbd9;
					padding: 4px 12px;
					border-radius: 4px;
					white-space: nowrap;
					margin-left: 32px;
				}
			}
		}

		.el-menu-item {
			& > span {
				opacity: 0;
				width: 0;
				overflow: hidden;
				transition: opacity 0.2s;
			}

			&:hover > span {
				opacity: 1;
				position: fixed;
				z-index: 9999;
				background: #304156;
				color: #bfcbd9;
				padding: 4px 12px;
				border-radius: 4px;
				white-space: nowrap;
				margin-left: 32px;
			}
		}

		.el-submenu__title,
		.el-menu-item {
			text-align: center;

			i {
				margin-right: 0;
			}
		}
	}

	::v-deep .el-menu-item i,
	::v-deep .el-submenu__title i {
		margin-right: 10px;
		width: 16px;
		text-align: center;
		font-size: 14px;
		vertical-align: middle;
	}

	::v-deep .el-menu-item span,
	::v-deep .el-submenu__title span {
		vertical-align: middle;
		font-size: 14px;
	}
</style>
