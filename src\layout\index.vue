<template>
  <el-container class="app-wrapper" :class="{ 'hide-sidebar': collapse }">
    <el-aside class="sidebar-container">
      <MainSidebar />
    </el-aside>
    <el-container class="main-container">
      <el-header class="navbar-container">
        <Navbar />
      </el-header>
      <el-main class="app-main">
        <router-view />
      </el-main>
    </el-container>
  </el-container>
</template>

<script>
	import Navbar from './modules/Navbar'
	import MainSidebar from './modules/MainSidebar'

	export default {
		components: {
			Navbar,
			MainSidebar
		},
		computed: {
			collapse() {
				return this.$store.state.layout.collapseMenu
			}
		}
	}
</script>

<style lang="scss" scoped>
	.app-wrapper {
		height: 100vh;
		width: 100%;
		position: relative;

		&.hide-sidebar {
			.sidebar-container {
				width: 64px !important;
			}

			.main-container {
				margin-left: 64px;
			}
		}
	}

	.sidebar-container {
		transition: width 0.28s;
		width: 210px !important;
		height: 100%;
		position: fixed;
		top: 0;
		bottom: 0;
		left: 0;
		z-index: 1001;
		overflow: hidden;
	}

	.main-container {
		min-height: 100%;
		transition: margin-left 0.28s;
		margin-left: 210px;
		position: relative;
		display: flex;
		flex-direction: column;
	}

	.navbar-container {
		overflow: hidden;
		position: relative;
		background: #fff;
		box-shadow: 0 1px 3px rgba(0, 21, 41, 0.09);
		flex-shrink: 0;
	}

	.app-main {
		// min-height: calc(100vh - 50px);
		width: 100%;
		position: relative;
		overflow: auto;
		// background-color: #f0f2f5;
		padding: 20px;
	}
</style>
