const ScraperStrategyFactory = require('./ScraperStrategyFactory')
const cropController = require('../../controllers/cropController')
const dbController = require('../../controllers/dbController')
const ossController = require('../../controllers/ossController')
const { INFORMATION } = require('../../db').tableNames
const { resFilter, getKeysObj } = require('../../utils/filter')
const { ScraperError, logger, healthChecker } = require('./ScraperUtils')

/**
 * 爬虫管理器
 * 提供高级的爬虫操作接口，处理业务逻辑
 */
class ScraperManager {
	constructor() {
		this.factory = ScraperStrategyFactory
		this.infoTableKeys = [
			'title',
			'cropIds',
			'content',
			'source',
			'sourceId',
			'author',
			'photos',
			'files',
			'originalLink',
			'summary',
			'publishTime',
			'category'
		]
	}

	/**
	 * 初始化数据源数据
	 * @param {number} sourceId - 数据源ID
	 * @param {Object} options - 选项 {pagesToScrape: number}
	 * @returns {Promise<string>} - 初始化结果
	 */
	async initializeSourceData(sourceId, options = {}) {
		const operationId = `init-${sourceId}-${Date.now()}`
		logger.info(`开始初始化数据源 ${sourceId}`, { operationId, options })

		try {
			// 健康检查
			const health = await this.performHealthCheck()
			if (!health.database.accessible) {
				throw new ScraperError('数据库连接失败，无法执行初始化', sourceId, 'initialize')
			}

			// 检查是否已初始化
			const existingCount = await dbController.countId(INFORMATION, { source: sourceId })
			if (existingCount > 0) {
				const sourceName = this.factory.getSourceConfig(sourceId)?.name || `Source ${sourceId}`
				const message = `${sourceName}数据已初始化，不可重复录入`
				logger.warn(message, { sourceId, existingCount })
				return message
			}

			const strategy = this.factory.getStrategy(sourceId)
			const config = this.factory.getSourceConfig(sourceId)
			const crops = await cropController.queryCrops(null, null, 1)

			let allData = []
			const pagesToScrape = options.pagesToScrape || 3

			logger.info(`开始抓取数据源 ${sourceId}`, {
				pagesToScrape,
				categories: config.categories?.length
			})

			// 根据不同数据源的特点抓取数据
			if (config.categories) {
				for (const [categoryIndex, category] of config.categories.entries()) {
					logger.info(`处理分类 ${categoryIndex + 1}/${config.categories.length}: ${category.name}`)

					for (let page = 0; page < pagesToScrape; page++) {
						try {
							const pageData = await strategy.scrapeListPage({
								url: category.url,
								page,
								category: category.name,
								categoryConfig: category // 传递分类配置用于分页
							})
							allData.push(...pageData)
							logger.debug(
								`分类 ${category.name} 第${page + 1}页抓取成功，获得 ${pageData.length} 条数据`
							)
						} catch (error) {
							logger.error(`抓取分类 ${category.name} 第${page + 1}页失败`, {
								error: error.message,
								categoryUrl: category.url,
								page
							})
						}
					}
				}
			} else {
				for (let page = 0; page < pagesToScrape; page++) {
					try {
						const pageData = await strategy.scrapeListPage({ page })
						allData.push(...pageData)
						logger.debug(`第${page + 1}页抓取成功，获得 ${pageData.length} 条数据`)
					} catch (error) {
						logger.error(`抓取第${page + 1}页失败`, { error: error.message, page })
					}
				}
			}

			logger.info(`完成列表页抓取，共获得 ${allData.length} 条数据`)

			// 按时间倒序处理，先插入老数据
			allData.reverse()

			let successCount = 0
			let errorCount = 0
			let skippedCount = 0

			// 并发处理详情页抓取，提高效率
			const CONCURRENT_LIMIT = 5 // 同时进行的请求数
			const batches = []

			// 将数据分批处理
			for (let i = 0; i < allData.length; i += CONCURRENT_LIMIT) {
				batches.push(allData.slice(i, i + CONCURRENT_LIMIT))
			}

			logger.info(`开始并发处理详情页，共${batches.length}批，每批最多${CONCURRENT_LIMIT}个`)

			for (const [batchIndex, batch] of batches.entries()) {
				// 并发处理当前批次
				const batchPromises = batch.map(async (item, itemIndex) => {
					try {
						// 抓取详情数据
						const detailData = await strategy.scrapeDetailPage(item.originalLink)

						// 合并数据
						const fullData = { ...item, ...detailData }

						// 匹配作物
						if (!fullData.cropIds) {
							if (config.defaultCropId) {
								fullData.cropIds = config.defaultCropId
							} else {
								// 安全地构建文本数组，过滤空值
							const textArray = [fullData.title, fullData.content].filter(text => text != null && text !== '')
							fullData.cropIds = this.matchCropByText(crops, textArray)
							}
						}

						// 处理图片和文件上传
						if (fullData.photos) {
							fullData.photos = await this.processMediaUrls(fullData.photos)
						}
						if (fullData.files) {
							fullData.files = await this.processMediaUrls(fullData.files)
						}

						// 确保包含必要字段
						if (!fullData.source) {
							fullData.source = sourceId
						} else if (typeof fullData.source === 'object' && fullData.source.id) {
							fullData.source = fullData.source.id
						}

						// sourceId字段应该存储原始文章ID，不是数据源ID
						if (!fullData.sourceId && item.sourceId) {
							fullData.sourceId = item.sourceId
						}
						if (!fullData.sourceId) {
							const urlId = this.extractIdFromUrl(item.originalLink)
							fullData.sourceId =
								urlId || `${sourceId}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
						}

						// 保存到数据库
						const result = await this.addInfoWithoutCheck(fullData)
						return {
							success: result.success,
							skipped: result.skipped,
							title: item.title,
							index: batchIndex * CONCURRENT_LIMIT + itemIndex
						}
					} catch (error) {
						logger.error(`处理文章失败: ${item.title}`, {
							error: error.message,
							url: item.originalLink
						})
						return {
							error: true,
							title: item.title,
							errorMessage: error.message,
							index: batchIndex * CONCURRENT_LIMIT + itemIndex
						}
					}
				})

				// 等待当前批次完成
				const batchResults = await Promise.allSettled(batchPromises)

				// 统计结果并处理错误
				batchResults.forEach((result, index) => {
					if (result.status === 'fulfilled') {
						const data = result.value
						if (data.error) {
							errorCount++
						} else if (data.skipped) {
							skippedCount++
						} else if (data.success) {
							successCount++
						}
					} else {
						errorCount++
						logger.error('批次Promise失败', { 
							batchIndex, 
							itemIndex: index,
							error: result.reason?.message || 'Unknown error'
						})
					}
				})

				logger.info(
					`批次 ${batchIndex + 1}/${
						batches.length
					} 完成，当前统计 - 成功: ${successCount}, 跳过: ${skippedCount}, 失败: ${errorCount}`
				)

				// 批次间添加延迟，避免服务器压力过大
				if (batchIndex < batches.length - 1) {
					await this.delay(1000)
				}
			}

			const message = `数据录入完成，共处理${allData.length}条，成功${successCount}条，跳过${skippedCount}条，失败${errorCount}条`
			logger.info(`数据源 ${sourceId} 初始化完成`, {
				total: allData.length,
				success: successCount,
				skipped: skippedCount,
				error: errorCount
			})
			return message
		} catch (error) {
			logger.error(`初始化数据源${sourceId}失败`, { error: error.message, operationId })
			throw new ScraperError(
				`初始化数据源${sourceId}失败: ${error.message}`,
				sourceId,
				'initialize',
				error
			)
		}
	}

	/**
	 * 更新最新数据
	 * @param {number} sourceId - 数据源ID
	 * @param {Object} options - 选项 {pagesToScrape: number}
	 * @returns {Promise<Object>} - 更新结果统计
	 */
	async updateLatestData(sourceId, options = {}) {
		try {
			const strategy = this.factory.getStrategy(sourceId)
			const config = this.factory.getSourceConfig(sourceId)
			const crops = await cropController.queryCrops(null, null, 1)

			let allData = []
			const pagesToScrape = options.pagesToScrape || 1

			// 只抓取最新的几页数据
			if (config.categories) {
				for (const category of config.categories) {
					for (let page = 0; page < pagesToScrape; page++) {
						try {
							const pageData = await strategy.scrapeListPage({
								url: category.url,
								page,
								category: category.name,
								categoryConfig: category // 传递分类配置用于分页
							})
							allData.push(...pageData)
						} catch (error) {
							console.error(`抓取分类 ${category.name} 第${page + 1}页失败:`, error.message)
						}
					}
				}
			} else {
				for (let page = 0; page < pagesToScrape; page++) {
					try {
						const pageData = await strategy.scrapeListPage({ page })
						allData.push(...pageData)
					} catch (error) {
						console.error(`抓取第${page + 1}页失败:`, error.message)
					}
				}
			}

			let addCount = 0
			let errorCount = 0
			let ignoreCount = 0

			for (const item of allData) {
				try {
					// 检查是否已存在
					const existingCount = await dbController.countId(INFORMATION, {
						source: sourceId,
						sourceId: item.sourceId
					})

					if (existingCount === 0) {
						// 抓取详情数据
						const detailData = await strategy.scrapeDetailPage(item.originalLink)

						// 合并数据
						const fullData = { ...item, ...detailData }

						// 确保包含必要字段
						if (!fullData.source) {
							fullData.source = sourceId
						} else if (typeof fullData.source === 'object' && fullData.source.id) {
							fullData.source = fullData.source.id
						}
						if (!fullData.sourceId && fullData.id) {
							fullData.sourceId = fullData.id
						}

						// 匹配作物
						if (!fullData.cropIds) {
							// 检查配置是否有默认作物ID
							if (config.defaultCropId) {
								fullData.cropIds = config.defaultCropId
							} else {
								// 安全地构建文本数组，过滤空值
							const textArray = [fullData.title, fullData.content].filter(text => text != null && text !== '')
							fullData.cropIds = this.matchCropByText(crops, textArray)
							}
						}

						// 处理图片和文件上传
						if (fullData.photos) {
							fullData.photos = await this.processMediaUrls(fullData.photos)
						}
						if (fullData.files) {
							fullData.files = await this.processMediaUrls(fullData.files)
						}

						// 保存到数据库
						await this.addInfoWithoutCheck(fullData)
						addCount++
						console.log(`${fullData.title}: 成功录入`)

						// 添加延迟
						await this.delay(200)
					} else {
						ignoreCount++
						console.log(`${item.title}: 已存在，忽略`)
					}
				} catch (error) {
					errorCount++
					console.error(`处理数据失败:`, error.message)
				}
			}

			return {
				total: allData.length,
				success: addCount,
				fail: errorCount,
				ignore: ignoreCount
			}
		} catch (error) {
			throw new Error(`更新数据源${sourceId}失败: ${error.message}`)
		}
	}

	/**
	 * 根据文本匹配作物
	 * @param {Array} cropArr - 作物数组
	 * @param {Array} textArr - 文本数组
	 * @returns {string} - 作物ID字符串
	 */
	matchCropByText(cropArr, textArr) {
		// 参数验证
		if (!Array.isArray(cropArr) || !Array.isArray(textArr)) {
			return ''
		}

		const idArr = []
		for (const crop of cropArr) {
			// 验证crop对象和name属性
			if (!crop || !crop.name || typeof crop.name !== 'string') continue
			
			const { id, name } = crop
			for (const text of textArr) {
				if (text && typeof text === 'string' && text.includes(name) && !idArr.includes(id)) {
					idArr.push(id)
				}
			}
		}
		return idArr.join(',')
	}

	/**
	 * 处理媒体URL（上传到OSS）
	 * @param {string|Object} mediaUrls - 媒体URL或URL对象
	 * @returns {Promise<string>} - 处理后的URL
	 */
	async processMediaUrls(mediaUrls) {
		try {
			if (typeof mediaUrls === 'string') {
				return await ossController.uploadToOSS(mediaUrls, 'info')
			} else if (typeof mediaUrls === 'object') {
				const processed = {}
				for (const [key, url] of Object.entries(mediaUrls)) {
					processed[key] = await ossController.uploadToOSS(url, 'info')
				}
				return JSON.stringify(processed)
			}
			return mediaUrls
		} catch (error) {
			console.error('处理媒体URL失败:', error.message)
			return mediaUrls
		}
	}

	/**
	 * 添加信息到数据库（无检查版本）
	 * @param {Object} infoData - 信息数据
	 * @returns {Promise} - 添加结果
	 */
	/**
	 * 添加信息数据（带重复检查）
	 * @param {Object} infoData - 信息数据
	 * @returns {Promise} - 添加结果
	 */
	async addInfoWithoutCheck(infoData) {
		// 检查是否已存在相同的文章
		if (infoData.originalLink) {
			const existingCount = await dbController.countId(INFORMATION, {
				originalLink: infoData.originalLink
			})
			if (existingCount > 0) {
				logger.debug(`跳过重复文章: ${infoData.title}`)
				return { skipped: true, reason: 'duplicate' }
			}
		}

		const data = getKeysObj(infoData, this.infoTableKeys)
		try {
			const result = await dbController.add(INFORMATION, data)
			logger.debug(`成功添加文章: ${infoData.title}`)
			return { success: true, id: result.insertId || result.id }
		} catch (error) {
			// 如果是重复键错误，返回跳过状态
			if (error.code === 'ER_DUP_ENTRY' || error.message.includes('duplicate')) {
				logger.debug(`跳过重复文章(数据库约束): ${infoData.title}`)
				return { skipped: true, reason: 'database_constraint' }
			}
			throw error
		}
	}

	/**
	 * 延迟执行
	 * @param {number} ms - 延迟毫秒数
	 * @returns {Promise} - Promise对象
	 */
	delay(ms) {
		return new Promise(resolve => setTimeout(resolve, ms))
	}

	/**
	 * 获取支持的数据源列表
	 * @returns {Array} - 数据源列表
	 */
	getSupportedSources() {
		return this.factory.getSupportedSources()
	}

	/**
	 * 执行健康检查
	 * @returns {Promise<Object>} - 健康检查结果
	 */
	async performHealthCheck() {
		try {
			return await healthChecker.checkAll()
		} catch (error) {
			logger.error('健康检查失败', { error: error.message })
			return {
				database: { accessible: false, error: error.message },
				network: { accessible: false, error: error.message }
			}
		}
	}

	/**
	 * 批量更新多个数据源
	 * @param {Array} sourceIds - 数据源ID数组
	 * @param {Object} options - 选项
	 * @returns {Promise<Object>} - 批量更新结果
	 */
	async batchUpdateSources(sourceIds, options = {}) {
		const results = {}

		for (const sourceId of sourceIds) {
			try {
				console.log(`开始更新数据源 ${sourceId}...`)
				results[sourceId] = await this.updateLatestData(sourceId, options)
				console.log(`数据源 ${sourceId} 更新完成:`, results[sourceId])
			} catch (error) {
				results[sourceId] = { error: error.message }
				console.error(`数据源 ${sourceId} 更新失败:`, error.message)
			}
		}

		return results
	}

	/**
	 * 从URL中提取ID
	 * @param {string} url - URL地址
	 * @param {RegExp} pattern - 匹配模式，可选
	 * @returns {string|null} - 提取的ID
	 */
	extractIdFromUrl(url, pattern) {
		if (!url) return null

		try {
			// 使用传入的模式或默认模式
			const regex = pattern || /\/(\d+)\.html?$/
			const match = url.match(regex)
			return match ? match[1] : null
		} catch (error) {
			logger.warn(`从URL提取ID失败: ${url}`, { error: error.message })
			return null
		}
	}
}

module.exports = new ScraperManager()
