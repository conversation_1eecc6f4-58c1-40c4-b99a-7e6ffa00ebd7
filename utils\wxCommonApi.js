const sha1 = require("sha1");

const httpUtil = require('./httpUtil');
const cacheUtil = require('./cacheUtil');
const wxAppInfos = require('../wxApp.infos');
const WXBizDataCrypt = require('./WXBizDataCrypt');
const { SERVICE_ORDER_CHANGE_USER_NOTICE, SERVICE_ORDER_CHANGE_BUYER_NOTICE, MINI_ORDER_CHANGE_NOTICE } = require('../constant/messageTemplate')
const { NZ_SERVICE, NZ_BUYER_SERVICE, NZ_MINI } = require('../constant/appName')


// 获取微信 app 的服务端的 accessToken;
function fetchAccessToken (appName) {
    return new Promise((resolve, reject) => {
        let accessToken = cacheUtil.get(`ACCESS_TOKEN_${appName}`);
        if (accessToken) {
            resolve(accessToken);
        } else {
            requestAccessToken(appName).then(accessToken => {
                resolve(accessToken);
            }).catch(err => {
                console.log(err)
                reject(err)
            })
        }
    })
}

// 查询小程序的服务端的 accessToken;
// znyMini | nzMini | nzService | nzBuyerService | merchant
function requestAccessToken (appName) {
    const { appId, secret } = wxAppInfos[appName];
    const url = `https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=${appId}&secret=${secret}`;
    return new Promise(function (resolve, reject) {
        httpUtil.get(url).then(res => {
            const { access_token, expires_in, errmsg } = res;
            if (access_token) {
                cacheUtil.set(`ACCESS_TOKEN_${appName}`, access_token, expires_in - 10);
                resolve(access_token);
            } else {
                reject(errmsg);
            }
        }).catch(err => {
            reject(err);
        })
    });
}

// 获取微信 公众号的 js_api_ticket;
// https://developers.weixin.qq.com/doc/offiaccount/OA_Web_Apps/JS-SDK.html#62
function fetchJsApiTicket (appName) {
    return new Promise(function (resolve, reject) {
        const cacheTicket = cacheUtil.get(`JS_API_TICKET_${appName}`);
        if (cacheTicket) {
            resolve(cacheTicket);
        } else {
            fetchAccessToken(appName).then(accessToken => {
                httpUtil.get(`https://api.weixin.qq.com/cgi-bin/ticket/getticket?access_token=${accessToken}&type=jsapi`).then(res => {
                    const { ticket, expires_in, errmsg } = res;
                    if (errmsg === 'ok' && ticket) {
                        cacheUtil.set(`JS_API_TICKET_${appName}`, ticket, expires_in - 10);
                        resolve(ticket);
                    } else {
                        reject(errmsg);
                    }
                }).catch(err => {
                    reject(err);
                })
            }).catch(err => {
                reject(err);
            })
        }
    });
}

// 获取网页端调用微信接口的签名
function requestJsSign (appName, url, nonceStr, timestamp) {
    return fetchJsApiTicket(appName).then(ticket => {
        const signature = sha1(`jsapi_ticket=${ticket}&noncestr=${nonceStr}&timestamp=${timestamp}&url=${url}`);
        return signature;
    })
}

// 获取微信公众号的用户 access_token,并查询用户完整信息
// https://developers.weixin.qq.com/doc/offiaccount/OA_Web_Apps/Wechat_webpage_authorization.html#1
// znyMini | nzMini | nzService | nzBuyerService | merchant
function requestServiceUser (appName, code) {
    const { appId, secret } = wxAppInfos[appName];
    return new Promise(function (resolve, reject) {
        const url = `https://api.weixin.qq.com/sns/oauth2/access_token?appid=${appId}&secret=${secret}&code=${code}&grant_type=authorization_code`;
        httpUtil.get(url).then(res => {// 获取用户的 openid
            const { openid, errmsg } = res;
            if (openid) {
                fetchAccessToken(appName).then(accessToken => {
                    // https://developers.weixin.qq.com/doc/offiaccount/User_Management/Get_users_basic_information_UnionID.html#UinonId
                    const url2 = `https://api.weixin.qq.com/cgi-bin/user/info?access_token=${accessToken}&openid=${openid}&lang=zh_CN`;
                    httpUtil.get(url2).then(res => {// 获取用户的 信息
                        const { errmsg } = res;
                        if (errmsg) {
                            reject(errmsg);
                        } else {
                            resolve(res);
                        }
                    }).catch(err => {
                        reject(err);
                    })
                }).catch(err => {
                    reject(err);
                })
            } else {
                reject(errmsg);
            }
        }).catch(err => {
            reject(err);
        })
    })
}

// 获取小程序的用户信息
// https://developers.weixin.qq.com/miniprogram/dev/api-backend/open-api/login/auth.code2Session.html
function requestSessionData (appName, code) {
    const { appId, secret } = wxAppInfos[appName];
    const url = `https://api.weixin.qq.com/sns/jscode2session?appid=${appId}&secret=${secret}&js_code=${code}&grant_type=authorization_code`;
    return new Promise(function (resolve, reject) {
        httpUtil.get(url).then(res => {
            // openid	string	用户唯一标识
            // session_key	string	会话密钥
            // unionid	string	用户在开放平台的唯一标识符，在满足 UnionID 下发条件的情况下会返回，详见 UnionID 机制说明。
            // errcode	number	错误码
            // errmsg	string	错误信息
            const { openid, session_key, errmsg } = res;
            if (openid && session_key) {
                resolve(res);
            } else {
                reject(errmsg);
            }
        }).catch(err => {
            reject(err);
        })
    });
}

// 获取小程序用户的手机号
// https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/user-info/phone-number/getPhoneNumber.html#%E5%8A%9F%E8%83%BD%E6%8F%8F%E8%BF%B0
function requestWxPhone (appName, code) {
    return new Promise(function (resolve, reject) {
        fetchAccessToken(appName).then(accessToken => {
            const url = `https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=${accessToken}`
            httpUtil.postJson(url, { code }).then(res => {
                resolve(res.phone_info)
            }).catch(err => reject(err))
        }).catch(err => reject(err))
    })
}


/**
 *  获取微信敏感数据
 *
 * @param {string} appName 小程序名称
 * @param {string} code
 * @param {string} encryptedData // 微信小程序端授权获取到的加密数据包
 * @param {string} iv // 加密算法的初始向量
 * @returns
 */
function requestWxEncryptedData (appName, code, encryptedData, iv) {
    return requestSessionData(appName, code).then(sessionData => {
        const { openid, session_key, unionid } = sessionData;
        if (encryptedData, iv) {
            const { appId } = wxAppInfos[appName];
            try {
                let data = decryptWxData(appId, session_key, encryptedData, iv);
                return data;
            } catch (error) {
                console.log(error)
            }
            // return decryptWxData(appId, session_key, encryptedData, iv);
        } else {
            return { openId: openid, unionId: unionid };
        }
    })
}

// 解密微信数据包
function decryptWxData (appId, sessionKey, encryptedData, iv) {
    const pc = new WXBizDataCrypt(appId, sessionKey);
    const decodeData = pc.decryptData(encryptedData, iv)
    return decodeData;
}

// 腾讯地图逆地址解析
// https://lbs.qq.com/service/webService/webServiceGuide/webServiceGcoder
function geocoderAddress (latitude, longitude) {
    return new Promise((resolve, reject) => {
        const url = `https://apis.map.qq.com/ws/geocoder/v1/?location=${latitude},${longitude}&key=S56BZ-S7F3U-4OFVW-BA3RO-CGOI5-I4FY5`;
        httpUtil.get(url).then(res => {
            const { status, message, result } = res;
            if (status === 0) {
                resolve(result)
            } else {
                reject(message);
            }
        }).catch(err => {
            reject(err);
        })
    })
}

// 给用户发送订单变化通知
// https://developers.weixin.qq.com/doc/offiaccount/Subscription_Messages/api.html#send%E5%8F%91%E9%80%81%E8%AE%A2%E9%98%85%E9%80%9A%E7%9F%A5
function sendServiceOrderChangeMessage (openId, orderId, orderContent, orderState, remarks, messageUrl, miniPath) {
    return new Promise((resolve, reject) => {
        fetchAccessToken(NZ_SERVICE).then(accessToken => {
            const url = `https://api.weixin.qq.com/cgi-bin/message/subscribe/bizsend?access_token=${accessToken}`;
            let postData = {
                touser: openId,
                template_id: SERVICE_ORDER_CHANGE_USER_NOTICE,
                data: {
                    character_string1: { value: orderId },
                    thing9: { value: orderContent },
                    phrase3: { value: orderState },
                    thing8: { value: remarks }
                },
                page: messageUrl
            };
            if (miniPath) {
                postData.miniprogram = {
                    appid: wxAppInfos.nzMini.appId,
                    pagepath: miniPath
                }
            };
            httpUtil.postJson(url, postData).then(res => {
                const { errmsg } = res;
                if (errmsg === 'ok') {
                    resolve('消息发送成功');
                } else {
                    reject(errmsg);
                }
            }).catch(err => {
                reject(err);
            });
        }).catch(err => {
            reject(err);
        })
    })
}

// 给用户发送订单变化通知
// https://developers.weixin.qq.com/miniprogram/dev/api-backend/open-api/subscribe-message/subscribeMessage.send.html
function sendMiniOrderChangeMessage (miniOpenId, orderId, orderContent, orderState, remarks, miniPath) {
    return new Promise((resolve, reject) => {
        fetchAccessToken(NZ_MINI).then(accessToken => {
            const url = `https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=${accessToken}`;
            let postData = {
                touser: miniOpenId,
                template_id: MINI_ORDER_CHANGE_NOTICE,
                page: miniPath,
                data: {
                    character_string1: { value: orderId },
                    thing9: { value: orderContent },
                    phrase3: { value: orderState },
                    thing5: { value: remarks }
                }
                // miniprogram_state | 跳转小程序类型：developer为开发版；trial为体验版；formal为正式版；默认为正式版
            };
            httpUtil.postJson(url, postData).then(res => {
                const { errmsg } = res;
                if (errmsg === 'ok') {
                    resolve('消息发送成功');
                } else {
                    reject(errmsg);
                }
            }).catch(err => {
                reject(err);
            });
        }).catch(err => {
            reject(err);
        })
    })
}

// 给商贩发送订单有更新的通知
// https://developers.weixin.qq.com/doc/offiaccount/Subscription_Messages/api.html#send%E5%8F%91%E9%80%81%E8%AE%A2%E9%98%85%E9%80%9A%E7%9F%A5
function sendOrderMessageToBuyer (openId, orderId, orderContent, orderState, remarks, messageUrl) {
    return new Promise((resolve, reject) => {
        fetchAccessToken(NZ_BUYER_SERVICE).then(accessToken => {
            const url = `https://api.weixin.qq.com/cgi-bin/message/subscribe/bizsend?access_token=${accessToken}`;
            let postData = {
                touser: openId,
                template_id: SERVICE_ORDER_CHANGE_BUYER_NOTICE,
                data: {
                    character_string1: { value: orderId },
                    thing9: { value: orderContent },
                    phrase3: { value: orderState },
                    thing8: { value: remarks }
                },
                page: messageUrl
            };
            httpUtil.postJson(url, postData).then(res => {
                const { errmsg } = res;
                if (errmsg === 'ok') {
                    resolve('消息发送成功');
                } else {
                    reject(errmsg);
                }
            }).catch(err => {
                reject(err);
            });
        }).catch(err => {
            reject(err);
        })
    })
}


module.exports = {
    fetchAccessToken,
    requestAccessToken,
    fetchJsApiTicket,
    requestJsSign,
    requestServiceUser,
    requestWxPhone,
    requestWxEncryptedData,
    geocoderAddress,
    sendServiceOrderChangeMessage,
    sendMiniOrderChangeMessage,
    sendOrderMessageToBuyer
};