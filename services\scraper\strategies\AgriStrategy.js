const ScraperStrategy = require('../ScraperStrategy')
const { INFO_SOURCE } = require('../../../constant/enum')

/**
 * 中国农业农村信息网爬虫策略
 * 处理供需形势分析等数据
 */
class AgriStrategy extends ScraperStrategy {
	constructor(config) {
		super(config)
		this.sourceId = INFO_SOURCE.ChineseAgriculturalSupplyAndDemandEstimates.id
	}

	getStrategyName() {
		return 'AgriStrategy'
	}

	getSourceId() {
		return this.sourceId
	}

	async scrapeListPage(params) {
		const { page = 0, category } = params
		const results = []

		for (const categoryConfig of this.config.categories) {
			for (const htmlName of this.config.pages) {
				if (page > 0 && htmlName !== 'index.htm') continue // 非首页请求只抓取首页

				const url = `${this.config.baseUrl}/${categoryConfig.type}/${htmlName}`

				try {
					const response = await this.fetchWithRetry(url)
					const $ = this.parseHtml(response.data)

					$(this.config.selectors.listContainer).each((index, element) => {
						const title = $(element).find(this.config.selectors.titleSelector).text().trim()

						// 过滤特定类型的文章
						if (categoryConfig.type === 'jcyj' && !title.includes('供需形势分析月报')) {
							return
						}

						const link = $(element).find(this.config.selectors.linkSelector).attr('href')
						if (!link) return

						const sourceId = this.extractSourceIdFromLink(link)
						const originalLink = `${this.config.baseUrl}/${categoryConfig.type}${link.substring(1)}`
						const summary = $(element).find(this.config.selectors.summarySelector).text().trim()
						const publishTime = $(element).find(this.config.selectors.dateSelector).text().trim()

						results.push(
							this.normalizeData({
								title,
								originalLink,
								summary,
								sourceId,
								publishTime,
								category: categoryConfig.name
							})
						)
					})
				} catch (error) {
					// 单个URL失败不影响整体抓取，记录警告继续处理
					this.safeExtract(() => {
						throw error
					}, `抓取${url}`, null)
				}
			}
		}

		return results
	}

	async scrapeDetailPage(url) {
		try {
			const response = await this.fetchWithRetry(url)
			const $ = this.parseHtml(response.data)

			const author = $(this.config.selectors.authorSelector)
				.last()
				.text()
				.replace(/来源：/, '')
				.trim()

			const content = $(this.config.selectors.contentSelector).html()

			// 提取PDF文件链接
			let htmlFiles = {}
			const links = $('.ArticleDetails').find('a')

			for (let i = 0; i < links.length; i++) {
				const element = links[i]
				const href = $(element).attr('href')
				const text = $(element).text()

				if (href && href.toLowerCase().endsWith('.pdf')) {
					const absoluteSrc = this.convertToAbsoluteUrl(href, url)
					htmlFiles[text] = absoluteSrc
				}
			}

			// 提取加粗文本用于作物匹配
			const strongTextArr = []
			$(this.config.selectors.contentSelector + ' b').each((index, element) => {
				const strongText = $(element).text().trim()
				if (strongText) {
					strongTextArr.push(strongText)
				}
			})

			return {
				author,
				content,
				files: Object.keys(htmlFiles).length > 0 ? JSON.stringify(htmlFiles) : null,
				strongTextArr // 用于作物匹配
			}
		} catch (error) {
			throw new Error(`抓取详情页${url}失败: ${error.message}`)
		}
	}

	/**
	 * 从链接中提取sourceId
	 * @param {string} link - 链接地址
	 * @returns {number|null} - sourceId或null
	 */
	extractSourceIdFromLink(link) {
		if (!link || typeof link !== 'string') return null
		
		try {
			const parts = link.split('/')
			if (parts.length === 0) return null
			
			const fileName = parts[parts.length - 1]
			const fileParts = fileName.split('_')
			if (fileParts.length < 2) return null
			
			const idPart = fileParts[1]
			const id = parseInt(idPart)
			return isNaN(id) ? null : id
		} catch (error) {
			return null
		}
	}
}

module.exports = AgriStrategy
