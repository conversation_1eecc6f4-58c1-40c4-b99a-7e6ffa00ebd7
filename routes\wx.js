// 由微信服务端发起请求处理模块
const express = require('express');
const router = express.Router();
const sha1 = require('sha1');
const resSend = require('../utils/resSend')
const { NZ_SERVICE_TOKEN, NZ_SERVICE_AES_KEY, NZ_BUYER_SERVICE_TOKEN, NZ_BUYER_SERVICE_AES_KEY } = require('../constant/serviceConfig')

//检查请求是否来源于农舟微信服务端
function matchNzServiceSignature(req, res, next){
    let { signature, timestamp, nonce }  = req.query;
    let str = [NZ_SERVICE_TOKEN, timestamp, nonce].sort().join('');
    let shaStr = sha1(str);//加密微信数据
    if(shaStr === signature){//属于微信过来的请求
        next();
    }else{
        resSend(res, 'No access', 0)
    }
}

//检查请求是否来源于农舟商贩微信服务端
function matchNzBuyerServiceSignature(req, res, next){
    let { signature, timestamp, nonce }  = req.query;
    let str = [NZ_BUYER_SERVICE_TOKEN, timestamp, nonce].sort().join('');
    let shaStr = sha1(str);//加密微信数据
    if(shaStr === signature){//属于微信过来的请求
        next();
    }else{
        res.send('No access')
    }
}

router.all('/nzService', matchNzServiceSignature);
router.all('/nzService/*', matchNzServiceSignature);
router.all('/nzBuyerService', matchNzBuyerServiceSignature);
router.all('/nzBuyerService/*', matchNzBuyerServiceSignature);

router.get('/nzService', function(req, res) {
    res.send(req.query.echostr);
})

router.get('/nzBuyerService', function(req, res) {
    res.send(req.query.echostr);
})

module.exports = router;