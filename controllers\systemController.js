const dbController = require('./dbController')
const { resFilter } = require('../utils/filter')
const { CONFIG } = require('../db').tableNames

function queryConfig() {
	return new Promise(function (resolve, reject) {
		dbController.query(CONFIG, { id: 1 }, null, (err, rows) => {
			if (err) {
				reject(err)
			} else {
				resolve(rows.map(item => resFilter(item)))
			}
		})
	})
}

function updateConfig(reqBody) {
	return new Promise(function (resolve, reject) {
		const {
			groupQrCode,
			version,
			qrCodeNoRepeatHour,
			qrCodeShowAuto,
			qrCodeShowCountMax,
			showInfoModule,
			shareValidDays,
			assistancePoints
		} = reqBody
		dbController.update(
			CONFIG,
			{ id: 1 },
			{
				groupQrCode,
				version,
				qrCodeNoRepeatHour,
				qrCodeShowAuto,
				qrCodeShowCountMax,
				showInfoModule,
				shareValidDays,
				assistancePoints
			},
			(err, rows) => {
				if (err) {
					reject(err)
				} else {
					resolve('系统配置更新成功')
				}
			}
		)
	})
}

module.exports = {
	queryConfig,
	updateConfig
}
