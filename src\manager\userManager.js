import http from '@/utils/http'
import storeManager from '@/utils/storeManager'
const managerKey = 'zny_user_manager'

// 用户登录时缓存用户登录信息，下次可以直接不用输入
function rememberAdmin(mobile, password) {
	storeManager.store(
		'login_remember',
		manager<PERSON><PERSON>,
		{
			mobile,
			password
		},
		'1000d'
	) //让这个缓存长时间有效
}

// 获取本地缓存用户登录信息
function getRememberAdmin() {
	const data = storeManager.get('login_remember', manager<PERSON>ey)
	return new Promise(resolve => {
		if (data) {
			resolve(data)
		}
	}).catch(err => err)
}

function queryUsers({ mobile, name, address, type, pageIndex = 0, pageSize = 10 } = {}) {
	return http.post('admin/user/load', {
		mobile,
		name,
		address,
		type,
		pageIndex,
		pageSize
	})
}

function updateUser(id, params) {
	return http.put('admin/user/update/' + id, params)
}

function loadSystemUsers() {
	return http.get('admin/system/user/load')
}

export default {
	rememberAdmin,
	getRememberAdmin,
	queryUsers,
	updateUser,
	loadSystemUsers
}
