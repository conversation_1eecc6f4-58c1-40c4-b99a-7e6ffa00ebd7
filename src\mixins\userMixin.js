import defaultPhoto from '@/images/user-default.png'
export default{
    computed:{
        adminPhoto(){
            if (
							this.$store.state.auth.admin &&
							this.$store.state.auth.admin.contact &&
							this.$store.state.auth.admin.contact.avatar
						) {
							return this.$store.state.auth.admin.contact.avatar
						} else {
							return defaultPhoto
						}
        },
        adminName(){
            if (
							this.$store.state.auth.admin &&
							this.$store.state.auth.admin.contact &&
							this.$store.state.auth.admin.contact.name
						) {
							return this.$store.state.auth.admin.contact.name
						} else {
							return '管理员'
						}
        }
    }
}