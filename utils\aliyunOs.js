const OSS = require('ali-oss')
const axios = require('axios')

// 建议: 将 bucket 名称移至配置文件
const BUCKET_NAME = 'qwc-prd'

/**
 * 生成用于上传的签名 URL。
 * @param {string} fileName - 在 OSS 中的完整对象名称（路径）。
 * @param {string} contentType - 要上传的文件的 MIME 类型。
 * @returns {string} 返回可用于 PUT 请求的签名 URL。
 */
function getSignedUrl(fileName, contentType) {
	const client = new OSS({
		region: 'oss-cn-hangzhou',
		accessKeyId: process.env.OSS_ACCESS_KEY_ID,
		accessKeySecret: process.env.OSS_ACCESS_KEY_SECRET,
		bucket: BUCKET_NAME
	})

	// 签名 URL 的 Content-Type 必须与实际上传时使用的 Content-Type 完全匹配。
	const url = client.signatureUrl(fileName, {
		method: 'PUT',
		'Content-Type': contentType
	})
	return url
}

/**
 * 使用签名 URL 将文件（流或缓冲区）上传到 OSS。
 * @param {import('stream').Readable | Buffer} file - 文件内容，可以是可读流或缓冲区。
 * @param {string} name - 在 OSS 中的完整对象名称（路径）。
 * @param {number} fileSize - 文件的大小（字节），用于 Content-Length 头。
 * @param {string} contentType - 文件的 MIME 类型，用于 Content-Type 头。
 * @returns {Promise<void>} 上传成功时解析，失败时拒绝。
 */
async function addFileToBucket(file, name, fileSize, contentType) {
	// 1. 获取带有正确 Content-Type 的签名 URL
	const signedUrl = getSignedUrl(name, contentType)

	// 2. 使用签名 URL 和流上传文件
	try {
		await axios({
			url: signedUrl,
			method: 'PUT',
			data: file,
			headers: {
				'Content-Type': contentType,
				'Content-Length': fileSize
			},
			// 对于流式上传，设置 maxBodyLength 和 maxContentLength
			maxBodyLength: Infinity,
			maxContentLength: Infinity
		})
	} catch (error) {
		// 提供更详细的错误信息
		const errorMessage = error.response ? JSON.stringify(error.response.data) : error.message
		throw new Error(`使用签名 URL 上传失败: ${errorMessage}`)
	}
}

module.exports = {
	addFileToBucket
}
