import http from '@/utils/http';

function queryOthers (date, pageIndex, pageSize) {
    return http.post('admin/priceCompany/priceOther/load', { date, pageIndex, pageSize })
}

function addOther (port1, port2, carCount, date) {
    return http.post('admin/priceCompany/other/add', { port1, port2, carCount, date })
}

function updateOther (id, port1, port2, carCount, date) {
    return http.put('admin/priceCompany/other/update/' + id, { port1, port2, carCount, date })
}

export default {
    queryOthers,
    addOther,
    updateOther
}