const ScraperStrategy = require('../ScraperStrategy')
const { INFO_SOURCE } = require('../../../constant/enum')

/**
 * 中国大豆产业协会爬虫策略
 */
class ChinaSoybeanStrategy extends ScraperStrategy {
	constructor(config) {
		super(config)
		this.sourceId = INFO_SOURCE.ChinaSoybean.id
	}

	getStrategyName() {
		return 'ChinaSoybeanStrategy'
	}

	getSourceId() {
		return this.sourceId
	}

	async scrapeListPage(params) {
		const { url, page = 0, category } = params
		const queryUrl = `${url}&page=${page + 1}`
		const fullUrl = `${this.config.baseUrl}/${queryUrl}`

		try {
			const response = await this.fetchWithRetry(fullUrl)
			const $ = this.parseHtml(response.data)
			const results = []

			$(this.config.selectors.listContainer).each((index, element) => {
				const title = $(element).find(this.config.selectors.titleSelector).text().trim()
				const relativeLink = $(element).find(this.config.selectors.linkSelector).attr('href')

				if (!relativeLink) return

				const originalLink = this.convertToAbsoluteUrl(relativeLink, this.config.baseUrl)
				const sourceId = this.extractSourceIdFromUrl(originalLink)
				const publishTime = $(element).find(this.config.selectors.dateSelector).text().trim()

				if (sourceId) {
					results.push(
						this.normalizeData({
							title,
							originalLink,
							sourceId: parseInt(sourceId),
							publishTime,
							category
						})
					)
				}
			})

			return results
		} catch (error) {
			throw new Error(`抓取列表页${fullUrl}失败: ${error.message}`)
		}
	}

	async scrapeDetailPage(url) {
		try {
			const response = await this.fetchWithRetry(url)
			const $ = this.parseHtml(response.data)

			const content = $(this.config.selectors.contentSelector).html()
			const author = $(this.config.selectors.authorSelector)
				.last()
				.text()
				.replace(/来源：/, '')
				.trim()

			return { content, author }
		} catch (error) {
			throw new Error(`抓取详情页${url}失败: ${error.message}`)
		}
	}

	/**
	 * 从URL中提取sourceId
	 * @param {string} url - URL地址
	 * @returns {string|null} - 提取的ID
	 */
	extractSourceIdFromUrl(url) {
		try {
			const urlObj = new URL(url)
			return urlObj.searchParams.get('id')
		} catch (error) {
			return null
		}
	}
}

module.exports = ChinaSoybeanStrategy
