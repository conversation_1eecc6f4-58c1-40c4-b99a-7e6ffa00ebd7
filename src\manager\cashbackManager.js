import http from '@/utils/http';

function queryCashback (pageIndex, pageSize, state, code, dateFrom, dateTo) {
    return http.post('/api/nz/admin/load/cashback', {
        pageIndex, pageSize, state, code, dateFrom, dateTo
    })
}

function queryCashbackById (id) {
    return http.get('/api/nz/admin/load/cashbackById/' + id)
}

function queryCashbackByIds (idArr) {
    return http.get('/api/nz/admin/load/cashbackByIds/' + idArr.join(','))
}

function confirmCashback (id) {
    return http.put('/api/nz/admin/flow/cashback/confirm/' + id)
}

// 发交易红包
function createTradeCashback (orderId, amount, detail) {
    return http.post('/api/nz/admin/flow/cashback/create/trade/' + orderId, {
        amount, detail
    })
}

//发邀请红包
function createInviteCashback (orderId, amount, detail) {
    return http.post('/api/nz/admin/flow/cashback/create/invite/' + orderId, {
        amount, detail
    })
}

export default {
    queryCashback,
    queryCashbackById,
    queryCashbackByIds,
    confirmCashback,
    createTradeCashback,
    createInviteCashback
}