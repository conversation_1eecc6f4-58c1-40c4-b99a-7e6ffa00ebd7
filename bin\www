const app = require('../app');
const debug = require('debug')('express-backend:server');
const http = require('http');

// 使用环境变量或默认值
const port = normalizePort(process.env.PORT || '3000');

function normalizePort (val) {
  const port = parseInt(val, 10);
  if (isNaN(port)) return val;
  return port >= 0 ? port : false;
}

// 创建 HTTP 服务器（仅用于本地开发）
const server = http.createServer(app);

server.listen(port, '127.0.0.1', () => {
  const addr = server.address();
  const bind = typeof addr === 'string' ? 'pipe ' + addr : 'port ' + addr.port;
  debug('Server listening on ' + bind);
});

server.on('error', (err) => {
  if (err.syscall !== 'listen') throw err;
  const bind = typeof port === 'string' ? 'Pipe ' + port : 'Port ' + port;
  switch (err.code) {
    case 'EACCES':
      console.error(bind + ' requires elevated privileges');
      process.exit(1);
      break;
    case 'EADDRINUSE':
      console.error(bind + ' is already in use');
      process.exit(1);
      break;
    default:
      throw err;
  }
});