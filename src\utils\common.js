export function getImage(url){
    if(url){
        if(url.substring(0,4) === 'http'){
            return url;
        }else{
            return 'https://nongyimai.com' + url;
        }
    }else{
        return null;
    }
}

export function getCookie(name){
    let cookieArr = document.cookie.split('; ');//分割
    //遍历匹配
    for ( let i = 0; i < cookieArr.length; i++) {
        let arr = cookieArr[i].split('=');
        if (arr[0] == name){
            return arr[1];
        }
    }
    return '';
}

export default {
    getImage,
    getCookie
}