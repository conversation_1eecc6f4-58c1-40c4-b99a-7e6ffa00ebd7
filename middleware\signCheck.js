const { APP_SECRET } = require('../secret')
const md5 = require('md5');
module.exports = function (req, res, next) {
    let signParams = [];
    const ignoreParams = ['nonce', 'timestamp', 'platform', 'userId', 'signature']
    const { nonce, timestamp, signature } = req.query;
    const now = Date.now();
    if (!nonce || !timestamp || !signature || now - timestamp > 30000) {
        res.send({
            code: 0,
            message: '请求参数异常'
        })
        return
    }
    if (['GET', 'DELETE'].includes(req.method)) {
        Object.keys(req.query).map(key => {
            if (!ignoreParams.includes(key)) {
                signParams.push({ key, value: req.query[key] })
            }
        })
    }
    if (['POST', 'PUT'].includes(req.method) && req.body) {
        Object.keys(req.body).map(key => {
            if (!ignoreParams.includes(key)) {
                signParams.push({ key, value: req.body[key] })
            }
        })
    }
    // 按 key 倒序排序
    signParams.sort((a, b) => a.key > b.key ? -1 : 1);
    signParams.push({ key: 'nonce', value: nonce })
    signParams.push({ key: 'timestamp', value: timestamp })
    const signStr = signParams.map(item => `${item.key}=${encodeURIComponent(item.value)}`).join('&') + `&${APP_SECRET}`
    const newSignature = md5(signStr);
    if (newSignature === signature) {
        next();
    } else {
        res.send({
            code: 0,
            message: '签名有误，无权访问'
        })
    }
}
