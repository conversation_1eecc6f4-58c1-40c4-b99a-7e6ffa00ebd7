const express = require('express')
const router = express.Router()
const qrcodeController = require('../controllers/qrcodeController')

/**
 * 处理路由请求的通用函数
 * @param {Function} fn - 要执行的函数
 */
const handleRequest = fn => async (req, res) => {
	try {
		const data = await fn(req)
		res.sendSuccess(data)
	} catch (error) {
		res.sendMessage(error)
	}
}

/**
 * 获取二维码详情
 * GET /qrcode/detail/:id
 */
router.get(
	'/detail/:id',
	handleRequest(req => {
		if (!req.params.id) throw new Error('参数有误')
		return qrcodeController.getQrcodeDetail({ id: req.params.id })
	})
)

/**
 * 获取二维码详情 by userId
 * GET /qrcode/detail/user/:userId
 */
router.get(
	'/detail/user/:userId',
	handleRequest(req => {
		if (!req.params.userId) throw new Error('参数有误')
		return qrcodeController.getQrcodeDetail({ userId: req.params.userId })
	})
)

module.exports = router
