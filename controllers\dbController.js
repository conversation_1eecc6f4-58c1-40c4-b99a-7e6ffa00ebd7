const mysql = require('mysql')
const filter = require('../utils/filter')
const pool = mysql.createPool(require('../db').dbConfig)

function parseValue(value) {
	if (value === '' || value === null || value === undefined) {
		return 'null'
	} else if (!isNaN(value)) {
		if (typeof value === 'string' && value[0] === '0') {
			return `"${value}"`
		} else {
			return value
		}
	} else if (value === 'CURRENT_TIMESTAMP') {
		return value
	} else {
		return `"${value}"`
	}
}

//key 跟 value 拼接成sql
function joinSql(key, value, equalString) {
	if (equalString === 'like') {
		// `name` like '%spring%'
		return '`' + filter.toLine(key) + '` like "%' + value + '%"'
	} else {
		return '`' + filter.toLine(key) + '`' + equalString + parseValue(value)
	}
}

//常规查询方法，该方法带特定的sql语句, argsArr中的值按循序替换sql中的?
function dbConnect(sql, argsArr, callback) {
	if (callback) {
		doConnection(callback)
	} else {
		return new Promise(function (resolve, reject) {
			doConnection((err, rows) => {
				if (err) {
					reject(err)
				} else {
					resolve(rows)
				}
			})
		})
	}
	function doConnection(connectCallback) {
		pool.getConnection(function (err, connection) {
			if (err) {
				connectCallback(err, null)
			} else {
				if (argsArr) {
					connection.query(sql, argsArr, function (err, rows) {
						connection.release()
						connectCallback(err, rows)
					})
				} else {
					connection.query(sql, function (err, rows) {
						connection.release()
						connectCallback(err, rows)
					})
				}
			}
		})
	}
}

// options = {
//     'a': 1, //等于
//     'a': [1, 2], //等于其中一个值
//     'a.like': 3, //模糊匹配某个值
//     'a.findIn': 3, //通过逗号分隔的值有一个值等于传递的查询值
//     'a.not': 3, //不等于某个值
//     'a.not': [1, 4], //不等于这两个值,
//     '_special': ''//自定义sql
// }
function buildQuerySql(options) {
	if (!options) {
		return ''
	}
	const sqlArr = []
	for (const key in options) {
		const value = options[key]
		if (value !== undefined && value !== null) {
			if (key === '_special') {
				//自定义的部分 query sql
				sqlArr.push(value)
			} else {
				const typeIndex = key.indexOf('.')
				const isValueArr = Object.prototype.toString.call(value) === '[object Array]'
				let newKey
				if (typeIndex > 0) {
					//包含.not / .like 标记
					newKey = key.substring(0, typeIndex)
				}
				if (isValueArr) {
					if (key.includes('.not')) {
						//不等于value中任意一值
						const newArr = value.map(item => joinSql(newKey, item, '<>'))
						sqlArr.push(`(${newArr.join(' AND ')})`)
					} else if (typeIndex === -1) {
						//等于value中的一个值
						const newArr = value.map(item => joinSql(key, item, '='))
						sqlArr.push(`(${newArr.join(' OR ')})`)
					}
				} else {
					if (key.includes('.like')) {
						sqlArr.push(joinSql(newKey, value, 'like'))
					} else if (key.includes('.not')) {
						//不等于value
						sqlArr.push(joinSql(newKey, value, '<>'))
					} else if (key.includes('.findIn')) {
						//数据库中逗号分隔的值有一个值和 value 相等
						sqlArr.push(`FIND_IN_SET("${value}", ${filter.toLine(newKey)})`)
					} else {
						//等于value
						sqlArr.push(joinSql(key, value, '='))
					}
				}
			}
		}
	}
	if (sqlArr.length > 0) {
		return ' WHERE ' + sqlArr.join(' AND ')
	} else {
		return ''
	}
}

// 往数据库中同时添加多条记录
function addMultiple(tableName, dataArr, callback) {
	const keyArr = []
	const valueArr = []
	const placeholderArr = []
	dataArr.forEach((data, index) => {
		for (const key in data) {
			if (index === 0) {
				keyArr.push('`' + filter.toLine(key) + '`')
				placeholderArr.push('?')
			}
			valueArr.push(data[key])
		}
	})
	const valueString = dataArr.map(() => `(${placeholderArr.join(',')})`).join(',')
	const sql = `INSERT into \`${tableName}\` (${keyArr.join(',')}) values ${valueString}`
	return dbConnect(sql, valueArr, callback)
}

function add(tableName, data, callback) {
	const keyArr = []
	const valueArr = []
	const placeholderArr = []
	for (const key in data) {
		// 单独录入一条数据的时候，忽略空字段的赋值
		if (data[key] !== undefined && data[key] !== null && data[key] !== '') {
			keyArr.push('`' + filter.toLine(key) + '`')
			valueArr.push(data[key])
			placeholderArr.push('?')
		}
	}
	const valueString = placeholderArr.join(',')
	const sql = `INSERT into \`${tableName}\` (${keyArr.join(',')}) values (${valueString})`
	return dbConnect(sql, valueArr, callback)
}

function count(tableName, queryOptions, callback) {
	let sql = `SELECT count(*) FROM \`${tableName}\`` + buildQuerySql(queryOptions)
	return dbConnect(sql, null, callback)
}

// 检测数据库中数据量，对应的表必须存在 id 字段
function countId(tableName, queryOptions, callback) {
	const sql = `SELECT count(id) FROM \`${tableName}\`` + buildQuerySql(queryOptions)
	return dbConnect(sql, null, callback).then(rows => rows[0]['count(id)'])
}

// limitOptions = {
//     orderBy: 'id',
//     orderSort: 'ASC', //DESC 倒序
//     pageIndex: 0,
//     pageSize: 10
// }
function query(tableName, queryOptions, limitOptions, callback) {
	let sql = 'SELECT * FROM `' + tableName + '`' + buildQuerySql(queryOptions)
	if (limitOptions) {
		if (limitOptions.orderBy) {
			if (Array.isArray(limitOptions.orderBy)) {
				const orderClauses = limitOptions.orderBy.map((orderByField, index) => {
					const orderSort = Array.isArray(limitOptions.orderSort)
						? limitOptions.orderSort[index]
						: limitOptions.orderSort
					return '`' + filter.toLine(orderByField) + '` ' + (orderSort || 'DESC')
				})
				sql += ' ORDER BY ' + orderClauses.join(', ')
			} else {
				sql +=
					' ORDER BY `' +
					filter.toLine(limitOptions.orderBy) +
					'` ' +
					(limitOptions.orderSort || 'DESC') // 默认是倒序
			}
		}
		if (limitOptions.pageIndex != undefined && limitOptions.pageSize != undefined) {
			sql += ` LIMIT ${
				parseInt(limitOptions.pageIndex) * parseInt(limitOptions.pageSize)
			},${parseInt(limitOptions.pageSize)}`
		}
	}
	return dbConnect(sql, null, callback)
}

// 根据ID查询数据，直接返回查询到的唯一的一条数据
function queryById(tableName, id) {
	const sql = `SELECT * FROM \`${tableName}\` WHERE id=? LIMIT 1`
	return new Promise(function (resolve, reject) {
		dbConnect(sql, [id], (err, rows) => {
			if (err) {
				reject(err)
			} else if (rows.length === 1) {
				resolve(rows[0])
			} else {
				reject('数据不存在')
			}
		})
	})
}

function deleteById(tableName, id) {
	const sql = `DELETE FROM ${tableName} WHERE id = ?`
	return new Promise(function (resolve, reject) {
		dbConnect(sql, [id], (err, result) => {
			if (err) {
				reject(err)
			} else if (result.affectedRows === 0) {
				reject('数据不存在')
			} else {
				resolve('删除成功')
			}
		})
	})
}

// 查询特定的列，不查询整条记录，需指定需要查询的列
function queryColumns(tableName, columns, queryOptions, limitOptions, callback) {
	if (!columns || columns.length === 0) {
		return Promise.reject('需指定需要查询的列')
	}
	const columnsStr = columns.map(name => filter.toLine(name)).join(',')
	let sql = `SELECT ${columnsStr} FROM \`${tableName}\`` + buildQuerySql(queryOptions)
	if (limitOptions) {
		if (limitOptions.orderBy) {
			sql +=
				' ORDER BY `' +
				filter.toLine(limitOptions.orderBy) +
				'` ' +
				(limitOptions.orderSort || 'DESC') // 默认是倒序
		}
		if (limitOptions.pageIndex != undefined && limitOptions.pageSize != undefined) {
			sql += ` LIMIT ${
				parseInt(limitOptions.pageIndex) * parseInt(limitOptions.pageSize)
			},${parseInt(limitOptions.pageSize)}`
		}
	}
	return dbConnect(sql, null, callback)
}

// 更新表，允许 updateData 传入空值
function update(tableName, queryOptions, updateData, callback) {
	let sql = 'UPDATE `' + tableName + '` SET'
	const setArr = []
	const valueArr = []
	for (const key in updateData) {
		setArr.push(`\`${filter.toLine(key)}\`=?`)
		let newValue = updateData[key]
		if (newValue === undefined) {
			newValue = null
		}
		valueArr.push(newValue)
	}
	sql += ` ${setArr.join(',')}` + buildQuerySql(queryOptions)
	return dbConnect(sql, valueArr, callback)
}

// 更新表，过滤掉 updateData 中的空值
function updateWithoutEmpty(tableName, queryOptions, updateData, callback) {
	for (const key in updateData) {
		!updateData[key] && updateData[key] != 0 && delete updateData[key]
	}
	return update(tableName, queryOptions, updateData, callback)
}

function deleteData(tableName, queryOptions, callback) {
	let sql = 'DELETE FROM `' + tableName + '`'
	sql += buildQuerySql(queryOptions)
	return dbConnect(sql, null, callback)
}

module.exports = {
	dbConnect,
	add,
	addMultiple,
	query,
	queryColumns,
	queryById,
	deleteById,
	count,
	countId,
	update,
	updateWithoutEmpty,
	delete: deleteData
}
