const axios = require('axios')
const dbController = require('./dbController')
const { resFilter } = require('../utils/filter')
const { CITY_CODE } = require('../db').tableNames

// 数据级别定义
const CONFIG = {
	LEVELS: {
		PROVINCE: 1, // 省、直辖市、自治区
		CITY: 2, // 地级市
		AREA: 3, // 区县
		STREET: 4, // 街道乡镇
		VILLAGE: 5 // 村级
	}
}

// 通用批处理执行器
async function executeBatchOperation(data, batchSize, operationName, operationFn) {
	if (!data || data.length === 0) return []

	const batches = []
	for (let i = 0; i < data.length; i += batchSize) {
		batches.push(data.slice(i, i + batchSize))
	}

	console.log(`开始${operationName}，总数：${data.length}，批次：${batches.length}`)

	let results = []
	for (const [index, batch] of batches.entries()) {
		const batchIndex = index + 1
		console.log(`正在处理第 ${batchIndex}/${batches.length} 批次，包含 ${batch.length} 条数据`)

		try {
			const result = await operationFn(batch)
			if (Array.isArray(result)) {
				results = results.concat(result)
			} else {
				results.push(result)
			}
			console.log(`第 ${batchIndex} 批次完成`)
		} catch (error) {
			console.error(`第 ${batchIndex} 批次失败:`, error.message)
		}

		// 批次间延时，避免数据库压力
		if (batchIndex < batches.length) {
			await new Promise(resolve => setTimeout(resolve, 50))
		}
	}

	console.log(`${operationName}完成，处理结果数：${results.length}`)
	return results
}

// 数据验证和清理 - 单一职责
function validateAndCleanAreaData(dataArr) {
	if (!dataArr || dataArr.length === 0) {
		throw new Error('没有数据需要处理')
	}

	const cleanData = dataArr
		.filter(item => item && item[1] && item[2])
		.map(item => ({
			code: item[1].toString(),
			name: item[2].replace(/['"]/g, '').trim(),
			level: parseInt(item[3]) || 1
		}))
		.filter(item => item.code && item.name)

	if (cleanData.length === 0) {
		throw new Error('清理后没有有效的行政区域数据')
	}

	return cleanData
}

// 去重处理 - 单一职责
function removeDuplicates(data) {
	const uniqueData = []
	const seenCodes = new Set()
	let duplicateCount = 0

	for (const item of data) {
		if (seenCodes.has(item.code)) {
			duplicateCount++
		} else {
			seenCodes.add(item.code)
			uniqueData.push(item)
		}
	}

	if (duplicateCount > 0) {
		console.log(`发现并去除内部重复 ${duplicateCount} 条`)
	}

	return { uniqueData, internalDuplicates: duplicateCount }
}

// 从OSS获取数据
async function loadPcaDataFromOSS(ossUrl) {
	try {
		console.log(`开始从OSS获取数据: ${ossUrl}`)
		const response = await axios.get(ossUrl, {
			timeout: 30000,
			responseType: 'json'
		})

		if (!response.data || !Array.isArray(response.data)) {
			throw new Error('OSS数据格式无效，期望数组格式')
		}

		console.log(`成功从OSS加载 ${response.data.length} 个省级行政区划数据`)
		return response.data
	} catch (error) {
		console.error('从OSS获取数据失败:', error)
		throw new Error(`从OSS获取数据失败: ${error.message}`)
	}
}

// 递归解析层级数据并转换为平面数组
function parsePcaHierarchyData(jsonData) {
	try {
		const result = []

		function parseLevel(items, level = 1) {
			if (!Array.isArray(items)) return

			items.forEach(item => {
				if (!item.code || !item.name) return

				result.push([
					'', // parent_code - 暂时为空，后续会填充
					item.code, // code
					item.name, // name
					level // level
				])

				if (item.children && Array.isArray(item.children) && item.children.length > 0) {
					parseLevel(item.children, level + 1)
				}
			})
		}

		parseLevel(jsonData)

		const codeMap = new Map()
		result.forEach(item => {
			codeMap.set(item[1], item)
		})

		result.forEach(item => {
			const code = item[1]
			const level = item[3]

			// 根据中国行政区划代码规则确定父级code
			if (level > 1 && code.length === 6) {
				let parentCode = ''

				if (level === 2) {
					parentCode = code.substring(0, 2) + '0000'
				} else if (level === 3) {
					parentCode = code.substring(0, 4) + '00'
				} else if (level === 4) {
					parentCode = code.substring(0, 6)
				}

				if (parentCode && codeMap.has(parentCode)) {
					item[0] = parentCode
				}
			}
		})

		console.log(`成功解析 ${result.length} 条行政区划数据`)
		return result
	} catch (error) {
		console.error('PCA层级数据解析失败:', error)
		return []
	}
}

// 查询数据库中已存在的代码
async function getExistingCodes(codes) {
	if (!codes || codes.length === 0) return []

	const queryOperation = async batch => {
		const placeholders = batch.map(() => '?').join(',')
		const sql = `SELECT code FROM \`${CITY_CODE}\` WHERE code IN (${placeholders})`
		const rows = await dbController.dbConnect(sql, batch)
		return rows.map(row => row.code)
	}

	try {
		const results = await executeBatchOperation(codes, 500, '查询已存在代码', queryOperation)
		console.log(`最终查询结果：从 ${codes.length} 个代码中找到 ${results.length} 个已存在`)
		return results
	} catch (error) {
		console.error('查询已存在代码失败:', error)
		return []
	}
}

// 批量插入数据
async function batchInsertAreas(newData) {
	if (!newData || newData.length === 0) return 0

	let totalInserted = 0

	const insertOperation = async batch => {
		const result = await dbController.addMultiple(CITY_CODE, batch)
		const insertedRows = result.affectedRows || 0
		totalInserted += insertedRows
		return insertedRows
	}

	await executeBatchOperation(newData, 500, '插入新数据', insertOperation)
	return totalInserted
}

// 简化的批量插入处理流程
async function insertAreasBatch(dataArr) {
	// 1. 数据验证和清理
	const cleanData = validateAndCleanAreaData(dataArr)
	// 2. 去重处理
	const { uniqueData, internalDuplicates } = removeDuplicates(cleanData)
	// 3. 检查数据库中已存在的代码
	console.log('检查数据库中已存在的行政区域代码...')
	const existingCodes = await getExistingCodes(uniqueData.map(item => item.code))
	console.log(`查询到已存在代码数量：${existingCodes.length}`)
	const existingCodeSet = new Set(existingCodes)
	// 4. 分离新数据
	const newData = uniqueData.filter(item => !existingCodeSet.has(item.code))
	const duplicateCount = uniqueData.length - newData.length
	console.log(
		`数据分析结果：总计 ${uniqueData.length} 条，新增 ${newData.length} 条，重复 ${duplicateCount} 条`
	)
	if (newData.length === 0) {
		console.log('所有数据均已存在，跳过插入')
		return {
			totalInserted: 0,
			duplicateCount: duplicateCount + internalDuplicates,
			totalProcessed: cleanData.length
		}
	}
	// 5. 批量插入新数据
	const totalInserted = await batchInsertAreas(newData)
	const result = {
		totalInserted,
		duplicateCount: duplicateCount + internalDuplicates,
		totalProcessed: cleanData.length
	}

	console.log(
		`数据插入完成：处理 ${result.totalProcessed} 条，新增 ${result.totalInserted} 条，跳过重复 ${result.duplicateCount} 条`
	)
	return result
}

// 主处理流程
async function processUploadedAreaData(ossUrl, fileName, fileSize) {
	try {
		console.log('开始处理上传的行政区划数据文件...')

		// 1. 从OSS获取数据
		const jsonData = await loadPcaDataFromOSS(ossUrl)

		// 2. 解析层级数据
		console.log('正在解析行政区划数据结构...')
		const areaData = parsePcaHierarchyData(jsonData)

		if (!areaData || areaData.length === 0) {
			throw new Error('没有解析到有效的行政区划数据')
		}

		console.log(`解析到 ${areaData.length} 条行政区划数据，准备插入数据库...`)

		// 3. 插入数据库
		const result = await insertAreasBatch(areaData)

		// 4. 构建响应
		let message = `数据源: ${fileName} 行政区划数据同步成功：`
		if (result.totalInserted > 0) {
			if (result.duplicateCount > 0) {
				message += `共处理 ${result.totalProcessed} 条，新增 ${result.totalInserted} 条，跳过重复 ${result.duplicateCount} 条`
			} else {
				message += `共处理 ${result.totalProcessed} 条，全部为新增数据`
			}
		} else {
			message += `共处理 ${result.totalProcessed} 条，全部数据已存在，跳过插入`
		}

		console.log('✅ 数据处理流程全部完成')
		return {
			success: true,
			message: message,
			processedRecords: result.totalInserted,
			totalRecords: result.totalProcessed,
			duplicateRecords: result.duplicateCount,
			fileName: fileName,
			fileSize: fileSize
		}
	} catch (error) {
		console.error('数据处理流程失败:', error.message)
		return {
			success: false,
			message: `处理失败: ${error.message}`,
			processedRecords: 0,
			totalRecords: 0,
			duplicateRecords: 0,
			fileName: fileName,
			fileSize: fileSize,
			errorMessage: error.message
		}
	}
}

// 查询全部行政区域数据
async function queryAllAreas(options = {}) {
	try {
		const { level, limit, offset } = options

		const queryOptions = {}
		if (level && typeof level === 'number') {
			queryOptions.level = level
		}

		const limitOptions = {
			orderBy: 'code',
			orderSort: 'ASC'
		}

		if (limit && typeof limit === 'number') {
			const pageSize = limit
			const pageIndex = offset ? Math.floor(offset / pageSize) : 0
			limitOptions.pageIndex = pageIndex
			limitOptions.pageSize = pageSize
		}

		const rows = await dbController.query(CITY_CODE, queryOptions, limitOptions)
		return rows.map(item => resFilter(item))
	} catch (error) {
		console.error('查询行政区域数据失败:', error)
		throw new Error(`查询行政区域数据失败: ${error.message}`)
	}
}

// 根据代码查询行政区域信息
async function queryAreaByCode(code) {
	try {
		const queryOptions = { code }
		const rows = await dbController.query(CITY_CODE, queryOptions)

		if (rows && rows.length > 0) {
			return resFilter(rows[0])
		} else {
			return null
		}
	} catch (error) {
		console.error('查询行政区域数据失败:', error)
		throw new Error(`查询行政区域数据失败: ${error.message}`)
	}
}

// 根据父级代码查询下级区划
async function queryChildrenByParentCode(parentCode, level = null) {
	try {
		const queryOptions = {}
		const limitOptions = {
			orderBy: 'code',
			orderSort: 'ASC'
		}

		if (level && typeof level === 'number') {
			let searchPattern
			switch (level) {
				case 2:
					searchPattern = `${parentCode.substring(0, 2)}%`
					break
				case 3:
					searchPattern = `${parentCode.substring(0, 4)}%`
					break
				default:
					searchPattern = `${parentCode}%`
			}
			queryOptions.level = level
			queryOptions._special = `code LIKE '${searchPattern}' AND code != '${parentCode}'`
		} else {
			queryOptions._special = `code LIKE '${parentCode}%' AND code != '${parentCode}'`
		}

		const rows = await dbController.query(CITY_CODE, queryOptions, limitOptions)
		return rows.map(item => resFilter(item))
	} catch (error) {
		console.error('查询下级区划失败:', error)
		throw new Error(`查询下级区划失败: ${error.message}`)
	}
}

// 获取省份列表
async function getProvinces() {
	return await queryAllAreas({ level: CONFIG.LEVELS.PROVINCE })
}

// 根据省份代码获取城市列表
async function getCitiesByProvince(provinceCode) {
	const provincePrefix = provinceCode.substring(0, 2)
	const queryOptions = {
		level: CONFIG.LEVELS.CITY,
		_special: `code LIKE '${provincePrefix}%'`
	}
	const limitOptions = {
		orderBy: 'code',
		orderSort: 'ASC'
	}

	const rows = await dbController.query(CITY_CODE, queryOptions, limitOptions)
	return rows.map(item => resFilter(item))
}

// 根据城市代码获取区县列表
async function getCountiesByCity(cityCode) {
	const cityPrefix = cityCode.substring(0, 4)
	const queryOptions = {
		level: CONFIG.LEVELS.AREA,
		_special: `code LIKE '${cityPrefix}%'`
	}
	const limitOptions = {
		orderBy: 'code',
		orderSort: 'ASC'
	}

	const rows = await dbController.query(CITY_CODE, queryOptions, limitOptions)
	return rows.map(item => resFilter(item))
}

// 根据区县代码获取街道列表
async function getStreetsByCounty(countyCode) {
	const queryOptions = {
		level: CONFIG.LEVELS.STREET,
		_special: `code LIKE '${countyCode}%'`
	}
	const limitOptions = {
		orderBy: 'code',
		orderSort: 'ASC'
	}

	const rows = await dbController.query(CITY_CODE, queryOptions, limitOptions)
	return rows.map(item => resFilter(item))
}

// 分页查询行政区域数据
async function queryAreasWithPagination(options = {}) {
	try {
		const { level, pageIndex = 1, pageSize = 20, keyword } = options

		const queryOptions = {}
		if (level && typeof parseInt(level) === 'number') {
			queryOptions.level = parseInt(level)
		}

		if (keyword && keyword.trim()) {
			const searchTerm = keyword.trim()
			queryOptions._special = `(name LIKE '%${searchTerm}%' OR code LIKE '%${searchTerm}%')`
		}
		const limitOptions = {
			orderBy: 'code',
			orderSort: 'ASC',
			pageIndex: parseInt(pageIndex),
			pageSize: parseInt(pageSize)
		}

		const rows = await dbController.query(CITY_CODE, queryOptions, limitOptions)
		const list = rows.map(item => resFilter(item))

		const countRows = await dbController.count(CITY_CODE, queryOptions)
		const total = countRows[0]['count(*)']

		return {
			list,
			total,
			pageIndex: parseInt(pageIndex),
			pageSize: parseInt(pageSize)
		}
	} catch (error) {
		console.error('分页查询行政区域失败:', error)
		throw new Error(`分页查询行政区域失败: ${error.message}`)
	}
}

module.exports = {
	processUploadedAreaData,
	queryAllAreas,
	queryAreaByCode,
	queryAreasWithPagination,
	getProvinces,
	getCitiesByProvince,
	getCountiesByCity,
	getStreetsByCounty,
	queryChildrenByParentCode
}
