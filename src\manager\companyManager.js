import http from '@/utils/http'

function queryCompanies(cityCode, cropId, name, pageIndex, pageSize) {
  return http.post('admin/priceCompany/company/load', {
    cityCode,
    cropId,
    name,
    pageIndex,
    pageSize
  })
}

function addCompany(cityCode, cropId, level, name) {
  return http.post('admin/priceCompany/company/add', { cityCode, cropId, level, name })
}

function updateCompany(id, cityCode, cropId, level, name) {
  return http.put('admin/priceCompany/company/update/' + id, { cityCode, cropId, level, name })
}

function queryCompanyLatestDetail(companyId) {
  return http.get(`admin/priceCompany/${companyId}/detailLatest`)
}

function queryCompanyDetails(companyId, cropId, pageIndex, pageSize) {
  return http.post('admin/priceCompany/priceDetail/load', {
    companyId,
    cropId,
    pageIndex,
    pageSize
  })
}

function addCompanyDetail(companyId, cropId, desc, photos) {
  return http.post('admin/priceCompany/addDetail', { companyId, cropId, desc, photos })
}

function updateCompanyDetail(id, companyId, cropId, desc, photos) {
  return http.put('admin/priceCompany/updateDetail/' + id, { companyId, cropId, desc, photos })
}

export default {
  queryCompanies,
  addCompany,
  updateCompany,
  queryCompanyLatestDetail,
  queryCompanyDetails,
  addCompanyDetail,
  updateCompanyDetail
}
