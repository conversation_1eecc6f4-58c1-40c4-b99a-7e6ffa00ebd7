# Ignore build outputs
dist/
build/
.nuxt/
coverage/

# Ignore dependencies
node_modules/
.pnp/
.pnp.js

# Ignore logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Ignore editor directories and files
.vscode/
.idea/
*.swp
*.swo
*~

# Ignore OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Ignore package-lock and yarn lock (optional)
package-lock.json
yarn.lock

# Ignore specific files
*.min.js
*.min.css

# Ignore generated files
*.d.ts

# Ignore markdown files if needed (optional)
# *.md

# Ignore config files that shouldn't be formatted
.browserslistrc
.nvmrc