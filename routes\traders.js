var express = require('express')
var router = express.Router()
const traderController = require('../controllers/traderController')

router.get('/:id', function (req, res) {
	traderController
		.getTraderInfo(req.params.id, req.query)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

router.post('/', function (req, res) {
	const { longitude, latitude } = req.body
	if (longitude && latitude) {
		traderController
			.queryTraders(req.body)
			.then(data => {
				res.sendSuccess(data)
			})
			.catch(err => {
				res.sendMessage(err)
			})
	} else {
		res.sendMessage('参数有误')
	}
})

router.get('/contacted/:id', function (req, res) {
	traderController
		.recordTraderContact(req.body.userId, req.params.id)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

module.exports = router
