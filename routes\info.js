const express = require('express')
const infoController = require('../controllers/infoController')
const router = express.Router()

router.get('/list', async function (req, res) {
	let { title, pageIndex, pageSize, source, cropId } = req.query
	pageIndex = pageIndex || 0
	pageSize = pageSize || 10
	
	try {
		const data = await infoController.getInfoList(pageIndex, pageSize, {
			title,
			source,
			cropId
		})
		res.sendSuccess(data)
	} catch (err) {
		res.sendMessage(err)
	}
})

router.get('/detail/:id', async function (req, res) {
	let { id } = req.params
	
	try {
		const data = await infoController.getInfoById(id)
		res.sendSuccess(data)
	} catch (err) {
		res.sendMessage(err)
	}
})

router.get('/test', function (req, res) {
	res.sendSuccess({
		title: 'deploy test 1'
	})
})

module.exports = router
