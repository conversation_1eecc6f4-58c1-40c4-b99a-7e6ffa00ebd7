const NodeCache = require('node-cache');
const myCache = new NodeCache({ stdTTL: 60 });
const companiesKey = 'CACHE_COMPANIES';
const companyLatestPriceKey = 'CACHE_COMPANY_LATEST_PRICE';
const nzServiceAccessTokenKey = 'NZ_SERVICE_ACCESS_TOKEN';


function set (key, value, ttl) {
    myCache.set(key, value, ttl);
}
function get (key) {
    return myCache.get(key);
}

function getNzServiceAccessToken () {
    return myCache.get(nzServiceAccessTokenKey);
}

function setNzServiceAccessToken (access_token, expires_in) {
    myCache.set(nzServiceAccessTokenKey, access_token, expires_in - 10);// 让服务端access_token有效期比微信服务端有效期短10秒
}

//  拼接缓存某个作物下全部公司的key
function getCropCompaniesKey (cropId) {
    return `${companiesKey}_CROP${cropId}`;
}

//  拼接缓存某个作物下所有公司的最新记录key
function getCropCompanyLatestPriceKey (cropId) {
    return `${companyLatestPriceKey}_CROP${cropId}`;
}

function setCompanies (cropId, value) {
    const key = getCropCompaniesKey(cropId);
    myCache.set(key, value, 120);
}

function getCompanies (cropId) {
    const key = getCropCompaniesKey(cropId);
    return myCache.get(key);
}

//  更新每个公司的最近的一条记录
function updateCompanyLatestPrice (cropPriceItem) {
    const key = getCropCompanyLatestPriceKey(cropPriceItem.crop_id);
    let priceMap = myCache.get(key) || {};
    let oldRecord = priceMap[cropPriceItem.company_id];
    if (!oldRecord || oldRecord.date <= cropPriceItem.date) {
        priceMap[cropPriceItem.company_id] = cropPriceItem;
        myCache.set(key, priceMap, 3600 * 24);
    }
}

function getCompanyLatestPrice (companyId, cropId) {
    const key = getCropCompanyLatestPriceKey(cropId);
    let priceMap = myCache.get(key) || {};
    return priceMap[companyId];
}

module.exports = {
    set,
    get,
    getNzServiceAccessToken,
    setNzServiceAccessToken,
    setCompanies,
    getCompanies,
    updateCompanyLatestPrice,
    getCompanyLatestPrice
}