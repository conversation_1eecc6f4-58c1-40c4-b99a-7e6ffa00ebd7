<template>
  <div class="region_list_page_root">
    <div class="search_wrapper">
      <el-input v-model="searchParams.name" placeholder="取输入区域名" clearable></el-input>
      <el-button type="primary" @click="loadData">查询</el-button>
      <el-button
        class="add_btn"
        type="primary"
        plain
        icon="el-icon-plus"
        @click="handleAddRegion('add')"
        >新增</el-button
      >
    </div>
    <div class="card-body">
      <el-table
        v-loading="loading"
        :data="renderList"
        row-key="id"
        border
        fit
        highlight-current-row
        style="width: 100%"
      >
        <el-table-column label="ID" prop="id" align="center" width="80"></el-table-column>
        <el-table-column label="区域名称" prop="name" align="center"></el-table-column>
        <el-table-column label="包含省份" prop="provinceName" align="center">
          <template slot-scope="{ row }">{{ row.provinceName }}</template>
        </el-table-column>
        <el-table-column label="更新时间" prop="updateTime" align="center" width="160">
          <template slot-scope="{ row }">
            {{ new Date(row.updateTime).format('yyyy-MM-dd hh:mm:ss') }}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="160">
          <template slot-scope="{ row }">
            <el-button size="small" :disabled="!row.editAble" @click="onEditClick(row, 'edit')"
              >编辑</el-button
            >
            <el-button
              size="small"
              :disabled="!row.editAble"
              type="danger"
              @click="onDeleteClick(row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <Pagination
        v-if="total > 0"
        :total="total"
        :pageIndex.sync="pageIndex"
        :pageSize.sync="pageSize"
        @pagination="loadData"
      />
    </div>
    <el-dialog
      :title="editItem.id ? '编辑区域' : '新增区域'"
      :visible.sync="editDialogVisible"
      :close-on-click-modal="false"
      :before-close="onEditBtnClose"
      width="800px"
    >
      <el-form ref="regionEditForm" :model="editItem" :rules="rules" label-width="130px">
        <el-form-item label="区域名称" prop="name" required>
          <el-input v-model="editItem.name" placeholder="请输入区域名称"></el-input>
        </el-form-item>
        <el-form-item label="区域省份" prop="provinceCodes" required>
          <el-select
            v-model="editItem.provinceCodes"
            multiple
            placeholder="请选择省份"
            style="width: 100%"
          >
            <el-option v-for="(key, val) in provinceMap" :key="key" :label="key" :value="val" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="onEditBtnClose">取 消</el-button>
        <el-button type="primary" :disabled="updating" @click="onEditBtnConfirm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import Pagination from '@/components/Pagination'
  import tableMixin from '@/mixins/tableMixin'
  import regionManager from '@/manager/regionManager'
  import cropManager from '@/manager/cropManager'
  export default {
    name: 'CropConfigPage',
    components: {
      Pagination
    },
    mixins: [tableMixin],
    data() {
      return {
        provinceMap: {},
        searchParams: {
          name: ''
        },
        tableData: [],
        editDialogVisible: false,
        updating: false, // 数据更新中，按钮防抖
        editItem: {},
        regions: [],
        editType: '',
        rules: {
          name: [{ required: true, message: '请输入区域名称', trigger: ['blur', 'change'] }],
          provinceCodes: [
            { required: true, message: '请选择区域省份', trigger: ['blur', 'change'] }
          ]
        }
      }
    },
    computed: {
      renderList() {
        return this.tableData.map(item => {
          let provinceName
          if (item.provinceCodes === 'OTHERS' || item.provinceCodes === 'ALL') {
            provinceName = item.provinceCodes
          } else {
            provinceName = item.provinceCodes
              .split(',')
              .map(code => this.provinceMap[code] || code)
              .join(',')
          }
          return { ...item, provinceName }
        })
      }
    },
    created() {
      regionManager.queryProvinceSimpleList().then(list => {
        const len = list.length
        for (let i = 0; i < len; i++) {
          const item = list[i]
          this.$set(this.provinceMap, item.code, item.name)
        }
      })
      this.queryRegions()
    },
    methods: {
      queryRegions() {
        cropManager.queryRegions().then(result => {
          this.regions = result
        })
      },
      getProvinceNames(codes) {
        if (codes === 'OTHERS' || codes === 'ALL') {
          return codes
        }
        return codes
          .split(',')
          .map(code => this.provinceMap[code])
          .join(',')
      },
      loadData() {
        const {
          searchParams: { name },
          pageIndex,
          pageSize
        } = this
        this.loading = true
        regionManager
          .queryRegions(name, pageIndex, pageSize)
          .then(res => {
            const { total, list } = res
            this.total = total
            this.tableData = list
          })
          .catch(err => {
            this.$message.error(err)
          })
          .then(() => {
            this.loading = false
          })
      },
      handleAddRegion(type) {
        this.editDialogVisible = true
        this.editType = type
        this.editItem = {
          name: '',
          provinceCodes: []
        }
      },
      onEditClick(item, type) {
        this.editType = type
        this.editDialogVisible = true
        this.editItem = {
          ...item,
          provinceCodes: item.provinceCodes.split(',')
        }
      },
      async onEditBtnConfirm() {
        this.$refs['regionEditForm'].validate(async valid => {
          if (valid) {
            try {
              const { id, name, provinceCodes } = this.editItem
              const newItem = {
                name,
                provinceCodes: provinceCodes.join(',')
              }
              this.updating = true
              ;(await this.editType) === 'add'
                ? regionManager.addRegion(newItem)
                : await regionManager.editRegion(id, newItem)
              this.updating = false
              this.$message.success(`区域信息${this.editType === 'add' ? '添加' : '更新'}成功`)
              this.loadData()
              this.onEditBtnClose()
            } catch (error) {
              this.updating = false
              this.$message.error(error.message)
            }
          } else {
            this.$message.error('请完善表单信息')
          }
        })
      },
      onEditBtnClose() {
        this.editDialogVisible = false
        this.editItem = {}
        this.$refs['regionEditForm'].resetFields()
      },
      onDeleteClick(item) {
        this.$confirm(`将删除区域：${item.name}，是否继续？`, '操作提示')
          .then(() => {
            regionManager
              .deleteRegion(item.id)
              .then(() => {
                this.$message.success('删除成功')
                this.loadData()
              })
              .catch(err => this.$message.error(err))
          })
          .catch(() => {
            this.$message.info('操作已取消')
          })
      }
    }
  }
</script>
