//  厂车、港口信息
const dbController = require('./dbController')
const { resFilter, getKeysObj } = require('../utils/filter')
const { TRANSPORT, TRANSPORT_RECORDS } = require('../db').tableNames
const { format } = require('../utils/date')

// 工具函数：生成前一日日期字符串
const getPreviousDate = dateStr => {
	const date = new Date(dateStr)
	date.setDate(date.getDate() - 1)
	return date.toISOString().split('T')[0]
}

// 检查工厂名称+日期组合冲突
const checkFactoryDateConflict = async (factoryName, date, excludeId = null) => {
	const queryConditions = { factoryName, date, isDeleted: 0 }
	if (excludeId !== null) queryConditions.id = { $ne: excludeId }

	const existingRecords = await dbController.query(TRANSPORT, queryConditions)
	if (existingRecords.length > 0) {
		throw new Error('factory_date_conflict')
	}
}

// 获取指定工厂前一日数据
const getPreviousDayData = async (factoryName, date) => {
	try {
		const prevDate = getPreviousDate(date)
		const prevRecords = await dbController.query(TRANSPORT, {
			factoryName,
			date: prevDate
		})
		return prevRecords[0]?.todayData ?? 0
	} catch (error) {
		console.error('获取前日数据失败:', error)
		return 0
	}
}

const addFactory = async info => {
	const { factoryName, todayData, date, type } = info

	try {
		const existing = await dbController.query(TRANSPORT, { factoryName, isDeleted: 0 })
		if (existing.length > 0) throw new Error('factory_exists')

		const initializationData = {
			factoryName,
			todayData,
			date,
			type: Number(type),
			minusYesterday: todayData,
			isDeleted: 0
		}

		const { insertId } = await dbController.add(TRANSPORT, initializationData)
		dbController.add(TRANSPORT_RECORDS, {
			transportId: insertId,
			oldTodayData: 0,
			newTodayData: todayData,
			newBusinessDate: date
		})
		return '工厂创建成功'
	} catch (error) {
		console.error('工厂创建失败:', error)
		throw new Error(error.message === 'factory_exists' ? '该工厂名称已被使用' : '创建工厂失败')
	}
}

const editTransportData = async (id, newInfo) => {
	const { factoryName: newName, todayData: newData, date: newDate } = newInfo
	const newTodayNum = Number(newData)
	try {
		const [original] = await dbController.query(TRANSPORT, { id })
		if (!original) throw new Error('record_not_found')

		const oldName = original.factory_name
		const oldData = Number(original.today_data)
		const oldDate = format(original.date, 'yyyy-MM-dd')

		const nameChanged = newName !== oldName
		const dataChanged = newTodayNum !== oldData
		const dateChanged = newDate !== oldDate
		const shouldUpdate = nameChanged || dataChanged || dateChanged

		if (!shouldUpdate) return `[${newName}]厂车数据未发生变化`

		if (original.is_system_data === 1 && nameChanged) {
			throw new Error('no_factory_rename')
		}

		await checkFactoryDateConflict(newName, newDate, id)

		const updates = {}
		let historyOperation = null
		if (nameChanged && !dataChanged && !dateChanged) {
			updates.factoryName = newName
			await dbController.update(TRANSPORT, { id }, updates)
			return `[${newName}]工厂名称更新成功`
		}

		if (dateChanged) {
			updates.minusYesterday = newTodayNum - oldData
			updates.date = newDate
			updates.todayData = newTodayNum

			historyOperation = dbController.add(TRANSPORT_RECORDS, {
				transportId: id,
				oldTodayData: oldData,
				newTodayData: newTodayNum,
				oldBusinessDate: oldDate,
				newBusinessDate: newDate
			})
		}

		if (dataChanged && !dateChanged) {
			updates.minusYesterday = newTodayNum - oldData + (original.minus_yesterday || 0)
			updates.todayData = newTodayNum

			historyOperation = dbController.update(
				TRANSPORT_RECORDS,
				{
					transportId: id,
					newBusinessDate: newDate
				},
				{
					newTodayData: newTodayNum
				}
			)
		}

		await Promise.all([dbController.update(TRANSPORT, { id }, updates), historyOperation])

		return `[${newName}]数据更新成功`
	} catch (error) {
		console.error('数据更新流程异常:', error)

		const errorMapping = {
			record_not_found: '指定记录不存在',
			factory_date_conflict: '工厂名称与日期组合已存在',
			no_factory_rename: '系统数据，名称不可修改',
			default: '数据更新失败，请检查输入'
		}
		throw new Error(errorMapping[error.message] || errorMapping.default)
	}
}

/**
 * 获取全部数据，不分页
 * @param {Object} params 查询参数
 * @param {boolean} [params.includeDeleted=false] 是否包含已删除数据
 * @returns {Promise<Array>} 数据列表
 */
const queryAll = async ({ includeDeleted = false } = {}) => {
	try {
		const baseConditions = {
			...(!includeDeleted && { isDeleted: 0 })
		}

		const commonOrderConfig = {
			orderBy: ['is_system_data', 'update_time'],
			orderSort: ['DESC', 'DESC']
		}

		const data = await dbController.query(TRANSPORT, baseConditions, commonOrderConfig)
		return data.map(resFilter)
	} catch (error) {
		console.error('获取全部数据失败:', error)
		throw new Error('数据获取失败')
	}
}

/**
 * 分页查询列表
 * @param {Object} params 查询参数
 * @param {number} [params.pageIndex=0] 页码
 * @param {number} [params.pageSize=10] 每页数量
 * @param {string} [params.type='0'] 类型
 * @param {string} [params.factoryName] 工厂名称
 * @param {boolean} [params.includeDeleted=false] 是否包含已删除数据
 * @returns {Promise<Object>} 分页数据
 */
const queryList = async ({
	pageIndex = 0,
	pageSize = 10,
	type = '0',
	factoryName,
	includeDeleted = false
}) => {
	try {
		const baseConditions = {
			type: Number(type),
			...(factoryName && { 'factoryName.like': factoryName }),
			...(!includeDeleted && { isDeleted: 0 })
		}

		const commonOrderConfig = {
			orderBy: ['is_system_data', 'update_time'],
			orderSort: ['DESC', 'DESC']
		}

		const totalRes = await dbController.count(TRANSPORT, baseConditions)
		const total = totalRes[0]['count(*)']
		if (total === 0) return { total, list: [] }

		const data = await dbController.query(TRANSPORT, baseConditions, {
			...commonOrderConfig,
			pageIndex,
			pageSize
		})

		return {
			total,
			list: data.map(resFilter)
		}
	} catch (error) {
		console.error('查询列表失败:', error)
		throw new Error('数据获取失败')
	}
}

const deleteFactory = async id => {
	try {
		const [record] = await dbController.query(TRANSPORT, {
			id,
			isDeleted: 0
		})

		if (!record) {
			throw new Error('record_not_found')
		}

		if (record.is_system_data === 1) {
			throw new Error('record_not_factory_delete')
		}

		await dbController.update(TRANSPORT, { id }, { isDeleted: 1 })

		return `工厂【${record.factoryName}】已删除`
	} catch (error) {
		console.error(`逻辑删除失败 (ID: ${id}):`, error)
		const errorHandler = error => {
			const errorMap = {
				record_not_found: '记录不存在或已被删除',
				record_not_found_or_active: '记录未删除或不存在',
				record_not_factory_delete: '系统预设数据，禁止删除'
			}
			return errorMap[error.message] || '操作失败'
		}
		throw new Error(errorHandler(error))
	}
}

const getTransportHistory = async id => {
	try {
		if (!Number.isInteger(Number(id))) {
			throw new Error('无效的记录ID')
		}

		const history = await dbController.query(
			TRANSPORT_RECORDS,
			{ transportId: id },
			{
				orderBy: 'updateTime',
				orderSort: 'DESC'
			}
		)

		return {
			total: history.length,
			list: history.map(item => resFilter(item))
		}
	} catch (error) {
		console.error('获取历史记录失败:', error)
		throw new Error('获取历史记录失败')
	}
}

const getTransportStats = async (req, res) => {
	try {
		const today = format(new Date(), 'yyyy-MM-dd')
		const stats = await dbController.dbConnect(
			`
      SELECT
        type,
        SUM(today_data) AS total_today,
        SUM(minus_yesterday) AS total_change
      FROM ${TRANSPORT}
      WHERE is_deleted = 0
        AND is_system_data != 1
        AND date = ?
      GROUP BY type
    `,
			[today]
		)

		const result = {
			0: { totalToday: 0, totalChange: 0 }, // 山东厂车
			1: { totalToday: 0, totalChange: 0 } // 锦州港
		}

		stats.forEach(item => {
			const key = item.type.toString()
			result[key] = {
				totalToday: item.total_today || 0,
				totalChange: item.total_change || 0
			}
		})

		return result
	} catch (error) {
		console.error('获取统计数据失败:', error)
		throw new Error('获取统计数据失败')
	}
}

const getTransportDailySummary = async (options = {}) => {
	try {
		const thirtyDaysAgo = new Date()
		thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 29)
		thirtyDaysAgo.setHours(0, 0, 0, 0)
		const queryConditions = {
			isDeleted: 0,
			is_system_data: { $ne: 1 }
		}
		if (options.type) {
			queryConditions.type = options.type
		}
		const activeFactories = await dbController.query(TRANSPORT, queryConditions, { fields: ['id'] })
		const factoryIds = activeFactories.map(f => f.id)

		if (factoryIds.length === 0) return _generateEmptyDates(thirtyDaysAgo)

		const placeholders = factoryIds.map(() => '?').join(', ')

		const params = [
			...factoryIds,
			format(thirtyDaysAgo, 'yyyyMMdd'),
			format(new Date(), 'yyyyMMdd')
		]

		const historyQuery = `
      SELECT
        r1.new_business_date AS date,
        SUM(r1.new_today_data) AS total_today,
        SUM(r1.new_today_data - r1.old_today_data) AS total_change
      FROM ${TRANSPORT_RECORDS} r1
      INNER JOIN (
        SELECT
          transport_id,
          new_business_date,
          MAX(create_time) AS max_create_time
        FROM ${TRANSPORT_RECORDS}
        WHERE
          transport_id IN (${placeholders})
          AND new_business_date >= ?
          AND new_business_date <= ?
        GROUP BY transport_id, new_business_date
      ) r2 ON r1.transport_id = r2.transport_id
        AND r1.new_business_date = r2.new_business_date
        AND r1.create_time = r2.max_create_time
      GROUP BY r1.new_business_date
    `

		const historyData = await dbController.dbConnect(historyQuery, params)

		const mergedMap = new Map()

		historyData.forEach(item => {
			mergedMap.set(item.date, {
				date: item.date,
				total_today: Number(item.total_today) || 0,
				total_change: Number(item.total_change) || 0
			})
		})

		return _fillDateGaps([...mergedMap.values()], thirtyDaysAgo)
	} catch (error) {
		console.error('获取每日汇总数据失败:', error)
		throw new Error('获取统计数据失败')
	}
}

const _generateEmptyDates = startDate => {
	const result = []
	const date = new Date(startDate)
	for (let i = 0; i < 30; i++) {
		result.push({
			date: format(date, 'yyyyMMdd'),
			totalToday: 0,
			totalChange: 0
		})
		date.setDate(date.getDate() + 1)
	}
	return result
}

const _fillDateGaps = (data, startDate) => {
	const dateMap = new Map(data.map(item => [format(item.date, 'yyyyMMdd'), item]))
	const result = []
	const currentDate = new Date(startDate)

	for (let i = 0; i < 30; i++) {
		const dateStr = format(currentDate, 'yyyyMMdd')
		const record = dateMap.get(dateStr) || {}
		result.push({
			date: dateStr,
			totalToday: record.total_today || 0,
			totalChange: record.total_change || 0
		})
		currentDate.setDate(currentDate.getDate() + 1)
	}
	return result
}

module.exports = {
	addFactory,
	editTransportData,
	queryAll,
	queryList,
	deleteFactory,
	getTransportHistory,
	getTransportStats,
	getTransportDailySummary
}
