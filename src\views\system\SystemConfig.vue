<template>
	<div class="sys_config_page_root">
		<el-form v-model="config" label-width="160px">
			<el-form-item label="群聊二维码" required>
				<el-upload
					class="photo-uploader"
					action="/api/v1/common/upload/photo"
					:show-file-list="false"
					:on-success="handlePhotoSuccess"
					:before-upload="beforePhotoUpload"
				>
					<img v-if="config.groupQrCode" :src="config.groupQrCode" class="qr_code" />
					<i v-else class="el-icon-plus photo-uploader-icon"></i>
				</el-upload>
			</el-form-item>
			<el-form-item label="强弹二维码">
				<el-switch
					v-model="config.qrCodeShowAuto"
					:active-value="1"
					:inactive-value="0"
					active-color="#13ce66"
					inactive-color="#ff4949"
				/>
			</el-form-item>
			<el-form-item label="强弹次数" required>
				<el-input-number
					v-model="config.qrCodeShowCountMax"
					:min="1"
					:max="100"
					:step="1"
					controls-position="right"
					label="强弹次数"
				/>
			</el-form-item>
			<el-form-item label="强弹周期" required>
				<el-input-number
					v-model="config.qrCodeNoRepeatHour"
					:min="1"
					:step="1"
					controls-position="right"
					label="强弹周期"
				/>
				小时
			</el-form-item>
			<el-form-item label="展示资讯模块">
				<el-switch
					v-model="config.showInfoModule"
					:active-value="1"
					:inactive-value="0"
					active-color="#13ce66"
					inactive-color="#ff4949"
				/>
			</el-form-item>
			<el-form-item label="分享后可访问天数" required>
				<el-input-number
					v-model="config.shareValidDays"
					:min="1"
					:step="1"
					controls-position="right"
				/>
				天
			</el-form-item>
			<el-form-item label="好友每次助力增加" required>
				<el-input-number
					v-model="config.assistancePoints"
					:min="1"
					:step="1"
					controls-position="right"
				/>
				积分
			</el-form-item>
			<el-form-item label="小程序版本" required>
				<el-input v-model="config.version" style="width: 180px"></el-input>
			</el-form-item>
			<el-form-item>
				<el-button type="primary" @click="onSave">确定</el-button>
			</el-form-item>
		</el-form>
	</div>
</template>

<script>
	import systemManager from '@/manager/systemManager'
	export default {
		name: 'CropConfigPage',
		data() {
			return {
				loading: false,
				config: {
					groupQrCode: '',
					version: '',
					showInfoModule: 1
				}
			}
		},
		created() {
			this.loadData()
		},
		methods: {
			loadData() {
				this.loading = true
				systemManager
					.queryConfig()
					.then(res => {
						this.config = res
					})
					.catch(err => {
						this.$message.error(err)
					})
					.then(() => {
						this.loading = false
					})
			},
			handlePhotoSuccess(res) {
				this.config.groupQrCode = `https://qianwancang.com${res.data}`
			},
			beforePhotoUpload(file) {
				if (file.size < 1024 * 200) {
					return true
				} else {
					this.$alert(
						`只允许上传小于200kb的二维码，可前往<a href="https://tinypng.com" style="color: red">https://tinypng.com</a>，先压缩后再上传。`,
						'提示',
						{ type: 'error' }
					)
					return false
				}
			},
			onSave() {
				if (this.config.groupQrCode && this.config.version) {
					this.updateConfig()
				} else {
					this.$message.error('请先补全表单信息')
				}
			},
			updateConfig() {
				this.loading = true
				systemManager
					.updateConfig(this.config)
					.then(() => {
						this.$message.success('系统配置更新成功')
					})
					.catch(err => {
						this.$message.error(err)
					})
					.then(() => {
						this.loading = false
					})
			}
		}
	}
</script>

<style lang="scss">
	.sys_config_page_root {
		.photo-uploader .el-upload {
			border: 1px dashed #d9d9d9;
			border-radius: 6px;
			cursor: pointer;
			position: relative;
			overflow: hidden;
		}
		.photo-uploader .el-upload:hover {
			border-color: #409eff;
		}
		.photo-uploader-icon {
			font-size: 28px;
			color: #8c939d;
			width: 178px;
			height: 178px;
			line-height: 178px;
			text-align: center;
		}
		.qr_code {
			width: 178px;
			height: 178px;
			display: block;
		}
	}
</style>
