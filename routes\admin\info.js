const express = require('express')
const router = express.Router()
const infoController = require('../../controllers/infoController')

router.post('/list', async function (req, res) {
	let pageIndex = req.body.pageIndex || 0
	let pageSize = req.body.pageSize || 0

	try {
		const data = await infoController.getInfoList(pageIndex, pageSize, req.body)
		res.sendSuccess(data)
	} catch (err) {
		res.sendMessage(err)
	}
})

router.post('/add', async function (req, res) {
	try {
		const data = await infoController.addInfo(req.body)
		res.sendSuccess(data)
	} catch (err) {
		res.sendMessage(err)
	}
})

router.put('/update/:id', async function (req, res) {
	const { id } = req.params

	try {
		const data = await infoController.editInfo(id, req.body)
		res.sendSuccess(data)
	} catch (err) {
		res.sendMessage(err)
	}
})

router.delete('/delete/:id', async function (req, res) {
	const { id } = req.params

	try {
		const data = await infoController.deleteInfo(id)
		res.sendSuccess(data)
	} catch (err) {
		res.sendMessage(err)
	}
})

router.get('/detail/:id', async function (req, res) {
	const { id } = req.params

	try {
		const data = await infoController.getInfoById(id)
		res.sendSuccess(data)
	} catch (err) {
		res.sendMessage(err)
	}
})

// 主动去刷新一个资源的数据，看看有没有新的数据
router.get('/crawling/:source', async function (req, res) {
	const { source } = req.params
	const { pages = 1 } = req.query

	try {
		const data = await infoController.refreshSourceData(parseInt(source), parseInt(pages))
		res.sendSuccess(data)
	} catch (err) {
		res.sendMessage(err)
	}
})

// 初始化一个资源的数据合集
router.get('/init/:source', async function (req, res) {
	// 初始化数据，对某一个数据合集进行第一次的爬取
	const { source } = req.params
	const { pages = 3 } = req.query

	try {
		const data = await infoController.initializeSourceData(parseInt(source), parseInt(pages))
		res.sendSuccess(data)
	} catch (err) {
		res.sendMessage(err)
	}
})

// 批量更新所有数据源
router.post('/batch-update', async function (req, res) {
	const { pages = 1, sources = [] } = req.body

	try {
		const options = { pagesToScrape: parseInt(pages) }
		if (sources.length > 0) {
			const data = await infoController.batchUpdateAllSources({
				...options,
				sources: sources.map(s => parseInt(s))
			})
			res.sendSuccess(data)
		} else {
			const data = await infoController.batchUpdateAllSources(options)
			res.sendSuccess(data)
		}
	} catch (err) {
		res.sendMessage(err)
	}
})

// 获取支持的数据源列表
router.get('/sources', async function (req, res) {
	try {
		const data = infoController.getSupportedSources()
		res.sendSuccess(data)
	} catch (err) {
		res.sendMessage(err)
	}
})

// 通用数据源操作接口
router.post('/operate/:source', async function (req, res) {
	const { source } = req.params
	const { operation, pages = 3 } = req.body

	try {
		const options = { pagesToScrape: parseInt(pages) }
		const data = await infoController.operateDataSource(parseInt(source), operation, options)
		res.sendSuccess(data)
	} catch (err) {
		res.sendMessage(err)
	}
})

module.exports = router
