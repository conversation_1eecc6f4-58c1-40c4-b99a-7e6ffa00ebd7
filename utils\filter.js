// 下划线转换驼峰
function toHump (name) {
	return name.replace(/\_(\w)/g, function (all, letter) {
		return letter.toUpperCase()
	})
}
// 驼峰转换下划线
function toLine (name) {
	return name.replace(/([A-Z])/g, '_$1').toLowerCase()
}

function keyToLine (object) {
	let newObj = {}
	for (const key in object) {
		if (object.hasOwnProperty(key)) {
			newObj[toLine(key)] = object[key]
		}
	}
	return newObj
}

function resFilter (data) {
	//处理请求返回的字段
	let returnData = {}
	for (let key in data) {
		let value = data[key]
		if (value !== null) {
			if (value instanceof Date) {
				returnData[toHump(key)] = value.getTime()
			} else {
				returnData[toHump(key)] = value
			}
		}
	}
	return returnData
}

// 根据给定的 key 从已知的 Obj 中过滤出有值的属性组成一个新对象
// 忽略 null 和 undefined

function getKeysObj (data, keysArr) {
	const res = {}
	keysArr.forEach(key => {
		if (data[key] === null || data[key] === undefined) {
			return
		}
		res[key] = data[key]
	})
	return res
}

// 根据给定的 key 从已知的 Obj 中过滤出有值的属性组成一个新对象
//支持 null 和 undefined 赋值
function getKeysFullObj (data, keysArr) {
	const res = {}
	keysArr.forEach(key => {
		res[key] = data[key]
	})
	return res
}

module.exports = {
	toHump,
	toLine,
	keyToLine,
	resFilter,
	getKeysObj,
	getKeysFullObj
}
