// DEBUG 日志开关
const DEBUG = false
function log(...args) {
	if (DEBUG) console.log(...args)
}

/**
 * 断言工具
 * @param {boolean} condition
 * @param {string} msg
 */
function assert(condition, msg) {
	if (!condition) throw new TypeError(msg)
}

/**
 * 正则转义
 * @param {string} str
 * @returns {string}
 */
const escapeRegExp = str => str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')

/**
 * 数字补零
 * @param {number|string} num
 * @returns {string}
 */
const pad2 = num => String(num).padStart(2, '0')

/**
 * 获取当前日期 yyyy-mm-dd
 * @returns {string}
 */
function getCurrentDate() {
	const d = new Date()
	return `${d.getFullYear()}-${pad2(d.getMonth() + 1)}-${pad2(d.getDate())}`
}

/**
 * 格式化日期
 * @param {Array} matchArr
 * @returns {string}
 */
function formatDate([_, year, month, day]) {
	return `${year}-${pad2(month)}-${pad2(day)}`
}

// 配置：作物-子作物映射
const cropParentChildMap = {
	小麦: ['普麦', '优麦', '红麦', '白麦', '新麦', '新普麦', '陈麦', '陈普麦'],
	玉米: ['普通玉米', '高油玉米', '新玉米', '陈玉米']
}

/**
 * 为作物列表中的每个作物添加子作物信息
 * @param {Array<Object>} cropList - 作物对象数组，每个对象应包含 `name` 属性
 * @returns {Array<Object>} - 添加了子作物信息的作物对象数组
 */
function enhanceCropWithChildren(cropList = []) {
	return cropList.map(crop => {
		crop.children = (cropParentChildMap[crop.name] || []).concat(crop.name)
		return crop
	})
}

/**
 * 根据段落内容和作物列表获取作物对象
 * @param {string} paragraph
 * @param {Array<Object>} crops
 * @returns {Object|null}
 */
function getCrop(paragraph = '', crops = []) {
	const cropList = enhanceCropWithChildren(crops)
	for (const crop of cropList) {
		const { children } = crop
		if (children.some(c => paragraph.includes(c))) return crop
	}
	return null
}

/**
 * 按公司名分段，返回 { company, text }
 * @param {string} inputText
 * @param {Array<string>} companies
 * @returns {Array<{company: string, text: string}>}
 */
function splitTextByCompaniesWithCompany(inputText, companies) {
	if (!inputText) return []
	if (!Array.isArray(companies) || companies.length === 0)
		return [{ company: null, text: inputText.trim() }]

	const sortedCompanies = [...companies].sort((a, b) => b.length - a.length)
	const companyPattern = sortedCompanies.map(escapeRegExp).join('|')
	const reg = new RegExp(companyPattern, 'g')

	const result = []
	const matches = [...inputText.matchAll(reg)]
	if (matches.length === 0) {
		return [{ company: null, text: inputText.trim() }]
	}
	let lastEnd = 0
	for (let i = 0; i < matches.length; i++) {
		const m = matches[i]
		const idx = m.index
		const company = m[0]
		const nextIdx = i + 1 < matches.length ? matches[i + 1].index : inputText.length

		// 前置信息合并到第一个公司段
		if (i === 0 && idx > 0) {
			const seg = inputText.slice(0, nextIdx)
			result.push({ company, text: seg.trim() })
			lastEnd = nextIdx
			continue
		}
		// 正常分段
		if (idx >= lastEnd) {
			const seg = inputText.slice(idx, nextIdx)
			result.push({ company, text: seg.trim() })
			lastEnd = nextIdx
		}
	}
	return result
}

/**
 * 提取水分
 * @param {string} str
 * @returns {number|null}
 */
function parseMoisture(str = '') {
	const m = str.match(/(\d+\.?\d*)%?/)
	return m ? parseFloat(m[1]) : null
}

// 价格提取相关正则和关键词
const INVOICE_KW = ['不带票', '无票']
const HAS_KW = ['带票', '有票']
const PRICE_REGEX =
	/([带不带票]?)([新老]厂)?([最高基础]价)?\s*((?:水分\s*[≤<>=]?\s*\d+\.?\d*%?\s*)*)(\d+\.?\d*)\s*(元\/?(?:斤|kg|㎏|公斤)?)/gi

/**
 * 提取价格信息（通用作物）
 * @param {string} text
 * @returns {Array<Object>}
 */
function extractPriceDetails(text = '') {
	const entries = []
	text.replace(PRICE_REGEX, (_, inv, fac, priceType, moistStr, val, unitRaw) => {
		const moisture = parseMoisture(moistStr)
		const hasInvoice = INVOICE_KW.some(k => inv.includes(k))
			? false
			: HAS_KW.some(k => inv.includes(k))
			? true
			: null
		entries.push({
			value: parseFloat(val),
			unit: unitRaw.toLowerCase().replace(/[^a-z\u4e00-\u9fa5]/g, '') || '斤',
			hasInvoice,
			factoryType: fac?.includes('老') ? '老厂' : fac?.includes('新') ? '新厂' : null,
			priceType: priceType?.includes('最高')
				? '最高价'
				: priceType?.includes('基础')
				? '基础价'
				: null,
			wheatCategory: text.match(/普麦|优麦|白麦|红麦|新麦|新普麦|陈麦|陈普麦/)?.[0] || null,
			scenario: text.match(/潮粮|干粮/)?.[0] || null,
			moisture
		})
	})
	return entries
}

// 尿素价格提取正则
const UREA_REGEX =
	/(?<factory>.*?公司)(?:（(.*?)厂区）)?.*?(?<type>大颗粒|小颗粒|多肽大颗粒)?(?:（(?<package>吨包|散装)）)?\s*(?:（(?<weight>\d+kg)）)?.*?(\d{3,})\b/gi

/**
 * 提取尿素价格信息
 * @param {string} text
 * @returns {Array<Object>}
 */
function extractUreaPrice(text) {
	const entries = []
	let match
	while ((match = UREA_REGEX.exec(text)) !== null) {
		const [_, companyName, factoryArea, type, packageType, weight, price] = match
		const extractedFactory = companyName.match(/(.*?公司)(?:\((.*?厂区)\))?/) || []
		const cleanCompany = extractedFactory[1]?.trim() || companyName.replace(/\(.*?\)/g, '').trim()
		entries.push({
			type: type || '大颗粒',
			package: packageType || '吨包',
			weight: weight ? parseInt(weight) : null,
			value: parseInt(price),
			unit: '元/吨',
			companyName: cleanCompany.replace(/\s+/g, ''),
			factoryArea: factoryArea || null
		})
	}
	return entries
}

/**
 * 选择最优价格
 * @param {Array<Object>} entries
 * @param {Object} crop
 * @returns {Object|null}
 */
function selectPrice(entries = [], crop) {
	if (!entries.length) return null
	let list = [...entries]

	if (crop.name === '小麦') {
		const subs = list.filter(e => crop.children.includes(e.wheatCategory))
		if (subs.length) list = subs
	}

	const rules = [
		e => e.factoryType === '老厂',
		e => e.hasInvoice === false,
		e => e.priceType === '最高价'
	]

	for (let i = 0; i < rules.length; i++) {
		const filtered = list.filter(rules[i])
		if (filtered.length) {
			list = filtered
			break
		}
	}

	let target = 14
	for (let i = 0; i < list.length; i++) {
		if (list[i].scenario === '潮粮') {
			target = 30
			break
		} else if (list[i].scenario === '干粮') {
			target = 14
			break
		}
	}

	const moistValues = list.map(e => e.moisture).filter(v => v != null)
	if (moistValues.length) {
		let best = moistValues[0]
		let bestDiff = Math.abs(best - target)
		for (let i = 1; i < moistValues.length; i++) {
			const diff = Math.abs(moistValues[i] - target)
			if (diff < bestDiff || (diff === bestDiff && moistValues[i] < best)) {
				best = moistValues[i]
				bestDiff = diff
			}
		}
		list = list.filter(e => e.moisture === best)
	}

	return list.length ? list[0] : null
}

/**
 * 归一化数据
 * @param {Object} param0
 * @param {Array<string>} companies
 * @param {Array<Object>} crops
 * @returns {Object|null}
 */
function normalizeData({ companyName, crop, value, unit, date }, companies, crops) {
	const companySet = new Set(companies.map(c => c.replace(/\s+/g, '')))
	const cropSet = new Set(crops.map(c => c.name.replace(/\s+/g, '')))
	if (
		!companySet.has(companyName.replace(/\s+/g, '')) ||
		!cropSet.has(crop.name.replace(/\s+/g, ''))
	) {
		return null
	}
	let price = value
	let normUnit = unit
	if (crop.name === '尿素') {
		normUnit = '元/吨'
	} else if (['kg', '㎏', '公斤'].includes(unit)) {
		price = value / 2
		normUnit = '元/斤'
	} else if (unit === '斤') {
		normUnit = '元/斤'
	}
	return {
		companyName,
		crop,
		price: +price.toFixed(4),
		unit: normUnit,
		date
	}
}

/**
 * 主流程：处理输入文本，提取公司、作物、价格等信息
 * @param {Array<string>} companies
 * @param {Array<Object>} crops
 * @param {string} inputText
 * @returns {{ results: Array, errors: Array }}
 */
function processData(companies, crops, inputText) {
	assert(Array.isArray(companies), 'companies 必须为数组')
	assert(Array.isArray(crops), 'crops 必须为数组')
	assert(typeof inputText === 'string', 'inputText 必须为字符串')
	const currentDate = getCurrentDate()
	let lastCrop = null
	let lastDate = currentDate
	const segments = splitTextByCompaniesWithCompany(inputText, companies)
	log('[segments]', segments)
	const results = []
	const errors = []
	for (const { company, text } of segments) {
		console.log('%c [ company, text ] ', 'background:pink; color:#bf2c9f;', company, text)
		try {
			if (!company) {
				errors.push('未匹配到公司信息')
				return { results, errors }
			}
			const crop = getCrop(text, crops) || lastCrop
			if (!crop) {
				errors.push('未匹配到作物信息')
				return { results, errors }
			}
			const dateMatch = text.match(/(\d{4})[年/-]\s*(\d{1,2})[月/-]\s*(\d{1,2})/)
			const date = dateMatch ? formatDate(dateMatch) : lastDate

			if (crop) lastCrop = crop
			if (date) lastDate = date
			const priceEntries = crop.name === '尿素' ? extractUreaPrice(text) : extractPriceDetails(text)
			console.log('%c [ priceEntries ] ', 'background:pink; color:#bf2c9f;', priceEntries)
			if (!company || !crop.id || priceEntries.length === 0) continue
			const sel = selectPrice(priceEntries, crop)
			if (!sel) continue

			const normalized = normalizeData(
				{ ...sel, companyName: company, crop, date },
				companies,
				crops
			)
			if (normalized) {
				results.push(normalized)
			}
		} catch (err) {
			errors.push('处理异常')
			return { results, errors }
		}
	}
	return { results, errors }
}

module.exports = processData
