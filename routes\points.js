const express = require('express')
const pointsController = require('../controllers/pointsController')
const router = express.Router()

router.get('/record', function (req, res) {
	let { pageIndex, pageSize, transactionType } = req.query
	const { userId } = req.body
	pageIndex = pageIndex || 0
	pageSize = pageSize || 10
	pointsController
		.queryPoints(pageIndex, pageSize, userId, transactionType)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

router.get('/share/generate', function (req, res) {
	const { userId } = req.body
	pointsController
		.generateShareId(userId)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

router.get('/share/check', function (req, res) {
	const { id } = req.query
	const { userId } = req.body
	pointsController
		.checkAndUseShareId(id, userId)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

router.post('/record', function (req, res) {
	pointsController
		.addPointsRecord(req.body)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

router.delete('/record/:id', function (req, res) {
	const { id } = req.params
	pointsController
		.deletePointsRecord(id)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

router.get('/used/today', function (req, res) {
	const { userId } = req.body
	pointsController
		.queryTodayUsedPoints(userId)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

module.exports = router
