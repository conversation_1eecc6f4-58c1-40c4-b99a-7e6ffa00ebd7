const state = {
  enums: {},
  enumLoading: false
}

const mutations = {
  SET_ENUMS: (state, data) => {
    state.enums = data
  },
  SET_ENUM_LOADING: (state, loading) => {
    state.enumLoading = loading
  }
}

const actions = {
  async getEnums({ commit }) {
    commit('SET_ENUM_LOADING', true)

    try {
      const { default: systemManager } = await import('@/manager/systemManager')
      const enums = await systemManager.queryEnums()

      if (enums.enums) {
        commit('SET_ENUMS', enums.enums)
        return enums.enums
      } else if (enums) {
        commit('SET_ENUMS', enums)
        return enums
      }
    } catch (error) {
      console.error('获取枚举失败:', error.message)
      commit('SET_ENUMS', {})
    } finally {
      commit('SET_ENUM_LOADING', false)
    }

    return state.enums
  }
}

const getters = {
  getEnumKey: () => (enumObj, value) => {
    for (const key in enumObj) {
      if (enumObj[key] === value) return key
    }
    return null
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
