const cheerio = require('cheerio')
const {
	NetworkError,
	ParseError,
	logger
} = require('./ScraperUtils')
const HttpClient = require('./engine/HttpClient')

/**
 * 爬虫策略抽象基类
 * 定义了所有爬虫策略必须实现的接口
 */
class ScraperStrategy {
	constructor(config = {}) {
		this.config = {
			headers: {
				'User-Agent':
					'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36'
			},
			...config
		}

		// 使用统一的HTTP客户端
		this.httpClient = new HttpClient()

		// 如果有自定义配置，更新HTTP客户端
		if (config.headers) {
			this.httpClient.updateHeaders(config.headers)
		}
	}

	/**
	 * 分页策略函数 - 根据不同数据源特点生成分页URL
	 * 通用分页处理，所有策略类都可以使用
	 */
	createPaginationStrategies() {
		return {
			// 标准模板分页：index{_page}.html -> index.html, index_1.html, index_2.html
			template: (baseUrl, url, pattern, page) => {
				const pagePath =
					page === 0 ? pattern.replace('{_page}', '') : pattern.replace('{_page}', `_${page}`)
				return `${baseUrl}/${url}${pagePath}`
			},

			// 数字分页：/category/20/1, /category/20/2
			numeric: (baseUrl, url, pageSize, page) => {
				const queryUrl = `${url}/${pageSize || 20}/${page + 1}`
				return `${baseUrl}/${queryUrl}`
			},

			// 查询参数分页：/path?page=1, /path?page=2
			query: (baseUrl, url, paramName, page) => {
				const separator = url.includes('?') ? '&' : '?'
				const pageParam = page > 0 ? `${separator}${paramName || 'page'}=${page}` : ''
				return `${baseUrl}/${url}${pageParam}`
			},

			// 路径分页：/path/page/1, /path/page/2
			path: (baseUrl, url, pathPattern, page) => {
				const pagePath = page > 0 ? pathPattern.replace('{page}', page) : ''
				return `${baseUrl}/${url}${pagePath}`
			}
		}
	}

	/**
	 * 构建分页URL - 统一分页处理入口
	 * @param {Object} params - 分页参数 {url, page, categoryConfig, config}
	 * @returns {string} - 完整的分页URL
	 */
	buildPaginationUrl(params) {
		const { url, page = 0, categoryConfig } = params
		const strategies = this.createPaginationStrategies()
		const config = this.config

		// 优先使用数据源级别的统一分页配置
		if (config.pagination) {
			const { type, pattern, pageSize, pageParam, pathPattern } = config.pagination

			switch (type) {
				case 'template':
					return strategies.template(config.baseUrl, url, pattern, page)
				case 'numeric':
					return strategies.numeric(config.baseUrl, url, pageSize, page)
				case 'query':
					return strategies.query(config.baseUrl, url, pageParam, page)
				case 'path':
					return strategies.path(config.baseUrl, url, pathPattern, page)
				default:
					throw new Error(`不支持的分页类型: ${type}`)
			}
		}

		// 向后兼容：使用分类特定的分页配置
		if (categoryConfig && categoryConfig.urlPattern) {
			return strategies.template(config.baseUrl, url, categoryConfig.urlPattern, page)
		}

		// 最后备选：默认数字分页（向后兼容）
		return strategies.numeric(config.baseUrl, url, 20, page)
	}

	/**
	 * 抓取列表页数据
	 * @param {Object} params - 抓取参数 {url, page, category}
	 * @returns {Promise<Array>} - 标准化的数据列表
	 */
	async scrapeListPage(params) {
		throw new Error('scrapeListPage method must be implemented')
	}

	/**
	 * 抓取详情页数据
	 * @param {string} url - 详情页URL
	 * @returns {Promise<Object>} - 标准化的详情数据
	 */
	async scrapeDetailPage(url) {
		throw new Error('scrapeDetailPage method must be implemented')
	}

	/**
	 * 获取策略名称
	 * @returns {string} - 策略名称
	 */
	getStrategyName() {
		throw new Error('getStrategyName method must be implemented')
	}

	/**
	 * 获取数据源ID
	 * @returns {number} - 数据源ID
	 */
	getSourceId() {
		throw new Error('getSourceId method must be implemented')
	}

	/**
	 * 通用HTTP请求方法 - 使用统一的HTTP客户端
	 * @param {string} url - 请求URL
	 * @param {Object} options - 请求选项
	 * @returns {Promise<Object>} - 请求响应
	 */
	async fetchWithRetry(url, options = {}) {
		try {
			logger.debug(`开始请求 ${url}`, { options })

			// 使用HttpClient获取HTML内容
			const htmlContent = await this.httpClient.get(url, options)

			// 封装成axios风格的响应对象，保持向后兼容
			const response = {
				data: htmlContent,
				status: 200,
				statusText: 'OK',
				headers: { 'content-type': 'text/html' }
			}

			logger.debug(`请求成功 ${url}`)
			return response
		} catch (error) {
			logger.warn(`请求失败 ${url}`, { error: error.message })

			// 转换为统一的网络错误
			if (error.message.includes('连接被拒绝')) {
				throw new NetworkError(`连接被拒绝: ${url}`, url, null, error)
			} else if (error.message.includes('超时')) {
				throw new NetworkError(`请求超时: ${url}`, url, null, error)
			} else if (error.message.includes('找不到服务器')) {
				throw new NetworkError(`服务器不存在: ${url}`, url, null, error)
			} else if (error.message.includes('403')) {
				throw new NetworkError(`访问被禁止: ${url}`, url, 403, error)
			} else if (error.message.includes('404')) {
				throw new NetworkError(`页面不存在: ${url}`, url, 404, error)
			} else {
				throw new NetworkError(`网络请求失败: ${url}`, url, null, error)
			}
		}
	}

	/**
	 * 解析HTML内容
	 * @param {string} html - HTML字符串
	 * @param {Object} options - cheerio选项
	 * @returns {Object} - cheerio实例
	 */
	parseHtml(html, options = {}) {
		try {
			const defaultOptions = {
				xml: { decodeEntities: false },
				...options
			}
			return cheerio.load(html, defaultOptions, false)
		} catch (error) {
			logger.error('HTML解析失败', { error: error.message })
			throw new ParseError(`HTML解析失败: ${error.message}`, null, null, error)
		}
	}

	/**
	 * 转换为绝对URL
	 * @param {string} url - 相对或绝对URL
	 * @param {string} baseUrl - 基础URL
	 * @returns {string} - 绝对URL
	 */
	convertToAbsoluteUrl(url, baseUrl) {
		if (!url) return ''
		try {
			return new URL(url, baseUrl).href
		} catch (error) {
			console.warn(`Invalid URL: ${url}, base: ${baseUrl}`)
			return url
		}
	}

	/**
	 * 从URL提取ID
	 * @param {string} url - URL字符串
	 * @param {RegExp} pattern - 提取模式
	 * @returns {string|null} - 提取的ID
	 */
	extractIdFromUrl(url, pattern = /(\d+)/) {
		if (!url) return null
		const match = url.match(pattern)
		return match ? match[1] : null
	}

	/**
	 * 标准化数据格式
	 * @param {Object} rawData - 原始数据
	 * @returns {Object} - 标准化后的数据
	 */
	normalizeData(rawData) {
		return {
			title: this.cleanText(rawData.title),
			originalLink: rawData.originalLink,
			summary: this.cleanText(rawData.summary),
			source: this.getSourceId(),
			sourceId: rawData.sourceId,
			publishTime: this.normalizeDate(rawData.publishTime),
			category: rawData.category,
			author: this.cleanText(rawData.author),
			content: rawData.content,
			photos: rawData.photos,
			files: rawData.files,
			cropIds: rawData.cropIds
		}
	}

	/**
	 * 清理文本内容
	 * @param {string} text - 原始文本
	 * @returns {string} - 清理后的文本
	 */
	cleanText(text) {
		if (!text) return ''
		return text.toString().trim().replace(/\s+/g, ' ')
	}

	/**
	 * 标准化日期格式
	 * @param {string} dateStr - 日期字符串
	 * @returns {string} - 标准化的日期
	 */
	normalizeDate(dateStr) {
		if (!dateStr) return ''
		// 可以在这里添加各种日期格式的转换逻辑
		return dateStr.toString().trim()
	}

	/**
	 * 处理图片URL，将相对路径转换为绝对路径
	 * @param {string} content - HTML内容
	 * @param {string} baseUrl - 基础URL
	 * @returns {string} - 处理后的HTML内容
	 */
	processImageUrls(content, baseUrl) {
		if (!content) return content

		return content.replace(/<img\s+[^>]*src="([^"]*)"/gi, (match, src) => {
			if (!src.startsWith('http')) {
				const absoluteSrc = this.convertToAbsoluteUrl(src, baseUrl)
				return match.replace(src, absoluteSrc)
			}
			return match
		})
	}

	/**
	 * 安全执行数据提取
	 * @param {Function} extractFn - 提取函数
	 * @param {string} description - 操作描述
	 * @param {*} defaultValue - 默认值
	 * @returns {*} - 提取结果或默认值
	 */
	safeExtract(extractFn, description = '数据提取', defaultValue = null) {
		try {
			const result = extractFn()
			return result !== undefined && result !== null ? result : defaultValue
		} catch (error) {
			logger.warn(`${description}失败，使用默认值`, {
				error: error.message,
				defaultValue
			})
			return defaultValue
		}
	}
}

module.exports = ScraperStrategy
