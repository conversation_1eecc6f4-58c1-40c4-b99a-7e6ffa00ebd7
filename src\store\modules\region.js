const state = {
  regionList: []
}

const mutations = {
  SET_REGIONS: (state, regions) => {
    state.regionList = regions
  }
}

const actions = {
  async getRegions({ commit }) {
    const { default: regionManager } = await import('@/manager/regionManager')
    const res = await regionManager.queryRegions()
    commit('SET_REGIONS', res.list)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
