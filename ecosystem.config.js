module.exports = {
  apps: [
    {
      name: 'webapp',
      script: '/home/<USER>/bin/www',                   // 主入口文件（适用于 Express 等框架）
      interpreter: '/usr/bin/node',                  // 👈 显式指定 Node.js v20 路径
      interpreter_args: '--max-old-space-size=1024', // 👈 限制内存
      instances: 1,                                  // 不使用所有 CPU 核心（配合 cluster 模式）
      exec_mode: 'fork',                             // 单进程模式，更省内存
      watch: false,                                  // ❌ 生产环境禁用文件监听（开发环境可设为 true）
      ignore_watch: [ // 忽略监听的目录
        'node_modules',
        'logs',
        'public',
        'uploads'
      ],
      env: {
        NODE_ENV: 'development',
        PORT: 3000
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 3000
      },
      autorestart: true,                          // 出错自动重启
      max_memory_restart: '1G',                   // 内存超过 1GB 自动重启，防止内存泄漏
      restart_delay: 2000,                        // 重启间隔 2 秒，避免频繁重启
      error_file: '/home/<USER>/logs/error.log',     // 错误日志路径
      out_file: '/home/<USER>/logs/out.log',         // 正常输出日志
      log_file: '/home/<USER>/logs/combined.log',    // 综合日志
      log_date_format: 'YYYY-MM-DD HH:mm:ss',     // 日志时间格式
      time: true                                  // 启用日志时间戳
    }
  ],
};