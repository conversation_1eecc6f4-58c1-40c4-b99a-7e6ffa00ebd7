import axios from 'axios'
import qs from 'qs'
import { Message, MessageBox } from 'element-ui'
import store from '../store'

const axiosInstance = axios.create({
  baseURL:
    process.env.NODE_ENV === 'development'
      ? '/api/v1/'
      : `${process.env.VUE_APP_API_BASE_URL}/api/v1/`,
  timeout: 30 * 1000
})

function cleanParams(data) {
  if (!data || typeof data !== 'object') return {}
  const cleanedData = {}
  for (const key in data) {
    const value = data[key]
    // 保留 false 和 0，只过滤 undefined、null、空字符串
    if (value !== undefined && value !== null && value !== '') {
      cleanedData[key] = value
    }
  }
  return cleanedData
}

async function http(method, url, data, errIgnore) {
  const cleanedData = cleanParams(data)
  method = method.toLowerCase()
  try {
    let response
    if (method === 'get' || method === 'delete') {
      response = await axiosInstance[method](url, { params: cleanedData })
    } else if (method === 'post' || method === 'put') {
      response = await axiosInstance[method](url, qs.stringify(cleanedData))
    }

    return await handleResponse(response, errIgnore)
  } catch (error) {
    return await handleError(error, errIgnore)
  }
}

async function handleResponse(response, errIgnore) {
  if (!response.data) {
    const errorMsg = '请求异常'
    if (errIgnore) {
      Message.error(errorMsg)
    }
    throw new Error(errorMsg)
  }

  const { code, data, message } = response.data

  if (code === 1) {
    return data
  } else if (code === -1) {
    await MessageBox.alert('您的登录信息已失效，请重新登录', '提示', {
      callback: () => {
        store.commit('auth/LOGOUT')
      }
    })
    throw new Error('登录失效')
  } else {
    const errorMsg = message || '请求失败'
    if (errIgnore) {
      Message.error(errorMsg)
    }
    throw new Error(errorMsg)
  }
}

async function handleError(error, errIgnore) {
  console.error(error)
  const errorMsg = error.response?.data?.message || error.message || '网络异常'
  if (errIgnore) {
    Message.error(errorMsg)
  }
  throw new Error(errorMsg)
}

const methods = ['get', 'post', 'put', 'delete']
methods.forEach(method => {
  http[method] = function (url, data, errIgnore) {
    return http(method, url, data, errIgnore)
  }
})

export default http
