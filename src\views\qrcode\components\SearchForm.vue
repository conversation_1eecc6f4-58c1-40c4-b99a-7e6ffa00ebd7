<template>
  <div class="search_wrapper">
    <el-input
      v-model="searchParams.userName"
      placeholder="请输入用户名"
      clearable
      style="width: 200px"
      @keyup.enter.native="handleSearch"
    ></el-input>
    <el-input
      v-model="searchParams.userMobile"
      placeholder="请输入用户手机号"
      clearable
      :maxlength="11"
      style="width: 200px"
      @keyup.enter.native="handleSearch"
    ></el-input>
    <el-select
      v-model="searchParams.isActive"
      placeholder="状态"
      clearable
      style="width: 120px">
      <el-option :value="1" label="启用"></el-option>
      <el-option :value="0" label="禁用"></el-option>
    </el-select>
    <el-button type="primary" @click="handleSearch">查询</el-button>
    <el-button @click="handleClear">清空</el-button>
    <el-button type="success" @click="handleAdd">新增</el-button>
  </div>
</template>

<script>
	export default {
		name: 'SearchForm',
		props: {
			value: {
				type: Object,
				default: () => ({
					userName: '',
					userMobile: '',
					isActive: null
				})
			}
		},
		computed: {
			searchParams: {
				get() {
					return this.value
				},
				set(val) {
					this.$emit('input', val)
				}
			}
		},
		methods: {
			handleSearch() {
				this.$emit('search', this.searchParams)
			},
			handleClear() {
				this.searchParams = {
					userName: '',
					userMobile: '',
					isActive: null
				}
				this.$emit('clear')
			},
			handleAdd() {
				this.$emit('add')
			}
		}
	}
</script>

<style scoped>
	.search_wrapper {
		display: flex;
		align-items: center;
		gap: 10px;
		margin-bottom: 20px;
		flex-wrap: wrap;
	}

	.search_wrapper .el-input,
	.search_wrapper .el-select {
		margin-right: 10px;
	}

	.search_wrapper .el-button {
		margin-right: 10px;
	}

	@media (max-width: 768px) {
		.search_wrapper {
			flex-direction: column;
			align-items: stretch;
		}

		.search_wrapper .el-input,
		.search_wrapper .el-select,
		.search_wrapper .el-button {
			margin-right: 0;
			margin-bottom: 10px;
			width: 100%;
		}
	}
</style>
