# 新数据源配置指南

## 📋 快速添加新数据源

### 1. 准备阶段 (2分钟)

**第一步：确定数据源类型**
```bash
# 查看目标网站，确定爬取类型：
# - HTML页面爬取 → type: 'html' 
# - JSON API接口 → type: 'api'
# - 省级政府网站 → type: 'province' 
# - 复杂多层级网站 → type: 'complex'
```

**第二步：在枚举中添加数据源ID**
```javascript
// 编辑 constant/enum.js，在 INFO_SOURCE 中添加
YourNewSource: { id: 12, name: '新数据源名称' }
```

### 2. 配置阶段 (5分钟)

**编辑 `services/scraper/configs/sources.js`**

```javascript
// 复制此模板到 module.exports 中
[INFO_SOURCE.YourNewSource.id]: {
    // 【必填】基础信息
    id: INFO_SOURCE.YourNewSource.id,
    name: '数据源中文显示名称',
    type: 'html', // 选择合适的类型
    baseUrl: 'https://example.com',
    
    // 【必填】选择器配置 - 支持多个备用选择器
    selectors: {
        // 列表页面选择器
        listContainer: '.news-list li, .list-item', // 新闻条目容器
        titleSelector: 'h3 a, .title', // 标题元素
        linkSelector: 'a[href], .link', // 详情页链接
        dateSelector: '.date, .time', // 发布时间
        
        // 详情页面选择器
        contentSelector: '.article-body, .content', // 正文内容
        authorSelector: '.author, .by', // 作者（可选）
        summarySelector: '.summary, .intro' // 摘要（可选）
    },
    
    // 【可选】扩展配置 - 根据需要添加
    categories: [
        { url: 'news/', name: '新闻' },
        { url: 'notice/', name: '公告' }
    ],
    cropKeywords: ['相关', '关键词'],
    defaultCropId: '1'
}
```

### 3. 测试验证 (3分钟)

```bash
# 运行配置验证
node -e "
const config = require('./services/scraper/configs/sources');
const { validateConfig } = config;
const newConfig = config[12]; // 使用你的数据源ID
const errors = validateConfig(newConfig);
console.log(errors.length ? '❌ 配置错误:' + errors : '✅ 配置正确');
"

# 测试爬取功能
curl "http://localhost:3000/admin/info/sources" # 确认新数据源出现在列表中
curl "http://localhost:3000/admin/info/init/12" # 测试初始化（替换为你的ID）
```

## 🔧 不同类型配置详解

### HTML类型 (`type: 'html'`)
适用于普通网页爬取，是最常用的类型。

```javascript
{
    type: 'html',
    baseUrl: 'https://example.com',
    
    // 可选：多分类支持
    categories: [
        { url: 'news/', name: '新闻资讯' },
        { url: 'policy/', name: '政策法规' }
    ],
    
    // 可选：分页支持
    urlPattern: 'list_{page}.html', // {page} 会被替换为页码
    
    selectors: {
        listContainer: '.news-list li',
        titleSelector: 'h3 a',
        linkSelector: 'a',
        dateSelector: '.date',
        contentSelector: '.article-content'
    }
}
```

### API类型 (`type: 'api'`)  
适用于提供JSON接口的网站。

```javascript
{
    type: 'api',
    baseUrl: 'https://api.example.com',
    
    // API配置是必需的
    apiUrl: 'https://api.example.com/data',
    apiConfig: {
        list: {
            method: 'getNewsList',
            pageSize: 20,
            params: { type: 'news' }
        },
        detail: {
            method: 'getNewsDetail'
        }
    },
    
    // API类型通常不需要selectors，数据直接从JSON获取
    cropKeywords: ['相关关键词']
}
```

### 省级类型 (`type: 'province'`)
适用于政府网站，有统一的处理逻辑。

```javascript
{
    type: 'province',
    baseUrl: 'https://gov.province.cn',
    category: '政务信息', // 数据分类标签
    
    // URL模式，支持分页
    urlPattern: 'list/index{_page}.html',
    
    selectors: {
        listContainer: '.gov-list li',
        titleSelector: 'a[title]',
        linkSelector: 'a',
        dateSelector: '.publish-date',
        contentSelector: '.gov-content'
    },
    
    // 可选：内容过滤
    filterKeyword: '通知公告',
    contentFilter: ['重要通知', '政策解读']
}
```

### 复杂类型 (`type: 'complex'`)
适用于结构复杂的多层级网站。

```javascript
{
    type: 'complex',
    baseUrl: 'https://complex.site.com',
    
    // 复杂分类配置，支持层级分组
    categories: [
        { 
            url: 'section1/', 
            name: '栏目1', 
            category: '分组A',
            subCategories: [
                { url: 'sub1/', name: '子栏目1' },
                { url: 'sub2/', name: '子栏目2' }
            ]
        }
    ],
    
    selectors: {
        // 使用多个备用选择器提高容错性
        listContainer: '.primary-list li, .secondary-list item, .fallback-container',
        titleSelector: 'h2 a, h3 a, .title-link, a.news-title',
        linkSelector: 'a[href*="detail"], a[href*="show"], .detail-link',
        dateSelector: '.publish-time, .date-info, .time-stamp',
        contentSelector: '.main-content, .article-body, .text-content'
    }
}
```

## ⚡ 选择器编写技巧

### 多备用选择器
```javascript
// ❌ 单一选择器 - 容易失效  
titleSelector: '.news-title'

// ✅ 多备用选择器 - 提高容错性
titleSelector: '.news-title, h3 a, h2, .title'
```

### 属性选择器
```javascript
// 精确匹配包含特定属性的元素
linkSelector: 'a[href*="detail"]', // 链接包含"detail"
titleSelector: 'a[title], h3[class]', // 有title属性的a标签或有class的h3
dateSelector: 'span[class*="time"], .date' // class包含"time"的span
```

### 组合选择器
```javascript
// 层级选择
listContainer: '.news-section .list-wrapper li',
contentSelector: '.article-main .content-body p',

// 兄弟选择器
authorSelector: '.article-info > .author, .byline + .writer'
```

## 🔍 常见问题解决

### 1. 选择器找不到元素
```javascript
// 解决方案：添加更多备用选择器
titleSelector: 'h1, h2, h3, .title, .news-title, a[class*="title"]'

// 使用更宽泛的选择器
listContainer: 'article, .item, li, .news-item, .list-item'
```

### 2. 日期格式解析失败
```javascript
// 在cropKeywords中添加日期相关信息
cropKeywords: ['粮食', '2024年', '最新'],

// 或者使用dateSelector的多种可能
dateSelector: '.date, .time, [class*="time"], [class*="date"]'
```

### 3. 内容获取不完整
```javascript
// 使用多个内容选择器
contentSelector: '.article-body, .content, .main-text, .news-content, main p'

// 添加摘要选择器作为备选
summarySelector: '.summary, .intro, .excerpt, .abstract'
```

## ✅ 配置检查清单

添加新数据源后，请确认：

- [ ] 在 `constant/enum.js` 中添加了数据源ID
- [ ] 配置了所有必填字段：`id`, `name`, `type`, `baseUrl`, `selectors`
- [ ] 配置了必需的选择器：`listContainer`, `titleSelector`, `linkSelector`
- [ ] 选择器使用了多个备用选项
- [ ] 添加了合适的农作物关键词
- [ ] 运行了配置验证测试
- [ ] 测试了初始化和刷新接口
- [ ] 确认数据源出现在支持列表中

## 🚀 高级配置选项

### 请求配置定制
```javascript
requestConfig: {
    headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; DataScraper/1.0)',
        'Accept': 'text/html,application/json',
        'Accept-Language': 'zh-CN,zh;q=0.9'
    },
    timeout: 15000, // 15秒超时
    encoding: 'utf8', // 编码格式
    retry: 3 // 重试次数
}
```

### 动态URL生成
```javascript
// 支持复杂的URL模式
urlPattern: 'list/{category}/page_{page}.html',
// 或者使用函数形式（高级用法）
urlGenerator: (category, page) => `${category}/${page > 1 ? 'page_' + page : 'index'}.html`
```

通过这个指南，你可以在10分钟内添加任何新的数据源！