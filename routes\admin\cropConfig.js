const express = require('express');
const router = express.Router();

const regionController = require('../../controllers/regionController');

router.get('/regions/load', function (req, res) { // 加载全部的可选的区域
    regionController.queryAllRegion().then(data => {
        res.sendSuccess(data)
    }).catch(err => {
        res.sendMessage(err)
    })
})

router.post('/regions/add', function (req, res) { // 新增作物的区域
    regionController.createRegion(req.body).then(data => {
        res.sendSuccess(data)
    }).catch(err => {
        res.sendMessage(err)
    })
})

router.put('/regions/edit/:id', function (req, res) { // 编辑区域信息
    const { body, params: { id } } = req;
    regionController.updateRegion(id, body).then(data => {
        res.sendSuccess(data)
    }).catch(err => {
        res.sendMessage(err)
    })
})

module.exports = router;