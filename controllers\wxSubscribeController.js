// 农舟订阅控制模块
const dbController = require('./dbController');
const wxCommonApi = require('../utils/wxCommonApi');
const { NZ_WX_SUBSCRIBE, NZ_USERS, NZ_BUYERS, NZ_ORDERS, NZ_CROPS } = require('../constant/tableName');
const {
    MINI_ORDER_CHANGE_NOTICE,
    SERVICE_ORDER_CHANGE_BUYER_NOTICE
} = require('../constant/messageTemplate')


// 查询用户是否订阅
function querySubscribeState (userId, buyerId, templateId) {
    return new Promise(function (resolve, reject) {
        dbController.query(NZ_WX_SUBSCRIBE, { userId, buyerId, templateId }, null, (err, rows) => {
            if (err) {
                reject(err);
            } else if (rows[0]) {
                if (rows[0].subscribe) {
                    resolve()
                } else {
                    reject('用户需要重新订阅')
                }
            } else {
                reject('用户未订阅')
            }
        })
    });
}

// 查询用户是否订阅
function querySubscribeRecord (userId, buyerId, templateId) {
    return new Promise(function (resolve, reject) {
        dbController.query(NZ_WX_SUBSCRIBE, { userId, buyerId, templateId }, null, (err, rows) => {
            if (err) {
                reject(err);
            } else {
                resolve(rows[0]);
            }
        })
    });
}

// 订阅一次性模板消息
function subscribeTemplate (userId, buyerId, templateId) {
    return new Promise(function (resolve, reject) {
        querySubscribeRecord(userId, buyerId, templateId).then(item => {
            if (item) {
                dbController.update(NZ_WX_SUBSCRIBE, { id: item.id }, { subscribe: 1 }, (err, rows) => {
                    if (err) {
                        reject(err)
                    } else {
                        resolve('订阅成功');
                    }
                })
            } else {
                dbController.add(NZ_WX_SUBSCRIBE, { userId, buyerId, templateId, subscribe: 1 }, (err, rows) => {
                    if (err) {
                        reject(err)
                    } else {
                        resolve('订阅成功');
                    }
                })
            }
        }).catch(err => {
            reject(err);
        })
    });
}

// 取消订阅记录
function cancelSubscribeTemplate (userId, buyerId, templateId) {
    return new Promise(function (resolve, reject) {
        dbController.update(NZ_WX_SUBSCRIBE, { userId, buyerId, templateId }, { subscribe: 0 }, (err, rows) => {
            if (err) {
                reject(err)
            } else {
                resolve('取消订阅成功');
            }
        })
    });
}

// 给用户发送订单变化通知
function sendMiniOrderMessage (userId, orderId, orderContent, orderState, remarks) {
    return new Promise(function (resolve, reject) {
        dbController.query(NZ_USERS, { id: userId }, null, (err, rows) => {
            if (err) {
                reject(err)
            } else {
                if (rows[0]) {
                    const { mini_wx_open_id } = rows[0];
                    wxCommonApi.sendMiniOrderChangeMessage(mini_wx_open_id, orderId, orderContent, orderState, remarks, `pages/order/detail?id=${orderId}`).then(() => {
                        cancelSubscribeTemplate(userId, null, MINI_ORDER_CHANGE_NOTICE);
                        resolve('发送成功');
                    }).catch(err => {
                        reject(err);
                    })
                } else {
                    reject('用户不存在');
                }
            }
        })
    });
}

// 给卖家发送新订单更新通知, 带test参数的情况是测试接口
function sendBuyerNewOrderMessage (orderId, oldOrderChange, test) {
    const taskArr = [];
    taskArr.push(new Promise((resolve, reject) => {
        dbController.query(NZ_CROPS, null, null, (err, rows) => {
            if (err) {
                reject();
            } else {
                resolve(rows)
            }
        })
    }), new Promise((resolve, reject) => {
        dbController.query(NZ_ORDERS, { id: orderId }, null, (err, rows) => {
            if (err) {
                reject();
            } else {
                resolve(rows[0])
            }
        })
    }), new Promise((resolve, reject) => {
        dbController.query(NZ_WX_SUBSCRIBE, { template_id: SERVICE_ORDER_CHANGE_BUYER_NOTICE, subscribe: 1 }, null, (err, rows) => {
            if (err) {
                reject();
            } else {
                resolve(rows)
            }
        })
    }));
    Promise.all(taskArr).then(resArr => {
        let crops = resArr[0];
        let order = resArr[1];
        let subscribeLists = resArr[2];
        // 如果条件满足，给满足条件的人发消息
        if (crops.length > 0 && order && subscribeLists.length > 0) {
            let buyerIds = subscribeLists.map(item => item.buyer_id);
            if (test) {
                buyerIds = buyerIds.filter(id => id < 3);
            }
            dbController.query(NZ_BUYERS, { id: buyerIds }, null, (err, rows) => {
                if (!err && rows.length > 0) {
                    doSend(crops, order, rows);
                }
            })
        }
    })
    function doSend (crops, order, buyers) {
        let cropId = order.crop_child_id || order.crop_id;
        let cropItem;
        crops.some(item => {
            if (cropId === item.id) {
                cropItem = item;
                return true;
            }
        });
        let orderId = order.id;
        let orderContent = cropItem.name + order.count + cropItem.order_count_unit;
        let orderState = '新订单';
        let remarks = '千万仓来新订单了，点击立即查看订单详细';
        if (oldOrderChange) {
            orderState = '价格下调'
            remarks = '有订单价格下调了，点击立即查看订单详细';
        }
        let messageUrl = 'http://nongyimai.com/fuwuhao/buyer/index.html?orderId=' + order.id;
        buyers.forEach(buyer => {
            let openId = buyer.service_wx_open_id;
            wxCommonApi.sendOrderMessageToBuyer(openId, orderId, orderContent, orderState, remarks, messageUrl).then(() => {
                // 如果发送成功，将用户的订阅状态取消
                cancelSubscribe(buyer.id);
            }).catch(err => {
                console.log(`发送订阅失败：${err}`);
                if (err.indexOf('refuse to accept') > 0) { // 用户未授权，同步后台数据
                    cancelSubscribe(buyer.id);
                }
            })
        });

        function cancelSubscribe (buyerId) {
            dbController.update(NZ_WX_SUBSCRIBE, { buyerId, templateId: SERVICE_ORDER_CHANGE_BUYER_NOTICE }, { subscribe: 0 }, () => {
                console.log('取消用户订阅成功')
            });
        }
    }
}



module.exports = {
    querySubscribeState,
    querySubscribeRecord,
    subscribeTemplate,
    cancelSubscribeTemplate,
    sendMiniOrderMessage,
    sendBuyerNewOrderMessage
}