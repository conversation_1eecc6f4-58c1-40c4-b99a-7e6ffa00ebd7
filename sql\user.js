// 根据距离的远近查询商家列表
// parameters = [
//     target_latitude, // 第一个问号，对应目标地点的纬度
//     target_longitude, // 第二个问号，对应目标地点的经度
//     target_latitude, // 第三个问号，再次对应目标地点的纬度
//     max_distance, // 第四个问号，对应最大搜索半径（距离）
//     page_size, // 第五个问号，对应每页显示的数量（LIMIT）
//     offset // 第六个问号，对应分页偏移量（OFFSET）
// ]

const queryTraderByDistance = `
SELECT id, avatar, name, mobile, type, city_code, address, latitude, longitude, contacted_count,
    official_subscribe, description, purchase_crops, transport_cars, sale_products, images,
    ( 6371 * acos(cos(radians(?))
                     * cos(radians(latitude))
                     * cos(radians(longitude) - radians(?)) 
                     + sin(radians(?))
                     * sin(radians(latitude))
                   )
       ) AS distance
FROM t_user
WHERE
  (
    (type IN (?) AND ? != 3)
    OR
    (type = ? AND is_paid = 1)
  )
  AND latitude IS NOT NULL
  AND longitude IS NOT NULL
ORDER BY distance
LIMIT ? OFFSET ?
`
module.exports = {
	queryTraderByDistance
}
