//地球半径
const EARTH_RADIUS = 6378137.0 //单位M

// 根据经纬度获取数据库查询的SQL语句
function getGpsSql (longitude, latitude, distance) {
    //地球半径
    const EARTH_CIRCLE = EARTH_RADIUS * Math.PI * 2
    const maxAngle = ((distance * 1000) / EARTH_CIRCLE) * 360
    const minLongitude = Math.round((longitude * 1 - maxAngle) * 100000) / 100000
    const maxLongitude = Math.round((longitude * 1 + maxAngle) * 100000) / 100000
    const minLatitude = Math.round((latitude * 1 - maxAngle) * 100000) / 100000
    const maxLatitude = Math.round((latitude * 1 + maxAngle) * 100000) / 100000

    const arr = [
        '`longitude` > ' + minLongitude,
        '`longitude` < ' + maxLongitude,
        '`latitude` > ' + minLatitude,
        '`latitude` < ' + maxLatitude
    ]
    return arr.join(' AND ')
}

// 通过距离（公里）获取展示的文案
function getDistanceLabel (value) {
    if (value < 1) {
        return Math.round(value * 1000) + '米'
    } else {
        return Math.round(value * 100) / 100 + '公里'
    }
}

function getRad (d) {
    return d * Math.PI / 180.0;
}
/**
 * 根据GPS两点经纬度信息计算距离
 * @param  {Number} lat1 点1 GPS 经度
 * @param  {Number} lng1 点1 GPS 纬度
 * @param  {Number} lat2 点2 GPS 经度
 * @param  {Number} lng2 点2 GPS 纬度
 * @return {Number}           两点距离
 */
function calculateDistance (lat1, lng1, lat2, lng2) {
    if ((lat1 === lat2) && (lng1 === lng2)) {
        return {
            value: 0,
            label: '0米'
        };
    }

    if (!lat1 || !lng1 || !lat2 || !lng2) {
        return {
            value: null,
            label: '缺位置信息'
        }
    }

    var f = getRad((lat1 + lat2) / 2);
    var g = getRad((lat1 - lat2) / 2);
    var l = getRad((lng1 - lng2) / 2);

    var sg = Math.sin(g);
    var sl = Math.sin(l);
    var sf = Math.sin(f);

    var s, c, w, r, d, h1, h2;
    var a = EARTH_RADIUS;
    var fl = 1 / 298.257;

    sg = sg * sg;
    sl = sl * sl;
    sf = sf * sf;

    s = sg * (1 - sl) + (1 - sf) * sl;
    c = (1 - sg) * (1 - sl) + sf * sl;

    w = Math.atan(Math.sqrt(s / c));
    r = Math.sqrt(s * c) / w;
    d = 2 * w * a;
    h1 = (3 * r - 1) / 2 / c;
    h2 = (3 * r + 1) / 2 / s;

    var tValue = parseInt(d * (1 + fl * (h1 * sf * (1 - sg) - h2 * (1 - sf) * sg)));
    return {
        value: tValue,
        label: tValue > 1000 ? `${Math.round(tValue / 100) / 10}公里` : `${tValue}米`
    }
};


module.exports = {
    getGpsSql,
    getDistanceLabel,
    calculateDistance
}