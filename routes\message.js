var express = require('express');
const axios = require('axios');
var router = express.Router();
const messageController = require('../controllers/messageController')

router.post('/add', function (req, res, next) {
    messageController.addMessage(req.body).then(data => {
        res.sendSuccess(data);
    }).catch(err => {
        res.sendMessage(err)
    })
});

router.post('/queryAnswer', function (req, res, next) {
    const { content } = req.body;
    // 替换为您自己的OpenAI API密钥
    const apiKey = '***************************************************';
    // ChatGPT API调用地址
    const apiUrl = 'https://api.openai.com/v1/chat/completions';
    res.sendSuccess('回答问题：' + content)
    console.log('执行gpt提问')
    axios.post(apiUrl, {
        model: 'gpt-3.5-turbo',
        messages: [{ role: "assistant", content }]
    }, {
        headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ' + apiKey
        }
    }).then(res => {
        console.log('gpt成功', res.data)
        res.sendSuccess(res.data.choices[0].message.content)
    }).catch(err => {
        console.log('gpt失败', err)
        res.sendMessage(err)
    })
});

router.post('/queryMy', function (req, res, next) {
    const { userId, pageIndex, pageSize } = req.body;
    messageController.queryMessages(userId, null, null, pageIndex, pageSize).then(data => {
        res.sendSuccess(data);
    }).catch(err => {
        res.sendMessage(err)
    })
});

// 查询与某个用户之间的聊天记录
router.post('/queryBetweenUser', function (req, res, next) {
    const { userId, otherUserId, pageIndex, pageSize } = req.body;
    messageController.queryMessages(userId, otherUserId, null, pageIndex, pageSize).then(data => {
        // 用户查询的由另一方创建的消息需要设置成已读状态
        const idArr = data.list.filter(item => item.userId == otherUserId).map(item => item.id);
        if (idArr.length > 0) {
            messageController.setMessageReadStatus(idArr)
        }
        res.sendSuccess(data);
    }).catch(err => {
        res.sendMessage(err)
        console.log(err);
    })
});

module.exports = router;
