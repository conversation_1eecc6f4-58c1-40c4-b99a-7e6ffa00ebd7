const express = require('express');
const router = express.Router();

const cropController = require('../../controllers/cropController');

router.post('/crop/load', function (req, res) { // 加载全部农作物
    const { online, showPrice, ignoreChildren } = req.body;
    cropController.queryCrops(online, showPrice, ignoreChildren).then(data => {
        res.sendSuccess(data)
    }).catch(err => {
        res.sendMessage(err)
    })
})

router.post('/crop/add', function (req, res) { // 新增农作物
    cropController.addCrop(req.body).then(data => {
        res.sendSuccess(data)
    }).catch(err => {
        res.sendMessage(err)
    })
})

router.put('/crop/edit/:id', function (req, res) { // 编辑农作物
    const { body, params: { id } } = req;
    cropController.updateCrop(id, body).then(data => {
        res.sendSuccess(data)
    }).catch(err => {
        res.sendMessage(err)
    })
})

router.get('/crop/delete/:id', function (req, res) { // 删除农作物
    const { params: { id } } = req;
    cropController.deleteCrop(id).then(data => {
        res.sendSuccess(data)
    }).catch(err => {
        res.sendMessage(err)
    })
})

router.post('/collection/load', function (req, res) { // 加载农作物收集信息
    cropController.queryCollections().then(data => {
        res.sendSuccess(data)
    }).catch(err => {
        res.sendMessage(err)
    })
})

router.post('/collection/add', function (req, res) { // 新增信息收集条目
    cropController.addCollection(req.body).then(data => {
        res.sendSuccess(data)
    }).catch(err => {
        res.sendMessage(err)
    })
})

router.put('/collection/edit/:id', function (req, res) { // 编辑信息收集条目
    const { body, params: { id } } = req;
    cropController.updateCollection(id, body).then(data => {
        res.sendSuccess(data)
    }).catch(err => {
        res.sendMessage(err)
    })
})

module.exports = router;