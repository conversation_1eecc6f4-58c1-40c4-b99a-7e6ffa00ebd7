import http from '@/utils/http'

function queryCrops(params) {
	return http.post('admin/orderConfig/crop/load', params)
}

function queryCropCollections() {
	return http.post('admin/orderConfig/collection/load')
}

function addCrop(params) {
	return http.post('admin/orderConfig/crop/add', params)
}

function editCrop(id, params) {
	return http.put('admin/orderConfig/crop/edit/' + id, params)
}

function deleteCrop(id) {
	return http.get('admin/orderConfig/crop/delete/' + id)
}

function addCollection(params) {
	return http.post('admin/orderConfig/collection/add', params)
}

function editCollection(id, params) {
	return http.put('admin/orderConfig/collection/edit/' + id, params)
}

// 给作物配置区域的时候，需要拉去全量的区域，给管理员选
function queryRegions() {
	return http.get('admin/cropConfig/regions/load')
}

function updateRegions(id, params) {
	return http.put('admin/orderConfig/crop/edit/' + id, params)
}

export default {
	queryCrops,
	queryCropCollections,
	addCrop,
	editCrop,
	deleteCrop,
	addCollection,
	editCollection,
	queryRegions,
	updateRegions
}
