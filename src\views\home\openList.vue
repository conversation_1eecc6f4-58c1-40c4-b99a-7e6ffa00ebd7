<template>
    <div class="card">
        <div class="search_wrapper">
            <el-select v-model="searchParams.shareId" placeholder="运营人员" clearable style="width:200px">
                <el-option v-for="user in systemUsers" :key="user.userId" :value="user.userId" :label="user.name"></el-option>
            </el-select>
            <el-date-picker
                v-model="searchParams.createTime"
                type="daterange"
                value-format="yyyy-MM-dd HH:mm:ss"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                clearable
                :picker-options="pickerOptions"
            ></el-date-picker>
            <el-button type="primary" @click="loadData">查询</el-button>
            <el-button @click="clear">清空</el-button>
        </div>
        <div class="card-body">
            <el-table v-loading="loading" :data="tableData" border fit highlight-current-row style="width:100%">
                <el-table-column label="用户ID" prop="userId" align="center" />
                <el-table-column label="分享人" prop="shareId" align="center">
                    <template slot-scope="{ row }">{{ systemUserMap[row.shareId] || (row.shareId ? `用户_${row.shareId}` : '--') }}</template>
                </el-table-column>
                <el-table-column label="打开应用时间" prop="createTime" align="center">
                    <template slot-scope="{ row }">{{ new Date(row.createTime).format('yyyy-MM-dd hh:mm:ss') }}</template>
                </el-table-column>
            </el-table>
            <Pagination v-if="total > 0" :total="total" :pageIndex.sync="pageIndex" :pageSize.sync="pageSize" @pagination="loadData" />
        </div>
    </div>
</template>

<script>
import logManager from '@/manager/logManager'
import userManager from '@/manager/userManager'
import Pagination from '@/components/Pagination'
import tableMixin from '@/mixins/tableMixin'
export default {
    name: 'OrderList',
    components: { Pagination },
    mixins: [tableMixin],
    data () {
        return {
            systemUsers: [],
            systemUserMap: {},
            pickerOptions: {
                shortcuts: [{
                    text: '今天',
                    onClick (picker) {
                        const start = new Date();
                        const end = new Date();
                        start.setTime(new Date(new Date().toLocaleDateString()).getTime());//今天零点
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '昨天',
                    onClick (picker) {
                        const todayStart = new Date(new Date().toLocaleDateString()).getTime();//今天零点
                        const start = new Date();
                        const end = new Date();
                        start.setTime(todayStart - 3600 * 1000 * 24);
                        end.setTime(todayStart - 1)
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近一周',
                    onClick (picker) {
                        const start = new Date();
                        const end = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近一个月',
                    onClick (picker) {
                        const start = new Date();
                        const end = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                        picker.$emit('pick', [start, end]);
                    }
                }, {
                    text: '最近三个月',
                    onClick (picker) {
                        const end = new Date();
                        const start = new Date();
                        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                        picker.$emit('pick', [start, end]);
                    }
                }]
            },
            searchParams: {
                shareId: null,
                createTime: null
            }
        }
    },
    created () {
        userManager.loadSystemUsers().then(data => {
            this.systemUsers = data
            const map = {};
            data.forEach(item => {
                map[item.userId] = item.name
            })
            this.systemUserMap = map;
        }).catch(err => err)
    },
    methods: {
        clear () {
            this.searchParams = {
                shareId: null,
                createTime: null
            }
        },
        loadData () {
            const { searchParams: { shareId, createTime }, pageIndex, pageSize } = this;
            let startTime;
            let endTime;
            if (createTime) {
                startTime = createTime[0];
                endTime = createTime[1];
            }
            this.loading = true;
            logManager.queryRecords(shareId, startTime, endTime, pageIndex, pageSize).then(res => {
                const { total, list } = res;
                this.total = total;
                this.tableData = list;
                this.loading = false;
            }).catch(err => {
                this.$message.error(err)
                this.loading = false;
            })
        }
    }
}
</script>
