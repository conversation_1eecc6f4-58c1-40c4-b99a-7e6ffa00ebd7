const express = require('express');
const router = express.Router();
const regionController = require('../../controllers/regionController');

router.post('/load', function (req, res) { //查询区域列表
    const { name, pageIndex, pageSize } = req.body;
    regionController.queryRegion(pageIndex, pageSize, { name }).then(data => {
        res.sendSuccess(data)
    }).catch(err => {
        res.sendMessage(err)
    })
})

router.post('/add', function (req, res) { // 更新区域
    const { name, provinceCodes } = req.body;
    if (name && provinceCodes) {
        regionController.createRegion({ name, provinceCodes }).then(data => {
            res.sendSuccess(data)
        }).catch(err => {
            res.sendMessage(err)
        })
    } else {
        res.sendMessage('参数有误')
    }
})

router.put('/update/:id', function (req, res) { // 更新区域
    const { id } = req.params;
    const { name, provinceCodes } = req.body;
    if (id && name && provinceCodes) {
        regionController.updateRegion(id, { name, provinceCodes }).then(data => {
            res.sendSuccess(data)
        }).catch(err => {
            res.sendMessage(err)
        })
    } else {
        res.sendMessage('参数有误')
    }
})

router.delete('/delete/:id', function (req, res) { // 删除区域
    const { id } = req.params;
    if (id) {
        regionController.deleteRegion(id).then(data => {
            res.sendSuccess(data)
        }).catch(err => {
            res.sendMessage(err)
        })
    } else {
        res.sendMessage('参数有误')
    }
})

module.exports = router;