const dbConfig = {
	host: process.env.MYSQL_HOST,
	user: process.env.MYSQL_USER,
	password: process.env.MYSQL_PASSWORD,
	port: process.env.MYSQL_PORT,
	database: process.env.MYSQL_DATABASE
}

const tableNames = {
	LOG: 't_log',
	INVITE_RECORD: 't_invite_record',
	CITY_CODE: 't_city_code',
	CONFIG: 't_config',
	CROP: 't_crop',
	CROP_COLLECTION: 't_crop_collection',
	CROP_CATEGORY: 't_crop_category',
	FEEDBACK: 't_feedback',
	USER: 't_user',
	ADMIN: 't_admin',
	MESSAGE: 't_message',
	BUYER: 't_buyer',
	ORDER: 't_order',
	ORDER_LIKE: 't_order_like',
	ORDER_RELATION: 't_order_relation',
	ORDER_TRADE: 't_order_trade',
	PRICE_COMPANY: 't_price_company',
	PRICE_COMPANY_DETAIL: 't_price_company_detail',
	PRICE_DAILY: 't_price_daily',
	PRICE_PORT: 't_price_port',
	WX_SUBSCRIBE: 't_wx_subscribe',
	REPORT_PRICE: 't_report_price',
	REGION: 't_region',
	INFORMATION: 't_information',
	INFORMATION_DETAIL: 't_information_detail',
	POINTS: 't_points',
	SHARE_RECORDS: 't_share_records',
	TRANSPORT: 't_transport',
	TRANSPORT_RECORDS: 't_transport_records',
	WECHAT_RAW_MESSAGES: 't_wechat_raw_messages',
	QRCODE: 't_qrcode'
}

module.exports = {
	dbConfig,
	tableNames
}
