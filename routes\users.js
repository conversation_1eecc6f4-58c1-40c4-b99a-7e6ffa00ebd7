var express = require('express')
var router = express.Router()
const userController = require('../controllers/userController')
const logController = require('../controllers/logController')
const { USER_TYPE } = require('../constant/enum')

/**
 * 根据用户数据设置用户会话信息
 * @param {object} req - Express 请求对象
 * @param {object} userData - 从数据库获取的用户数据
 * @returns {object} - 处理过的用户数据（已删除密码）
 */
function setupUserSession(req, userData) {
	delete userData.password
	req.session.userId = userData.id

	// 动态遍历USER_TYPE枚举，为每个角色设置会话标志
	for (const key in USER_TYPE) {
		const sessionKey = `is${key.charAt(0).toUpperCase() + key.slice(1)}`
		req.session[sessionKey] = userData.type === USER_TYPE[key]
	}
	return userData
}

router.post('/login', async (req, res, next) => {
	try {
		const { mobile, password, wxUnionId, ksUnionId, dyUnionId } = req.body

		let task
		if (wxUnionId) {
			task = userController.wxLogin(req.body)
		} else if (ksUnionId) {
			task = userController.ksLogin(req.body)
		} else if (dyUnionId) {
			task = userController.dyLogin(req.body)
		} else if (mobile && password) {
			task = userController.mobileLogin(mobile, password)
		} else {
			return res.sendMessage('登录参数有误')
		}

		const userData = await task
		const responseData = setupUserSession(req, userData)
		res.sendSuccess(responseData)
	} catch (err) {
		res.sendMessage(err)
	}
})

router.post('/logout', async (req, res, next) => {
	try {
		const data = await userController.logout(req)
		res.sendSuccess(data)
	} catch (err) {
		res.sendMessage(err)
	}
})

router.post('/mini/loginByCode', async (req, res, next) => {
	try {
		const { code, inviteUserId } = req.body
		const { platform } = req.query

		if (!code || !platform || !['mp-weixin', 'mp-toutiao', 'mp-kuaishou'].includes(platform)) {
			return res.sendMessage('参数有误')
		}

		const miniUser = await userController.getMiniUserByCode(code, platform)
		if (!miniUser) {
			return res.sendMessage('未能获取小程序账号信息')
		}

		console.log('小程序用户', miniUser)
		const userData = await userController.queryOrAddMiniUser(miniUser, inviteUserId, platform)
		const responseData = setupUserSession(req, userData)
		res.sendSuccess(responseData)
	} catch (err) {
		console.log('获取用户失败', err)
		res.sendMessage(err)
	}
})

router.get('/getCurrentUser', async (req, res, next) => {
	try {
		const { userId } = req.body
		const data = await userController.getUserInfo(userId)
		res.sendSuccess(data)
	} catch (err) {
		res.sendMessage(err)
	}
})

router.post('/mini/getWxUser', async (req, res, next) => {
	try {
		const { code, encryptedData, iv } = req.body
		const data = await userController.getMiniWxUser(code, encryptedData, iv)
		res.sendSuccess(data)
	} catch (err) {
		res.sendMessage(err)
	}
})

router.post('/mini/getWxPhone', async (req, res, next) => {
	try {
		const { code } = req.body
		const data = await userController.getMiniWxPhone(code)
		res.sendSuccess(data)
	} catch (err) {
		res.sendMessage(err)
	}
})

router.post('/official/getWxUser', async (req, res, next) => {
	try {
		const { code, encryptedData, iv } = req.body
		const data = await userController.getOfficialWxUser(code, encryptedData, iv)
		res.sendSuccess(data)
	} catch (err) {
		res.sendMessage(err)
	}
})

router.put('/updateMyInfo', async (req, res, next) => {
	try {
		const { userId } = req.body
		if (!userId) {
			return res.sendMessage('账号未登录')
		}
		const data = await userController.updateInfo(userId, req.body)
		res.sendSuccess(data)
	} catch (err) {
		res.sendMessage(err)
	}
})

router.put('/updateInfoByCollector/:id', async (req, res, next) => {
	// 信息收集员帮忙更新用户的地址、手机号、名字
	try {
		const { id } = req.params
		if (req.session.isCollector || req.session.isAdmin) {
			const data = await userController.updateInfo(id, req.body)
			res.sendSuccess(data)
		} else {
			res.status(403).sendMessage('没有权限')
		}
	} catch (err) {
		res.sendMessage(err)
	}
})

router.get('/countLog/:key', async (req, res, next) => {
	try {
		const { key } = req.params
		const { userId } = req.body
		const data = await logController.countLogs(key, userId)
		res.sendSuccess(data)
	} catch (err) {
		console.log(err)
		res.sendMessage(err)
	}
})

router.post('/getMyShareCount', async (req, res, next) => {
	try {
		const data = await logController.countRecords(req.body)
		res.sendSuccess(data)
	} catch (err) {
		console.log(err)
		res.sendMessage(err)
	}
})

router.get('/getMyRole', async (req, res, next) => {
	try {
		const data = await userController.getMyRoles(req.body.userId)
		res.sendSuccess(data)
	} catch (err) {
		console.log(err)
		res.sendMessage(err)
	}
})

router.get('/getMyLatestLog', async (req, res, next) => {
	try {
		const { userId } = req.body
		const { key } = req.query
		if (!key) {
			return res.sendMessage('参数有误')
		}
		const data = await userController.getMyLogCountAndLatestLog(userId, key)
		res.sendSuccess(data)
	} catch (err) {
		console.log(err)
		res.sendMessage(err)
	}
})

module.exports = router
