<template>
  <el-dialog
    title="编辑资讯作物品类"
    :visible.sync="visible"
    @close="$emit('update:visible', false)"
  >
    <el-form
      ref="editForm"
      :model="editForm"
      :rules="rules"
      label-width="80px">
      <el-form-item label="作物" prop="cropIds">
        <el-select
          v-model="editForm.cropIds"
          multiple
          placeholder="请选择作物"
          style="width: 100%">
          <el-option
            v-for="crop in cropMaps"
            :key="crop.id"
            :label="crop.name"
            :value="crop.id" />
        </el-select>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="$emit('update:visible', false)">取 消</el-button>
      <el-button type="primary" :disabled="editLoading" @click="handleConfirm">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
	export default {
		name: 'EditDialog',
		props: {
			visible: {
				type: Boolean,
				default: false
			},
			editForm: {
				type: Object,
				default: () => ({})
			},
			cropMaps: {
				type: Array,
				default: () => []
			},
			editLoading: {
				type: Boolean,
				default: false
			}
		},
		data() {
			return {
				rules: {
					cropIds: [{ required: true, message: '请选择作物品品类', trigger: ['blur', 'change'] }]
				}
			}
		},
		methods: {
			handleConfirm() {
				this.$refs.editForm.validate(valid => {
					if (valid) {
						this.$emit('onEditBtnConfirm')
					}
				})
			}
		}
	}
</script>
