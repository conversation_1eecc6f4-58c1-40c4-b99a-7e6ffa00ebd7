const express = require('express')
const router = express.Router()
const cropPriceController = require('../controllers/cropPriceController')
const cropPortDailyController = require('../controllers/cropPortDailyController')
const cropCompanyController = require('../controllers/cropCompanyController')
const reportPriceController = require('../controllers/reportPriceController')
const regionController = require('../controllers/regionController')
const cropController = require('../controllers/cropController')
const dateUtil = require('../utils/date')
const signMiddleware = require('../middleware/signCheck')

router.get('/config', function (req, res) {
	let task1 = cropController.queryPricePageCrops()
	let task2 = regionController.queryAllRegion()
	Promise.all([task1, task2])
		.then(resArr => {
			const crops = resArr[0]
			const regionsMap = {}
			resArr[1].forEach(item => {
				regionsMap[item.id] = item
			})
			crops.sort((a, b) => a.sortNumber - b.sortNumber)
			res.sendSuccess(
				crops.map(item => ({
					id: item.id,
					name: item.name,
					priceUnit: item.priceUnit,
					defaultHumidity: item.defaultHumidity,
					sortNumber: item.sortNumber,
					regions: item.regions ? item.regions.split(',').map(id => regionsMap[id]) : null
				}))
			)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

// 获取全量数据的接口需要追加签名获取
router.use('/chartData', signMiddleware)

// 查询最近 30 天的价格变动曲线
router.get('/chartData/home/<USER>', function (req, res) {
	const { cropId } = req.params
	const { regionIds } = req.query
	cropPriceController
		.queryPricePageData(cropId, regionIds)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

// 查询单独一个企业的最近 30 天的价格变动曲线
router.get('/chartData/company/:id', function (req, res) {
	const { id } = req.params
	const { cropId } = req.query
	cropPriceController
		.queryCompanyPageData(id, cropId)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

// 查询区域的价格列表，不处理分页，同时返回今天和昨天的价格数据
router.get('/regionPrice/:cropId', function (req, res) {
	const { cropId } = req.params
	const { regionIds } = req.query
	cropPriceController
		.queryPricesChartData(cropId, regionIds)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

router.get('/common', function (req, res) {
	//通用查询
	let { cropId, companyId, date, pageIndex, pageSize } = req.query
	cropPriceController
		.queryPrices(cropId, companyId, date, pageIndex, pageSize)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

router.get('/today', function (req, res) {
	//查询全国今天价格有变化的
	let date = dateUtil.format(new Date(), 'yyyy-MM-dd')
	cropPriceController
		.queryPrices(null, null, date, null, null)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

router.get('/anyDay', function (req, res) {
	//查询全国某一天
	let { cropId, companyId, date } = req.query
	cropPriceController
		.queryPrices(cropId, companyId, date, null, null)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

router.get('/lastWeek', function (req, res) {
	//查询全国最近一个礼拜
	let { cropId } = req.query
	cropPriceController
		.queryPricesLatest(cropId, 7)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

router.get('/latest/:days', function (req, res) {
	//查询全国最近days天内的价格
	let { days } = req.params
	let { companyId } = req.query
	cropPriceController
		.queryPricesByDays(days, companyId)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

router.get('/city', function (req, res) {
	//查询某个城市最近一个礼拜
	let { cropId, cityCode, date } = req.query
	cropPriceController
		.queryCityPrices(cropId, cityCode, date)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

router.get('/port/latest', function (req, res) {
	cropPortDailyController
		.queryDaily(null, 0, 1)
		.then(data => {
			res.sendSuccess(data[0])
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

router.get('/port/date/:date', function (req, res) {
	let { date } = req.params
	cropPortDailyController
		.queryDaily(date)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

router.get('/company/all', function (req, res) {
	cropCompanyController
		.queryAllCompanies()
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

router.get('/company/updated/fromYesterday', function (req, res) {
	//查询最近两天更新详细的企业
	const startDate = dateUtil.format(new Date(Date.now() - 3600000 * 24), 'yyyy-MM-dd')
	cropCompanyController
		.queryCompanyHasUpdate(startDate)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

router.get('/company/:companyId', function (req, res) {
	//查询某个公司详情
	let { companyId } = req.params
	cropCompanyController
		.queryCompanyById(companyId)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

router.get('/company/:companyId/detailLatest', function (req, res) {
	//查询某个公司价格标准详情
	let { companyId } = req.params
	cropCompanyController
		.queryCompanyLatestDetail(companyId)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

//查询某个公司价格标准详情，涵盖所有的作物
router.get('/company/:companyId/details', function (req, res) {
	let { companyId } = req.params
	cropCompanyController
		.queryCropLatestDetails(companyId)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

// 查询用户上报的价格列表，支持分页查询，支持通过经纬度查询
router.get('/report', function (req, res) {
	// let { pageIndex, pageSize, cropId, longitude, latitude } = req.query;
	let { pageIndex, pageSize, cropId } = req.query
	pageIndex = pageIndex || 0
	pageSize = pageSize || 10
	// TODO: 考虑到前期数据较少，不在对数据进行区域范围的判断，展示全部的数据
	reportPriceController
		.queryReportPrice(pageIndex, pageSize, cropId)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

// 查询用户自己上报的价格信息
router.get('/myReport', function (req, res) {
	const { userId } = req.body
	if (!userId) {
		return res.sendMessage('用户未登录')
	}
	let { pageIndex, pageSize } = req.query
	pageIndex = pageIndex || 0
	pageSize = pageSize || 10
	reportPriceController
		.queryMyReportPrice(pageIndex, pageSize, userId)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

// 用户上报的价格信息
router.post('/report', function (req, res) {
	const { userId, cropId, description } = req.body
	if (!userId) {
		return res.sendMessage('用户未登录')
	}
	if (!cropId) {
		return res.sendMessage('作物的品类不能为空')
	}
	if (!description) {
		return res.sendMessage('价格信息不能为空')
	}
	reportPriceController
		.createReportPrice(req.body)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

// 用户更新的价格信息
router.put('/report/:id', function (req, res) {
	const id = req.params.id
	const { userId, cropId, description } = req.body
	if (!userId) {
		return res.sendMessage('用户未登录')
	}
	if (!cropId) {
		return res.sendMessage('作物的品类不能为空')
	}
	if (!description) {
		return res.sendMessage('价格信息不能为空')
	}
	reportPriceController
		.updateReportPrice(id, req.body)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

router.delete('/report/:id', function (req, res) {
	const id = req.params.id
	const { userId } = req.body
	if (!userId) {
		return res.sendMessage('用户未登录')
	}
	if (!id) {
		return res.sendMessage('参数有误')
	}
	reportPriceController
		.deleteReportPrice(id, userId)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

module.exports = router
