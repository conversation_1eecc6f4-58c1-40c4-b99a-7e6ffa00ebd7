const ScraperStrategy = require('../ScraperStrategy')
const { INFO_SOURCE } = require('../../../constant/enum')

/**
 * 中国棉花协会爬虫策略
 */
class ChinaCottonStrategy extends ScraperStrategy {
	constructor(config) {
		super(config)
		this.sourceId = INFO_SOURCE.ChinaCotton.id
	}

	getStrategyName() {
		return 'ChinaCottonStrategy'
	}

	getSourceId() {
		return this.sourceId
	}

	async scrapeListPage(params) {
		const { url, page = 0, category } = params

		// 使用基类的统一分页策略构建URL
		const fullUrl = this.buildPaginationUrl(params)

		try {
			const response = await this.fetchWithRetry(fullUrl)
			const $ = this.parseHtml(response.data)
			const results = []

			$(this.config.selectors.listContainer).each((index, element) => {
				const title = $(element).find(this.config.selectors.titleSelector).text().trim()
				const relativeLink = $(element).find(this.config.selectors.linkSelector).attr('href')

				if (!relativeLink) return

				const originalLink = this.convertToAbsoluteUrl(relativeLink, this.config.baseUrl)
				const sourceId = this.extractIdFromUrl(originalLink, /\/(\d+)\.html$/)
				const publishTime = $(element).find(this.config.selectors.dateSelector).text().trim()

				if (sourceId) {
					results.push(
						this.normalizeData({
							title,
							originalLink,
							sourceId: parseInt(sourceId),
							publishTime,
							category
						})
					)
				}
			})

			return results
		} catch (error) {
			throw new Error(`抓取列表页${fullUrl}失败: ${error.message}`)
		}
	}

	async scrapeDetailPage(url) {
		try {
			const response = await this.fetchWithRetry(url)
			const $ = this.parseHtml(response.data)

			const container = $('.container')
			const title = $(container).find('.article > h1').text().trim()
			let content = $(container).find(this.config.selectors.contentSelector).html()

			// 处理相对路径的图片 - 使用基类方法
			content = this.processImageUrls(content, url)

			const author = $(container)
				.find(this.config.selectors.authorSelector)
				.first()
				.text()
				.replace(/出处：/, '')
				.trim()

			return { title, content, author }
		} catch (error) {
			throw new Error(`抓取详情页${url}失败: ${error.message}`)
		}
	}
}

module.exports = ChinaCottonStrategy
