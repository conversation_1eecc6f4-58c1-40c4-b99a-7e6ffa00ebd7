const dbController = require('./dbController')
const cropCompanyController = require('./cropCompanyController')
const cropController = require('./cropController')
const cacheController = require('./cacheController')
const regionController = require('./regionController')
const { resFilter } = require('../utils/filter')
const { dateFormat } = require('../utils/common')
const { PRICE_DAILY } = require('../db').tableNames
const CHART_TOTAL_DAY = 30
const DATE_FORMAT = 'yyyy-MM-dd'

// 查询最近N天内的价格记录，如果一个企业没有过价格更新，将不出现在价格表中
function queryPricesByDays(days, companyId) {
	let startDate = dateFormat(new Date(Date.now() - 3600000 * 24 * days), DATE_FORMAT)
	return new Promise(function (resolve, reject) {
		dbController.query(
			PRICE_DAILY,
			{
				companyId,
				cropId: [1, 2], // 老接口只返回小麦和玉米的价格信息
				_special: '`date` > "' + startDate + '"'
			},
			null,
			(err, rows) => {
				if (err) {
					reject(err)
				} else {
					resolve(rows.map(item => resFilter(item)))
				}
			}
		)
	})
}

// 查询最近30天的价格记录
function queryPricesByCropIdAndCompanyId(cropId, companyId, fromDate) {
	return new Promise(function (resolve, reject) {
		const queryConditions = {
			cropId,
			_special: '`date` >= "' + fromDate + '"'
		}
		if (companyId && companyId.length > 0) {
			queryConditions.companyId = companyId
		}
		dbController.query(
			PRICE_DAILY,
			queryConditions,
			{
				orderBy: 'date',
				orderSort: 'ASC' // 按照日期升序
			},
			(err, rows) => {
				if (err) {
					reject(err)
				} else {
					resolve(rows.map(item => resFilter(item)))
				}
			}
		)
	})
}

// 构建价格曲线图的日期数据
function buildDateParams(dateSwitchDelay) {
	const dateArr = []
	// 当前时间是否在延迟切换的时刻之后,如果在切换时间之后，那么执行切换至今日
	const needSwitchToToday =
		Date.now() - new Date().setHours(0, 0, 0, 0) > dateSwitchDelay * 3600 * 1000
	// 遍历过去31天的日期
	// 当今天价格没有更新，农作物对应的属性 dateSwitchDelay （小时），之前，剔除今日的日期，最新日期显示昨日
	for (let index = CHART_TOTAL_DAY; index >= 0; index--) {
		const date = dateFormat(new Date(Date.now() - 3600000 * 24 * index), DATE_FORMAT)
		dateArr.push(date)
	}
	return {
		dateArr,
		needSwitchToToday,
		startDate: dateArr[0],
		yesterdayDate: dateArr[dateArr.length - 2],
		endDate: dateArr[dateArr.length - 1]
	}
}

// 查询最近30天的农作物的价格页面完整的数据合集
function queryCompanyPageData(companyId, cropId) {
	return new Promise(function (resolve, reject) {
		Promise.all([
			cropCompanyController.queryCompanyById(companyId),
			cropController.queryCropById(cropId)
		])
			.then(resArr => {
				const { name: companyName, cityCode } = resArr[0]
				const { name: cropName, dateSwitchDelay } = resArr[1]
				queryCompanyPricesChartData(
					companyId,
					cropId,
					companyName,
					cropName,
					cityCode,
					dateSwitchDelay
				)
					.then(res => {
						resolve(res)
					})
					.catch(err => {
						reject(err)
					})
			})
			.catch(err => {
				console.log(err)
				reject('数据不存在')
			})
	})
}

function queryCompanyPricesChartData(
	companyId,
	cropId,
	companyName,
	cropName,
	cityCode,
	dateSwitchDelay
) {
	return new Promise(function (resolve, reject) {
		let { dateArr, needSwitchToToday, startDate, endDate } = buildDateParams(dateSwitchDelay)
		queryPricesByCropIdAndCompanyId(cropId, companyId, startDate)
			.then(priceArr => {
				const datePriceMap = {}
				priceArr.forEach(item => {
					item.date = dateFormat(new Date(item.date), DATE_FORMAT)
					datePriceMap[item.date] = item
					// 确认今日是否有过当天的价格录入（不是提前录入的未来价格）
					// 只有当天开始录入收购厂商价格的时候，才切换到这一天显示
					if (!needSwitchToToday && item.date === endDate) {
						const todayStart = new Date().setHours(0, 0, 0, 0) // 今日0点
						const now = Date.now()
						const priceCreateTime = new Date(item.createTime).getTime()
						// 只有当价格日期为今日且录入时间也为今日时，才触发切换
						if (priceCreateTime >= todayStart && priceCreateTime <= now) {
							needSwitchToToday = true
						}
					}
				})
				if (needSwitchToToday) {
					// 倒数第二天作为最后一天
					dateArr.shift()
					startDate = dateArr[0]
				} else {
					dateArr.pop()
					endDate = dateArr[dateArr.length - 1]
				}
				// 如果起始日没有价格，取价格列表第一个价格给到起始日
				if (!datePriceMap[startDate]) {
					const { price, minusYesterday, date } = priceArr[0]
					if (date < startDate) {
						// 取的是比起始日更早的价格进行补全
						datePriceMap[startDate] = {
							...priceArr[0],
							minusYesterday: 0
						}
					} else {
						// 根据起始日后面的价格计算补全
						datePriceMap[startDate] = {
							...priceArr[0],
							minusYesterday: 0,
							price: Math.round((price - minusYesterday) * 10000) / 10000
						}
					}
				}
				const datePrices = dateArr.map((date, index) => {
					if (index > 0 && !datePriceMap[date]) {
						datePriceMap[date] = datePriceMap[dateArr[index - 1]]
					}
					return datePriceMap[date].price
				})
				resolve({
					companyName,
					cropName,
					cityCode,
					datePrices,
					dateRange: [startDate, endDate]
				})
			})
			.catch(err => {
				reject(err)
			})
	})
}

// 查询最近30天的农作物的价格页面完整的数据合集
function queryPricePageData(cropId, regionIds) {
	return new Promise(function (resolve, reject) {
		cropController
			.queryCropById(cropId)
			.then(res => {
				const { excludeRegions, dateSwitchDelay } = res
				queryPricesChartData(cropId, regionIds, excludeRegions, dateSwitchDelay)
					.then(res => {
						resolve(res)
					})
					.catch(err => {
						reject(err)
					})
			})
			.catch(err => reject(err))
	})
}

/**
 * 获取需要排除的企业 ID 集合
 * @param {Array} regions - 所有区域列表
 * @param {string|null} excludeRegionIds - 需要排除的区域 ID 列表
 * @param {Array} companies - 所有企业列表
 * @returns {Set} - 包含需要排除的企业 ID 的集合
 */
function getExcludedCompanyIds(regions, excludeRegionIds, companies) {
	const excludeRegionMaps = excludeRegionIds
		? new Set(excludeRegionIds.split(',').map(Number))
		: new Set()
	const excludeProvincePreCodes = regions.flatMap(r =>
		excludeRegionMaps.has(r.id) ? r.provinceCodes.split(',').map(Number) : []
	)
	const excludeCompanies = companies.filter(company =>
		excludeProvincePreCodes.includes(company.provincePreCode)
	)
	return new Set(excludeCompanies.map(c => c.id))
}

/**
 * 查询最近30天的农作物的价格页面完整的数据合集所需的图表数据
 * @param {number} cropId - 农作物的 ID
 * @param {string|null} regionIds - 区域 ID 列表，为 null 则默认查询全国
 * @param {string|null} excludeRegionIds - 需要排除的区域 ID 列表
 * @param {number} dateSwitchDelay - 日期切换延迟时间（小时），用于判断是否切换到今日数据
 * @returns {Promise<object>} - 解析为包含价格图表数据的对象
 */
// 查询最近30天的农作物的价格页面完整的数据合集
async function queryPricesChartData(cropId, regionIds, excludeRegionIds, dateSwitchDelay) {
	try {
		let { dateArr, needSwitchToToday, startDate, yesterdayDate, endDate } =
			buildDateParams(dateSwitchDelay)
		const priceDateMap = {}
		dateArr.forEach(date => {
			priceDateMap[date] = {}
		})

		const task1 = regionIds
			? regionController.queryRegionByIds(regionIds.split(','))
			: [{ name: '全国', provinceCodes: 'ALL' }]
		const task2 = cropCompanyController.queryAllCompanies()
		const task3 = queryPricesByCropIdAndCompanyId(cropId, null, startDate)

		const [regions, companies, priceArr] = await Promise.all([task1, task2, task3])

		// 排除的区域企业列表
		const excludedCompanyIds = getExcludedCompanyIds(regions, excludeRegionIds, companies)
		const wholeCountryRegionName = '全国'
		const otherRegionName = '其他'
		// 记录所有区域名称
		const regionNameList = [
			wholeCountryRegionName,
			otherRegionName,
			...regions.filter(r => !['ALL', 'OTHERS'].includes(r.provinceCodes)).map(r => r.name)
		]
		const regionDataMap = {} // 按照区域划分数据的
		const companyMap = {}
		const companyResMap = {} // 返回给客户端使用的企业map
		const priceOneDayTotalMap = {}
		const companyFirstPriceMap = {} // 从数据库中拉去到的每个企业的价格的最早的一个价格数据
		const detailUpdatedMap = {}
		const getRegionName = function (pCode) {
			// 第一项为全国，最后一项为其他
			for (let i = 0; i < regions.length; i++) {
				const { name, provinceCodes } = regions[i]
				if (provinceCodes === 'ALL') {
					// 全国代表了全部区域，不做归类
					continue
				}
				if (provinceCodes === 'OTHERS') {
					return otherRegionName
				}
				const arr = provinceCodes.split(',').map(code => code * 1)
				if (arr.includes(pCode)) {
					return name
				}
			}
			return otherRegionName // 找不到区域的情况下，返回其他
		}
		// 确认今日是否有过当天的价格录入（不是提前录入的未来价格）
		// 只有当天开始录入收购厂商价格的时候，才切换到这一天显示
		// 如果今天录入了明天的价格，过了12点，也是显示今天的价格
		if (!needSwitchToToday) {
			const todayStart = new Date().setHours(0, 0, 0, 0) // 今日0点
			const now = Date.now()

			// 遍历所有价格记录，查找今日录入的今日价格
			for (let pIdx = 0; pIdx < priceArr.length; pIdx++) {
				const priceItem = priceArr[pIdx]
				const priceItemDate = dateFormat(new Date(priceItem.date), DATE_FORMAT)
				const priceCreateTime = new Date(priceItem.createTime).getTime()

				// 只有当价格日期为今日且录入时间也为今日时，才触发切换
				// 这样可以避免提前录入的未来价格影响当前显示
				if (priceItemDate === endDate && priceCreateTime >= todayStart && priceCreateTime <= now) {
					needSwitchToToday = true
					break
				}
			}
		}
		if (needSwitchToToday) {
			// 倒数第二天作为最后一天
			dateArr.shift()
			startDate = dateArr[0]
		} else {
			dateArr.pop()
			yesterdayDate = dateArr[dateArr.length - 2]
			endDate = dateArr[dateArr.length - 1]
		}

		// 在日期切换逻辑完成后查询企业更新状态，确保使用正确的yesterdayDate
		const detailCompanyList = await cropCompanyController.queryCompanyHasUpdate(yesterdayDate)

		detailCompanyList.forEach(item => {
			const todayStart = new Date().setHours(0, 0, 0, 0)
			const yesterdayStart = new Date(todayStart - 24 * 3600 * 1000)

			if (needSwitchToToday) {
				// 如果切换到今日，只有今日有更新的企业才标记为有更新
				if (item.updateTime >= todayStart) {
					detailUpdatedMap[item.companyId] = true
				}
			} else {
				// 如果没有切换到今日，昨日有更新的企业标记为有更新
				if (item.updateTime >= yesterdayStart && item.updateTime < todayStart) {
					detailUpdatedMap[item.companyId] = true
				}
			}
		})
		companies.forEach(item => {
			const { id, provincePreCode } = item
			item.regionName = getRegionName(provincePreCode)
			companyMap[id] = item
		})
		// 包含全国，包含其他的区域列表
		regionNameList.forEach(name => {
			priceOneDayTotalMap[name] = 0
			regionDataMap[name] = {
				upTotal: 0, // 今日上涨的数量
				downTotal: 0, // 今日下降的数量
				sameTotal: 0, // 今日持平的数量
				companyAverageCount: 0, // 一个区域计入平均价的企业的个数
				averageArr: [], // 按照日期排序的均价的列表
				priceList: [],
				yesterdayPriceList: []
			}
		})

		// 遍历价格
		priceArr.forEach(item => {
			// 移除部分字段，不需要返回客户端
			;['id', 'createTime', 'companyName', 'cropId'].forEach(field => delete item[field])
			item.date = dateFormat(new Date(item.date), DATE_FORMAT)
			if (item.date > endDate || item.date < startDate) return
			const { price, minusYesterday, companyId } = item
			const company = companyMap[companyId]
			if (!companyFirstPriceMap[companyId]) {
				companyResMap[companyId] = {
					name: company.name,
					updated: detailUpdatedMap[companyId] ? 1 : 0
				}
				companyFirstPriceMap[companyId] =
					item.date === startDate
						? item
						: {
								...item,
								price: Math.round((price - minusYesterday) * 10000) / 10000,
								minusYesterday: 0
						  }
				// 更新区域统计
				if (company.level) {
					if (!excludedCompanyIds.has(company.id)) {
						regionDataMap[wholeCountryRegionName].companyAverageCount++
					}
					regionDataMap[company.regionName].companyAverageCount++
				}
			}
			// 存储价格数据
			priceDateMap[item.date][companyId] = item
		})

		// 给每个企业的所有日期补齐价格，从第二天开始如果某天没有价格，那么取前面的一天的价格来补全
		dateArr.forEach((date, index) => {
			Object.keys(companyFirstPriceMap).forEach(cId => {
				// 如果某一天没有价格
				if (!priceDateMap[date][cId]) {
					const addPriceItem =
						index > 1 ? priceDateMap[dateArr[index - 1]][cId] : companyFirstPriceMap[cId]
					priceDateMap[date][cId] = {
						...addPriceItem,
						minusYesterday: 0
					}
				}
			})
		})

		// 构建今日数据
		const buildTodayData = function () {
			const todayMap = priceDateMap[endDate]
			const todayArr = Object.values(todayMap) // 同一日期下的各个企业的价格信息
			todayArr.sort((a, b) => b.updateTime - a.updateTime)
			regionDataMap[wholeCountryRegionName].priceList = todayArr.filter(
				item => !excludedCompanyIds.has(item.companyId)
			)
			todayArr.forEach(item => {
				const { minusYesterday } = item
				const { regionName } = companyMap[item.companyId]
				const isExcluded = excludedCompanyIds.has(item.companyId)

				if (minusYesterday > 0) {
					if (!isExcluded) {
						regionDataMap[wholeCountryRegionName].upTotal++
					}
					regionDataMap[regionName].upTotal++
				} else if (minusYesterday < 0) {
					if (!isExcluded) {
						regionDataMap[wholeCountryRegionName].downTotal++
					}
					regionDataMap[regionName].downTotal++
				} else {
					if (!isExcluded) {
						regionDataMap[wholeCountryRegionName].sameTotal++
					}
					regionDataMap[regionName].sameTotal++
				}
				regionDataMap[regionName].priceList.push(item)
			})
		}

		// 构建昨日数据
		const buildYesterdayData = function () {
			const yesterdayMap = priceDateMap[yesterdayDate] // 同一日期下的各个企业的价格信息
			const arr = Object.values(yesterdayMap)
			arr.sort((a, b) => b.updateTime - a.updateTime)
			regionDataMap[wholeCountryRegionName].yesterdayPriceList = arr.filter(
				item => !excludedCompanyIds.has(item.companyId)
			)
			arr.forEach(item => {
				const itemRegionName = companyMap[item.companyId].regionName
				regionDataMap[itemRegionName].yesterdayPriceList.push(item)
			})
		}

		// 构建每日的平均价格
		const buildAveragePrice = function () {
			dateArr.forEach(date => {
				const totalMap = { ...priceOneDayTotalMap }
				const dateMap = priceDateMap[date]
				Object.keys(dateMap).forEach(cId => {
					const { price } = dateMap[cId]
					const { regionName, level } = companyMap[cId]
					if (level) {
						if (!excludedCompanyIds.has(Number(cId))) {
							totalMap[wholeCountryRegionName] += price
						}
						totalMap[regionName] += price
					}
				})
				for (const name in totalMap) {
					const total = totalMap[name]
					const average =
						total > 0
							? Math.round((total / regionDataMap[name].companyAverageCount) * 10000) / 10000
							: 0
					regionDataMap[name].averageArr.push(average)
				}
			})
		}

		buildTodayData()
		buildYesterdayData()
		buildAveragePrice()

		const regionDataList = regions.map(item => {
			return {
				id: item.id,
				name: item.name,
				...regionDataMap[item.name]
			}
		})
		const { upTotal, downTotal, sameTotal, averageArr } = regionDataMap[wholeCountryRegionName]
		const priceAverage = averageArr[averageArr.length - 1]
		const yesterdayPriceAverage = averageArr[averageArr.length - 2]
		const priceChange = Math.round((priceAverage - yesterdayPriceAverage) * 10000) / 10000
		return {
			dateRange: [startDate, endDate], // 起止日期
			todayDetail: {
				// 今日详情
				upTotal,
				downTotal,
				sameTotal,
				priceAverage,
				priceChange
			},
			companyMap: companyResMap,
			regionDataList
		}
	} catch (error) {
		console.error(' [ queryPricesChartData ] ', error)
		throw new Error(error)
	}
}

function queryPricesLatest(cropId, days) {
	let startDate = dateFormat(new Date(Date.now() - 3600000 * 24 * days), 'yyyy-MM-dd')
	return new Promise(function (resolve, reject) {
		dbController.query(
			PRICE_DAILY,
			{ cropId, _special: '`date` > "' + startDate + '"' },
			null,
			(err, rows) => {
				if (err) {
					reject(err)
				} else {
					cropCompanyController
						.fetchCompanyByCrop(cropId)
						.then(companies => {
							//  查询最近七天没有更新的剩余的公司的记录
							let needQueryIdArr = []
							companies.forEach(item => {
								const containThis = rows.some(priceItem => priceItem.companyId === item.id)
								if (!containThis) {
									needQueryIdArr.push(item.id)
								}
							})

							if (needQueryIdArr.length === 0) {
								resolve(rows.map(item => resFilter(item)))
								return
							}

							let finishQuery = 0
							needQueryIdArr.forEach(id => {
								let cacheRecord = cacheController.getCompanyLatestPrice(id, cropId)
								if (cacheRecord) {
									rows.push(cacheRecord)
									finishQuery++
									checkFinish()
								} else {
									dbController.query(
										PRICE_DAILY,
										{ cropId, companyId: id },
										{
											orderBy: 'date',
											orderSort: 'DESC',
											pageIndex: 0,
											pageSize: 1
										},
										(err, data) => {
											// 查询到某个公司有价格记录塞入结果，并缓存
											if (!err && data.length > 0) {
												rows.push(data[0])
												cacheController.updateCompanyLatestPrice(data[0])
											}
											finishQuery++
											checkFinish()
										}
									)
								}
							})
							function checkFinish() {
								if (finishQuery === needQueryIdArr.length) {
									resolve(rows.map(item => resFilter(item)))
								}
							}
						})
						.catch(err => {
							reject(err)
						})
				}
			}
		)
	})
}

//查询某个区域七天内有更新的价格
function queryCityPrices(cropId, cityCode) {
	let startDate = dateFormat(new Date(Date.now() - 3600000 * 24 * 7), 'yyyy-MM-dd')
	return new Promise(function (resolve, reject) {
		cropCompanyController
			.queryCompanies(cityCode, null, null, null)
			.then(data => {
				let companyId = data.map(item => item.id)
				let queryOptions = { cropId, _special: '`date` > "' + startDate + '"', companyId }
				dbController.query(PRICE_DAILY, queryOptions, null, (err, rows) => {
					if (err) {
						reject(err)
					} else {
						resolve(rows.map(item => resFilter(item)))
					}
				})
			})
			.catch(err => {
				reject(err)
			})
	})
}

function countPrices(cropId, companyId, date) {
	return new Promise(function (resolve, reject) {
		dbController.count(PRICE_DAILY, { cropId, companyId, date }, (err, rows) => {
			if (err) {
				reject(err)
			} else {
				resolve(rows[0]['count(*)'])
			}
		})
	})
}

function queryPrices(cropId, companyId, date, pageIndex, pageSize) {
	return new Promise(function (resolve, reject) {
		dbController.query(
			PRICE_DAILY,
			{ cropId, companyId, date },
			{ pageIndex, pageSize, orderBy: 'id', orderSort: 'DESC' },
			(err, rows) => {
				if (err) {
					reject(err)
				} else {
					resolve(rows.map(item => resFilter(item)))
				}
			}
		)
	})
}

function addPrice(cropId, companyId, companyName, price, unit, date) {
	return new Promise(function (resolve, reject) {
		queryPrices(cropId, companyId, null, 0, 1)
			.then(beforeArr => {
				let minusYesterday = 0
				let beforeDate
				let updateId
				let lastItem = beforeArr[0]
				let wrongAction = false
				// 该公司已经录入过历史数据
				if (lastItem) {
					beforeDate = dateFormat(new Date(beforeArr[0].date), 'yyyy-MM-dd')
					if (date === beforeDate) {
						// 同一天的价格
						updateId = lastItem.id
						minusYesterday =
							Math.round((lastItem.minusYesterday + (price - lastItem.price)) * 10000) / 10000
					} else if (date > beforeDate) {
						// 本次录入的价格为最新的日期的价格
						minusYesterday = Math.round((price - lastItem.price) * 10000) / 10000
					} else {
						wrongAction = true
					}
				}
				if (wrongAction) {
					reject(
						`${companyName} 已录入 ${dateFormat(
							new Date(lastItem.date),
							'yyyy-MM-dd'
						)} 的记录，仅允许录入该日期之后的数据，请按时间顺序录入，若需修改请先处理先前数据。`
					)
				} else if (updateId) {
					dbController.update(PRICE_DAILY, { id: updateId }, { price, minusYesterday }, err => {
						if (err) {
							reject(err)
						} else {
							resolve(`${companyName}${date}价格更新成功`)
						}
					})
				} else {
					dbController.add(
						PRICE_DAILY,
						{ cropId, companyId, companyName, price, unit, minusYesterday, date },
						err => {
							if (err) {
								reject(err)
							} else {
								resolve(`${companyName}${date}价格录入成功`)
							}
						}
					)
				}
			})
			.catch(err => {
				reject(err)
			})
	})
}

function updatePrice(id, cropId, companyId, price, date) {
	return new Promise(function (resolve, reject) {
		dbController.query(
			PRICE_DAILY,
			{ cropId, companyId, _special: '`date`<"' + date + '"' },
			{ pageIndex: 0, pageSize: 1 },
			(err, rows) => {
				if (err) {
					reject(err)
				} else if (rows.length === 0) {
					reject('未找到该日期之前的价格记录，无法计算价格变化')
				} else {
					const lastPrice = rows[0].price
					const minusYesterday = Math.round((price - lastPrice) * 10000) / 10000
					dbController.update(PRICE_DAILY, { id }, { cropId, price, date, minusYesterday }, err => {
						if (err) {
							reject(err)
						} else {
							resolve('更新成功')
							// TODO: 去更新缓存的价格信息
							dbController.query(PRICE_DAILY, { id }, null, (err, rows) => {
								if (!err && rows[0]) {
									cacheController.updateCompanyLatestPrice(rows[0])
								}
							})
						}
					})
				}
			}
		)
	})
}

module.exports = {
	queryPricesByDays,
	queryPricesLatest,
	queryCompanyPageData,
	queryPricePageData,
	countPrices,
	queryPrices,
	queryCityPrices,
	addPrice,
	updatePrice
}
