const { USER } = require('../db').tableNames
const { resFilter, getKeysObj } = require('../utils/filter')
const dbController = require('./dbController')
const ossController = require('./ossController')

const gps = require('../utils/gps')
const userSql = require('../sql/user')

const userUpdateKeys = [
	'mobile',
	'latitude',
	'longitude',
	'cityCode',
	'address',
	'wxMiniOpenId',
	'wxOfficialOpenId',
	'name',
	'avatar',
	'gender',
	'type',
	'isPaid',
	'images',
	'saleProducts',
	'officialSubscribe',
	'points',
	'purchaseCrops',
	'transportCars',
	'description',
	'contactedCount'
]

const deleteKeys = ['officialSubscribe', 'points', 'wxMiniOpenId', 'wxUnionId', 'password']

async function queryTraders(reqBody) {
	const { type = '', longitude, latitude, pageIndex = 0, pageSize = 10 } = reqBody
	let typeList
	if (type.includes(',')) {
		typeList = type.split(',').map(t => parseInt(t.trim()))
	} else {
		typeList = [parseInt(type)]
	}

	if (typeList.some(t => ![1, 2, 3].includes(t))) {
		throw new Error('非法的商户类型参数')
	}
	const baseQuery = {
		type: typeList,
		_special: 'latitude IS NOT NULL AND longitude IS NOT NULL'
	}
	if (typeList.includes(3)) baseQuery._special += ' AND is_paid = 1'

	const checkType = typeList.includes(3) ? 3 : typeList[0]

	const pageOffset = pageIndex * pageSize
	const parameters = [
		latitude, // 第一个问号，对应目标地点的纬度
		longitude, // 第二个问号，对应目标地点的经度
		latitude, // 第三个问号，再次对应目标地点的纬度
		typeList, // 第四个问号：类型数组（如[1,2]）
		checkType, // 第五个问号：类型校验值（与typeList相同）
		checkType, // 第六个问号：类型校验值（再次使用）
		parseInt(pageSize), // 第四个问号，对应每页显示的数量（LIMIT）
		pageOffset // 第五个问号，对应分页偏移量（OFFSET）
	]
	try {
		const total = await dbController.countId(USER, baseQuery)
		if (total === 0) {
			return {
				total: 0,
				list: []
			}
		}
		const rows = await dbController.dbConnect(userSql.queryTraderByDistance, parameters)
		return {
			total,
			list: rows.map(item => ({
				...resFilter(item),
				distance: Math.round(item.distance * 100) / 100,
				distanceLabel: gps.getDistanceLabel(item.distance)
			}))
		}
	} catch (error) {
		throw new Error(`查询列表失败: ${error.message || error}`)
	}
}

function updateTraderInfo(id, reqBody) {
	return new Promise((resolve, reject) => {
		const updateData = getKeysObj(reqBody, userUpdateKeys)
		dbController.update(USER, { id }, updateData, (err, rows) => {
			if (err) {
				reject(err)
			} else {
				resolve('更新成功')
			}
		})
	})
}

async function getTraderInfo(id, query) {
	try {
		const traders = await dbController.query(USER, { id }, null)
		if (traders.length === 0) {
			throw new Error('用户不存在')
		}
		const traderInfo = resFilter(traders[0])
		deleteKeys.forEach(key => {
			delete traderInfo[key]
		})
		return traderInfo
	} catch (error) {
		throw new Error(`获取用户信息失败: ${error.message}`)
	}
}

async function recordTraderContact(userId, traderId) {
	try {
		const { contactedCount } = await getTraderInfo(traderId)
		const newContactedCount = contactedCount + 1
		await updateTraderInfo(traderId, { contactedCount: newContactedCount })
		return '联系记录添加成功'
	} catch (error) {
		throw new Error(`添加联系记录失败: ${error.message || error}`)
	}
}

module.exports = {
	getTraderInfo,
	queryTraders,
	recordTraderContact
}
