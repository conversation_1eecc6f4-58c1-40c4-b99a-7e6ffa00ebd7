const express = require('express')
const router = express.Router()
const qrcodeController = require('../../controllers/qrcodeController')

/**
 * 处理路由请求的通用函数
 * @param {Function} fn - 要执行的函数
 */
const handleRequest = fn => async (req, res) => {
	try {
		const data = await fn(req)
		res.sendSuccess(data)
	} catch (error) {
		res.sendMessage(error)
	}
}

/**
 * 查询二维码列表
 * POST /admin/qrcode/load
 */
router.post(
	'/load',
	handleRequest(req => {
		const { userName, userMobile, isActive, pageIndex, pageSize } = req.body
		return qrcodeController.queryQrcodes(userName, userMobile, isActive, pageIndex, pageSize)
	})
)

/**
 * 创建二维码
 * POST /admin/qrcode/create
 */
router.post(
	'/create',
	handleRequest(req => qrcodeController.createQrcode(req.body))
)

/**
 * 更新二维码
 * PUT /admin/qrcode/update/:id
 */
router.put(
	'/update/:id',
	handleRequest(req => {
		if (!req.params.id) throw new Error('参数有误')
		return qrcodeController.updateQrcode(req.params.id, req.body)
	})
)

/**
 * 删除二维码
 * DELETE /admin/qrcode/delete/:id
 */
router.delete(
	'/delete/:id',
	handleRequest(req => {
		if (!req.params.id) throw new Error('参数有误')
		return qrcodeController.deleteQrcode(req.params.id)
	})
)

/**
 * 获取二维码详情
 * GET /admin/qrcode/detail/:id
 */
router.get(
	'/detail/:id',
	handleRequest(req => {
		if (!req.params.id) throw new Error('参数有误')
		return qrcodeController.getQrcodeDetail({ id: req.params.id })
	})
)

/**
 * 获取二维码详情 by userId
 * GET /admin/qrcode/detail/user/:userId
 */
router.get(
	'/detail/user/:userId',
	handleRequest(req => {
		if (!req.params.userId) throw new Error('参数有误')
		return qrcodeController.getQrcodeDetail({ userId: req.params.userId })
	})
)
/**
 * 批量更新二维码状态
 * POST /admin/qrcode/batch-status
 */
router.post(
	'/batch-status',
	handleRequest(req => {
		const { ids, isActive } = req.body
		return qrcodeController.batchUpdateQrcodeStatus(ids, isActive)
	})
)

module.exports = router
