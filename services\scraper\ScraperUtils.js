/**
 * 爬虫错误处理和日志模块
 * 提供统一的错误处理、重试机制和日志记录
 */

class ScraperError extends Error {
	constructor(message, sourceId, operation, originalError = null) {
		super(message)
		this.name = 'ScraperError'
		this.sourceId = sourceId
		this.operation = operation
		this.originalError = originalError
		this.timestamp = new Date().toISOString()
	}

	toJSON() {
		return {
			name: this.name,
			message: this.message,
			sourceId: this.sourceId,
			operation: this.operation,
			timestamp: this.timestamp,
			stack: this.stack,
			originalError: this.originalError
				? {
						message: this.originalError.message,
						stack: this.originalError.stack
				  }
				: null
		}
	}
}

class NetworkError extends ScraperError {
	constructor(message, url, statusCode = null, originalError = null) {
		super(message, null, 'network', originalError)
		this.name = 'NetworkError'
		this.url = url
		this.statusCode = statusCode
	}
}

class ParseError extends ScraperError {
	constructor(message, url, selector = null, originalError = null) {
		super(message, null, 'parse', originalError)
		this.name = 'ParseError'
		this.url = url
		this.selector = selector
	}
}

/**
 * 日志记录器
 */
class ScraperLogger {
	constructor() {
		this.logs = []
		this.maxLogs = 1000 // 最多保存1000条日志
	}

	log(level, message, data = null) {
		const logEntry = {
			level,
			message,
			data,
			timestamp: new Date().toISOString()
		}

		this.logs.push(logEntry)

		// 批量清理，提高性能并防止内存泄漏
		if (this.logs.length > this.maxLogs + 100) {
			this.logs.splice(0, this.logs.length - this.maxLogs)
		}

		// 启动定期清理机制
		if (!this.cleanupInterval) {
			this.cleanupInterval = setInterval(() => {
				if (this.logs.length > this.maxLogs) {
					this.logs.splice(0, this.logs.length - this.maxLogs)
				}
			}, 60000) // 每分钟清理一次
		}

		// 输出到控制台
		const consoleMethod = console[level] || console.log
		if (data) {
			consoleMethod(`[${level.toUpperCase()}] ${message}`, data)
		} else {
			consoleMethod(`[${level.toUpperCase()}] ${message}`)
		}
	}

	info(message, data = null) {
		this.log('info', message, data)
	}

	warn(message, data = null) {
		this.log('warn', message, data)
	}

	error(message, data = null) {
		this.log('error', message, data)
	}

	debug(message, data = null) {
		this.log('debug', message, data)
	}

	getLogs(level = null, limit = 100) {
		let filteredLogs = this.logs

		if (level) {
			filteredLogs = this.logs.filter(log => log.level === level)
		}

		return filteredLogs.slice(-limit)
	}

	clear() {
		this.logs = []
	}
}

/**
 * 健康检查器
 */
class HealthChecker {
	constructor() {
		this.checks = new Map()
	}

	// 注册健康检查
	register(name, checkFunction) {
		this.checks.set(name, checkFunction)
	}

	// 执行所有健康检查
	async checkAll() {
		const results = {}

		for (const [name, checkFunction] of this.checks) {
			try {
				const start = Date.now()
				const result = await checkFunction()
				const duration = Date.now() - start

				results[name] = {
					status: 'healthy',
					result,
					duration
				}
			} catch (error) {
				results[name] = {
					status: 'unhealthy',
					error: error.message,
					duration: 0
				}
			}
		}

		return results
	}

	// 检查网络连接
	async checkNetwork(url = 'https://www.baidu.com') {
		const axios = require('axios')
		try {
			const response = await axios.get(url, { timeout: 5000 })
			return { status: response.status, accessible: true }
		} catch (error) {
			throw new Error(`网络连接失败: ${error.message}`)
		}
	}

	// 检查数据库连接
	async checkDatabase() {
		const dbController = require('../../controllers/dbController')
		try {
			// 简单的数据库查询测试
			await dbController.query('SELECT 1 as test')
			return { accessible: true }
		} catch (error) {
			throw new Error(`数据库连接失败: ${error.message}`)
		}
	}
}

// 创建全局实例
const logger = new ScraperLogger()
const healthChecker = new HealthChecker()

// 注册默认健康检查
healthChecker.register('network', () => healthChecker.checkNetwork())
healthChecker.register('database', () => healthChecker.checkDatabase())

module.exports = {
	ScraperError,
	NetworkError,
	ParseError,
	ScraperLogger,
	HealthChecker,
	logger,
	healthChecker
}
