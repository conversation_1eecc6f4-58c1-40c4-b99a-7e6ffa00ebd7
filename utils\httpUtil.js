const http = require('http');
const https = require('https');
const querystring = require('querystring');
const axios = require('axios');

function resController(res, resolve, reject){
	const { statusCode } = res;
	let error;
	// 任何 2xx 状态码都表示成功的响应，但是这里只检查 200。
	if (statusCode !== 200) {
		error = `请求失败，状态码: ${statusCode}`;
	}
	if (error) {
		console.error(error);
		reject(error);
		// 消费响应的数据来释放内存。
		res.resume();
		return;
	}
	res.setEncoding('utf8');
	let rawData = '';
	res.on('data', (chunk) => { rawData += chunk; });
	res.on('end', () => {
		try {
			const parsedData = JSON.parse(rawData);
			resolve(parsedData);
		} catch (e) {
			console.error(e.message);
			reject(e.message);
		}
	});
}

function get(url){
	const { protocol } = new URL(url);
	return new Promise(function(resolve, reject){
		const httpModel = protocol === 'https:' ? https : http;
		httpModel.get(url, (res) => {
			resController(res, resolve, reject);
		}).on('error', (e) => {
			reject(e.message);
			console.error(`出现错误: ${e.message}`);
		});
	});
}

function postJson(url, data){
	return new Promise(function(resolve, reject){
		axios.post(url, data).then(res => {
			if(res.status === 200){
				resolve(res.data);
			}else{
				reject(res.statusText)
			}
		}).catch(err => {
			reject(err);
		})
	})
}

function post(url, data, options){
	const { protocol, hostname, pathname, search } = new URL(url);
	const method = 'POST';
	const postData = querystring.stringify(Object.assign({}, data));
	const postOptions = Object.assign({
		hostname,
		path: pathname + search,
		method,
		headers: {
			'Content-Type': 'application/x-www-form-urlencoded; charset=utf-8'
		}
	}, options || {});
	return new Promise(function(resolve, reject){
		const httpModel = protocol === 'https:' ? https : http;
		const req = httpModel.request(postOptions, (res) => {
			resController(res, resolve, reject);
		}).on('error', (e) => {
			reject(e.message);
			console.error(`出现错误: ${e.message}`);
		});

		// 将数据写入请求主体。
		req.write(postData);
		req.end();
	})
}

// 服务于微信一些老接口，数据参数采用了xml方式传输
function xmlPost(url, data, options){
	const postUrl = new URL(url);
	const { hostname, pathname, search } = postUrl;
	const method = 'POST';
    let postData = '<xml>';
    for (const key in data) {
        postData += `<${key}>${data[key]}</${key}>`;
    }
	postData += '</xml>';
	const postOptions = Object.assign({
		hostname,
		path: pathname + search,
		method,
		headers: {
			'Content-Type': 'text/xml; charset=utf-8'
		}
	}, options || {});
	return new Promise(function(resolve, reject){
		const req = https.request(postOptions, (res) => {
			resController(res, resolve, reject);
		}).on('error', (e) => {
			reject(e.message);
			console.error(`出现错误: ${e.message}`);
		});
		// 将数据写入请求主体。
		req.write(postData);
		req.end();
	})
}

module.exports = {
    get,
	post,
	postJson,
	xmlPost
}