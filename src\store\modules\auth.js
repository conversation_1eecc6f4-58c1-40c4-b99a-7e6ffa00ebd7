import http from '@/utils/http'
import md5 from 'md5'

const state = {
	admin: null,
	logged: false
}

const getters = {
	isLoggedIn: state => !!state.admin && state.logged
}

const mutations = {
	LOGIN: (state, admin) => {
		state.admin = admin
		state.logged = true
	},
	LOGOUT: state => {
		state.logged = false
		state.admin = null
	}
}

const actions = {
	async login({ commit }, { mobile, password }) {
		const admin = await http.post('user/login', { mobile, password: md5(password) })
		commit('LOGIN', admin)
		return admin
	},

	async logout({ commit }) {
		try {
			await http.post('user/logout')
		} catch (error) {
			console.warn('服务端退出失败:', error)
		}
		// 无论服务端是否成功，都清除前端状态
		commit('LOGOUT')
	},

	async getUserInfo({ commit, state }) {
		// 如果store中已有有效用户信息，直接返回
		if (state.admin && state.logged && Object.keys(state.admin).length > 0) {
			return true
		}

		try {
			const userInfo = await http.get('admin/current/userInfo')

			if (userInfo && Object.keys(userInfo).length > 0) {
				commit('LOGIN', userInfo)
				return true
			}

			commit('LOGOUT')
			return false
		} catch (error) {
			console.error('Get user info failed:', error)
			// 出现错误时清除用户状态
			commit('LOGOUT')
			return false
		}
	}
}

export default {
	namespaced: true,
	state,
	getters,
	mutations,
	actions
}
