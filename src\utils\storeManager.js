import {
    unicodeTimeString
} from './date'

function decodeData(data){
    try {
        return JSON.parse(decodeURIComponent(data));
    } catch(err){
        return null;
    }
}

export default {
    store(dataKey, managerKey, data, expires) {
        let now = new Date().getTime();
        expires = expires || '4h'; //默认缓存设置4小时有效
        let maxAge = now + unicodeTimeString(expires)
        let obj = {
            updateTime: now,
            expires: maxAge
        }
        obj[dataKey] = data;
        localStorage.setItem(dataKey + '_' + managerKey, encodeURIComponent(JSON.stringify(obj))); //缓存本地数据
    },
    get(data<PERSON>ey, managerKey) {
        let localValue = decodeData(localStorage[dataKey + '_' + managerKey]);
        if (localValue && localValue.expires && localValue.expires > new Date().getTime()) {
            return localValue[dataKey];
        }else{
            return null;
        }
    },
    getIgnoreMaxAge(data<PERSON><PERSON>, manager<PERSON>ey) {
        return decodeData(localStorage[dataKey + '_' + manager<PERSON>ey]);
    },
    clear(data<PERSON><PERSON>, managerKey) {
        localStorage.removeItem(dataKey + '_' + managerKey);
    },
    clearAll() {
        localStorage.clear();
    }
}