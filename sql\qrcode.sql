CREATE TABLE `t_qrcode` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int(11) DEFAULT NULL COMMENT '归属用户ID',
  `user_name` varchar(50) DEFAULT NULL COMMENT '用户名（冗余字段，便于查询）',
  `user_mobile` varchar(20) DEFAULT NULL COMMENT '用户手机号（冗余字段，便于查询）',
  `user_type` int(11) DEFAULT NULL COMMENT '用户类型（冗余字段）',
  `qr_code_url` varchar(500) NOT NULL COMMENT '二维码图片URL',
  `description` text COMMENT '描述信息',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否生效：1-启用，0-禁用',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_user_name` (`user_name`),
  KEY `idx_user_mobile` (`user_mobile`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信二维码管理表';
