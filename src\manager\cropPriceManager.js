import http from '@/utils/http';

function queryAllPrices (companyName, cropId, date, pageIndex, pageSize) {
    return http.post('admin/priceCompany/priceDaily/load', { companyName, cropId, date, pageIndex, pageSize })
}

function addPrice (cropId, companyId, companyName, price, unit, date) {
    return http.post('admin/priceCompany/price/add', { cropId, companyId, companyName, price, unit, date })
}

function updatePrice (id, cropId, companyId, price, date) {
    return http.put('admin/priceCompany/price/update/' + id, { cropId, companyId, price, date })
}

export default {
    queryAllPrices,
    addPrice,
    updatePrice
}