import Layout from '@/layout'
import Login from '@/views/Login.vue'
import OrderList from '@/views/order/List.vue'
import CropCompany from '@/views/price/Company.vue'
import CompanyDetail from '@/views/price/CompanyDetail.vue'
import CropPrice from '@/views/price/CropPrice.vue'
import CropOther from '@/views/price/CropOther.vue'
import Transport from '@/views/transport/index.vue'
import UserList from '@/views/user/List.vue'
import InfoList from '@/views/info/List.vue'
import OpenList from '@/views/home/<USER>'
import CropList from '@/views/system/CropList.vue'
import RegionList from '@/views/system/RegionList.vue'
import AreaList from '@/views/system/AreaList.vue'
import SystemConfig from '@/views/system/SystemConfig.vue'
import QrcodeList from '@/views/qrcode/List.vue'

// 公共路由，无需登录即可访问
export const constantRoutes = [
	{
		path: '/login',
		name: 'Login',
		component: Login,
		meta: {
			hidden: true // 在侧边栏中隐藏
		}
	}
]

// 需要登录才能访问的路由
export const asyncRoutes = [
	{
		path: '/system',
		name: 'System',
		component: Layout,
		meta: {
			title: '系统设置',
			icon: 'el-icon-setting'
		},
		children: [
			{
				path: 'config',
				name: 'SysConfig',
				component: SystemConfig,
				meta: {
					title: '小程序启动配置'
				}
			},
			{
				path: 'crop',
				name: 'CropList',
				component: CropList,
				meta: {
					title: '农作物管理'
				}
			},
			{
				path: 'region',
				name: 'RegionList',
				component: RegionList,
				meta: {
					title: '价格页区域管理'
				}
			},
			{
				path: 'area',
				name: 'AreaList',
				component: AreaList,
				meta: {
					title: '行政区域管理'
				}
			}
		]
	},
	{
		path: '/price',
		name: 'Price',
		component: Layout,
		meta: {
			title: '价格管理',
			icon: 'el-icon-money'
		},
		children: [
			{
				path: 'company',
				name: 'CropCompany',
				component: CropCompany,
				meta: {
					title: '收购厂商'
				}
			},
			{
				path: 'companyDetail',
				name: 'CropCompanyDetail',
				component: CompanyDetail,
				meta: {
					title: '收购标准'
				}
			},
			{
				path: 'list',
				name: 'CropPrice',
				component: CropPrice,
				meta: {
					title: '收购价格'
				}
			},
			{
				path: 'port',
				name: 'CropOther',
				component: CropOther,
				meta: {
					title: '港口信息'
				}
			},
			{
				path: 'transport',
				name: 'Transport',
				component: Transport,
				meta: {
					title: '运输信息'
				}
			}
		]
	},
	{
		path: '/open',
		name: '',
		component: Layout,
		meta: {
			title: '应用启动统计',
			icon: 'el-icon-switch-button'
		},
		children: [
			{
				path: 'log',
				name: 'OpenManager',
				component: OpenList,
				meta: {
					title: '应用启动统计'
				}
			}
		]
	},
	{
		path: '/order',
		name: '',
		component: Layout,
		meta: {
			title: '订单列表',
			icon: 'el-icon-menu'
		},
		children: [
			{
				path: '',
				name: 'OrderManager',
				component: OrderList,
				meta: {
					title: '订单列表'
				}
			}
		]
	},
	{
		path: '/info',
		name: '',
		component: Layout,
		meta: {
			title: '资讯管理',
			icon: 'el-icon-news'
		},
		children: [
			{
				path: 'list',
				name: 'InfoList',
				component: InfoList,
				meta: {
					title: '资讯管理'
				}
			}
		]
	},
	{
		path: '/qrcode',
		component: Layout,
		meta: {
			title: '微信二维码管理',
			icon: 'el-icon-picture'
		},
		children: [
			{
				path: '',
				name: 'QrcodeManager',
				component: QrcodeList,
				meta: {
					title: '微信二维码管理'
				}
			}
		]
	},
	{
		path: '/user',
		component: Layout,
		meta: {
			title: '用户管理',
			icon: 'el-icon-user-solid'
		},
		children: [
			{
				path: '',
				name: 'QwcUser',
				component: UserList,
				meta: {
					title: '用户管理'
				}
			}
		]
	},
	{
		path: '*',
		redirect: '/system/config'
	}
]
