const dbController = require('./dbController')
const cacheController = require('./cacheController')
const { resFilter, getKeysObj } = require('../utils/filter')
const { PRICE_COMPANY, PRICE_COMPANY_DETAIL } = require('../db').tableNames
const tableKeys = ['name', 'cityCode', 'level', 'cropId']
const companyDetailSql = require('../sql/companyDetailSql')

function buildSpecialSql(cityCode, cropId, name) {
	if (cityCode || cropId || name) {
		let sqlArr = []
		if (cityCode) {
			let lastNotZeroIndex
			let cityCodeString = cityCode.toString()
			cityCodeString.split('').forEach((item, index) => {
				if (item !== '0') {
					lastNotZeroIndex = index
				}
			})
			const cityCodeNoZero = cityCodeString.substr(0, lastNotZeroIndex + 1) * 1
			const maxCityCode =
				(cityCodeNoZero + 1) * Math.pow(10, cityCodeString.length - (lastNotZeroIndex + 1))
			sqlArr.push('(`city_code` >= ' + cityCode + ' AND `city_code` < ' + maxCityCode + ')')
		}
		if (cropId) {
			sqlArr.push('`crop_id` = ' + cropId)
		}
		if (name) {
			sqlArr.push('`name` like "%' + name + '%"')
		}
		return sqlArr.join(' AND ')
	} else {
		return null
	}
}

function countCompanies(cityCode, cropId, name) {
	return new Promise(function (resolve, reject) {
		dbController.count(
			PRICE_COMPANY,
			{ _special: buildSpecialSql(cityCode, cropId, name) },
			(err, rows) => {
				if (err) {
					reject(err)
				} else {
					resolve(rows[0]['count(*)'])
				}
			}
		)
	})
}

function queryCompanyById(id) {
	return dbController.query(PRICE_COMPANY, { id }, null).then(rows => {
		if (rows.length >= 1) {
			return resFilter(rows[0])
		} else {
			throw new Error('数据不存在')
		}
	})
}

// 拉取全表的收购厂商数据
function queryAllCompanies() {
	return new Promise(function (resolve, reject) {
		dbController.query(PRICE_COMPANY, null, null, (err, rows) => {
			if (err) {
				reject(err)
			} else {
				resolve(
					rows.map(item => {
						item.provincePreCode = Math.floor(item.city_code / 10000000000) // 省编码，只取前面两位数字
						return resFilter(item)
					})
				)
			}
		})
	})
}

// 后台查询收购厂商的详细列表
function queryCompanyDetails(companyId, cropId, pageIndex, pageSize) {
	return new Promise(function (resolve, reject) {
		const queryOptions = {
			companyId,
			cropId
		}
		dbController.count(PRICE_COMPANY_DETAIL, queryOptions, (err, rows) => {
			if (err) {
				reject(err)
			} else {
				const total = rows[0]['count(*)']
				if (total > 0) {
					dbController.query(
						PRICE_COMPANY_DETAIL,
						queryOptions,
						{ pageIndex, pageSize, orderBy: 'id', orderSort: 'DESC' },
						(err, rows) => {
							if (err) {
								reject(err)
							} else {
								resolve({
									total,
									list: rows.map(item => resFilter(item))
								})
							}
						}
					)
				} else {
					resolve({
						list: [],
						total: 0
					})
				}
			}
		})
	})
}

function addCompanyDetail(params) {
	return new Promise((resolve, reject) => {
		const { companyId, cropId, desc, photos } = params
		dbController.add(
			PRICE_COMPANY_DETAIL,
			{
				companyId,
				cropId,
				desc,
				photos
			},
			(err, rows) => {
				if (err) {
					reject(err)
				} else {
					resolve('收购厂商收购详情录入成功')
				}
			}
		)
	})
}

function editCompanyDetail(id, params) {
	return new Promise((resolve, reject) => {
		const { companyId, cropId, desc, photos } = params
		dbController.update(
			PRICE_COMPANY_DETAIL,
			{ id },
			{
				companyId,
				cropId,
				desc,
				photos
			},
			(err, rows) => {
				if (err) {
					reject(err)
				} else {
					resolve('收购厂商详细更新成功')
				}
			}
		)
	})
}

// 查询某个收购厂商的最新的价格标准，该方法属于老方法
// TODO: 发布新版本后续不在做支持
function queryCompanyLatestDetail(companyId) {
	return new Promise((resolve, reject) => {
		const detailMap = {
			cornDetail: null,
			wheatDetail: null
		}
		let finishCount = 0
		dbController.query(
			PRICE_COMPANY_DETAIL,
			{ companyId, cropId: 1 },
			{
				pageIndex: 0,
				pageSize: 1
			},
			(err, rows) => {
				if (err) {
					reject(err)
				} else {
					finishCount++
					if (rows[0]) {
						detailMap.cornDetail = resFilter(rows[0])
					}
					if (finishCount === 2) {
						resolve(detailMap)
					}
				}
			}
		)
		dbController.query(
			PRICE_COMPANY_DETAIL,
			{ companyId, cropId: 2 },
			{
				pageIndex: 0,
				pageSize: 1
			},
			(err, rows) => {
				if (err) {
					reject(err)
				} else {
					finishCount++
					if (rows[0]) {
						detailMap.wheatDetail = resFilter(rows[0])
					}
					if (finishCount === 2) {
						resolve(detailMap)
					}
				}
			}
		)
	})
}

// 查询一个收购厂商的最新的价格详情信息，每个作物只查询最新的一条记录
function queryCropLatestDetails(companyId) {
	return dbController.dbConnect(companyDetailSql.queryLatest, [companyId, companyId]).then(rows => {
		return rows.map(item => resFilter(item))
	})
}

function queryCompanyHasUpdate(startDate) {
	// 查询今天存在标准变更的收购厂商
	return new Promise(function (resolve, reject) {
		dbController.query(
			PRICE_COMPANY_DETAIL,
			{ _special: '`update_time` >= "' + startDate + '"' },
			null,
			(err, rows) => {
				if (err) {
					reject(err)
				} else {
					resolve(
						rows.map(item => {
							const { companyId, updateTime, createTime } = resFilter(item)
							return { companyId, updateTime, createTime }
						})
					)
				}
			}
		)
	})
}

//  查询某个作物的所有收购厂商并放入缓存中
function queryCompanyByCrop(cropId) {
	return new Promise(function (resolve, reject) {
		dbController.query(PRICE_COMPANY, null, null, (err, rows) => {
			if (err) {
				reject(err)
			} else {
				cacheController.setCompanies(cropId, rows) //将最新查询放入缓存
				resolve(rows)
			}
		})
	})
}

//  优先从缓存中获取某个作物的所有收购厂商，获取不到时查询数据库
function fetchCompanyByCrop(cropId) {
	return new Promise(function (resolve, reject) {
		let cacheValue = cacheController.getCompanies(cropId)
		if (cacheValue) {
			resolve(cacheValue.map(item => resFilter(item)))
		} else {
			queryCompanyByCrop(cropId)
				.then(queryValue => {
					resolve(queryValue.map(item => resFilter(item)))
				})
				.catch(err => {
					reject(err)
				})
		}
	})
}

function queryCompanies(cityCode, cropId, name, pageIndex, pageSize) {
	return new Promise(function (resolve, reject) {
		dbController.query(
			PRICE_COMPANY,
			{ _special: buildSpecialSql(cityCode, cropId, name) },
			{ pageIndex, pageSize },
			(err, rows) => {
				if (err) {
					reject(err)
				} else {
					resolve(rows.map(item => resFilter(item)))
				}
			}
		)
	})
}

function addCompany(reqBody) {
	const newCompany = getKeysObj(reqBody, tableKeys)
	return dbController.add(PRICE_COMPANY, newCompany).then(() => '收购厂商添加成功')
}

function updateCompany(id, reqBody) {
	const newCompany = getKeysObj(reqBody, tableKeys)
	return dbController.update(PRICE_COMPANY, { id }, newCompany).then(() => '收购厂商更新成功')
}

module.exports = {
	queryAllCompanies,
	queryCompanyDetails,
	addCompanyDetail,
	editCompanyDetail,
	countCompanies,
	queryCompanyById,
	queryCompanies,
	queryCompanyByCrop,
	fetchCompanyByCrop,
	addCompany,
	updateCompany,
	queryCompanyLatestDetail,
	queryCropLatestDetails,
	queryCompanyHasUpdate
}
