const express = require('express');
const router = express.Router();

//控制模块
const logController = require('../../controllers/logController');

router.post('/load', function (req, res) { // 查看日志
    logController.queryLogs(req.body).then(data => {
        res.sendSuccess(data)
    }).catch(err => {
        res.sendMessage(err)
    })
})

router.post('/record/load', function (req, res) { // 查看统计记录
    logController.queryRecords(req.body).then(data => {
        res.sendSuccess(data)
    }).catch(err => {
        res.sendMessage(err)
    })
})

module.exports = router;