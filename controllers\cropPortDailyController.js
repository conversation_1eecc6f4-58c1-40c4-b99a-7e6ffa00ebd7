const dbController = require('./dbController')
const { resFilter } = require('../utils/filter')
const { dateFormat } = require('../utils/common')
const { PRICE_PORT } = require('../db').tableNames;


function countDaily (date) {
    return new Promise(function (resolve, reject) {
        dbController.count(PRICE_PORT, { date }, (err, rows) => {
            if (err) {
                reject(err)
            } else {
                resolve(rows[0]['count(*)']);
            }
        })
    })
}

function queryDaily (date, pageIndex, pageSize) {
    return new Promise(function (resolve, reject) {
        dbController.query(PRICE_PORT, { date }, { pageIndex, pageSize, orderBy: 'id', orderSort: 'DESC' }, (err, rows) => {
            if (err) {
                reject(err)
            } else {
                resolve(rows.map(item => resFilter(item)))
            }
        })
    })
}

function addDaily (port1, port2, carCount, date) {
    return new Promise(function (resolve, reject) {
        const yesterday = dateFormat(new Date(new Date(date).getTime() - 3600000 * 24), 'yyyy-MM-dd');
        dbController.query(PRICE_PORT, { date: yesterday }, null, (err, rows) => {
            if (err) {
                reject(err)
            } else {
                let carMinusYesterday = 0;
                if (rows.length > 0) {
                    carMinusYesterday = carCount - rows[0].car_count;
                }
                dbController.add(PRICE_PORT, { port1, port2, carCount, carMinusYesterday, date }, err => {
                    if (err) {
                        reject(err)
                    } else {
                        resolve('信息添加成功')
                    }
                })
            }
        })
    })
}

function updateDaily (id, port1, port2, carCount, date) {
    return new Promise(function (resolve, reject) {
        dbController.query(PRICE_PORT, { _special: '`id` < ' + id }, { pageIndex: 0, pageSize: 1, orderBy: 'id', orderSort: 'DESC' }, (err, rows) => {
            if (err) {
                reject(err)
            } else {
                let carMinusYesterday = 0;
                if (rows.length > 0) {
                    carMinusYesterday = carCount - rows[0].car_count;
                }
                dbController.update(PRICE_PORT, { id }, { port1, port2, carCount, carMinusYesterday, date }, err => {
                    if (err) {
                        reject(err)
                    } else {
                        resolve('更新成功')
                    }
                })
            }
        })
    })
}

module.exports = {
    countDaily,
    queryDaily,
    addDaily,
    updateDaily
}