-- 行政区划表结构优化 - 确保code字段唯一性
-- 如果表不存在则创建，如果存在则添加唯一索引

-- 创建行政区划表（如果不存在）
CREATE TABLE IF NOT EXISTS `t_city_code` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `code` varchar(20) NOT NULL COMMENT '行政区划代码',
  `name` varchar(100) NOT NULL COMMENT '行政区划名称', 
  `level` int(2) NOT NULL DEFAULT '1' COMMENT '行政级别：1省级，2市级，3区县级，4街道级，5村级',
  `createTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code` (`code`) COMMENT '行政代码唯一索引',
  KEY `idx_level` (`level`) COMMENT '级别索引',
  KEY `idx_name` (`name`) COMMENT '名称索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='行政区划表';

-- 如果表已存在但没有唯一索引，则添加唯一索引（忽略已存在的错误）
-- 注意：如果表中已有重复数据，需要先清理
ALTER TABLE `t_city_code` ADD UNIQUE KEY `uk_code` (`code`);