const ScraperStrategy = require('../ScraperStrategy')

/**
 * 省级粮食和物资储备局通用爬虫策略
 * 支持多个省份的数据抓取
 */
class ProvinceStrategy extends ScraperStrategy {
	constructor(config) {
		super(config)
		this.sourceId = config.sourceId || this.determineSourceId(config.name)
	}

	getStrategyName() {
		return 'ProvinceStrategy'
	}

	getSourceId() {
		return this.sourceId
	}

	async scrapeListPage(params) {
		const { page = 0, category, categoryConfig } = params

		// 使用基类的统一分页策略构建URL
		const url = this.buildPaginationUrl(params)

		try {
			const response = await this.fetchWithRetry(url, {
				headers: this.config.headers
			})

			return this.parseListPageData(response.data, url)
		} catch (error) {
			throw new Error(`抓取列表页${url}失败: ${error.message}`)
		}
	}

	async scrapeDetailPage(url) {
		try {
			const response = await this.fetchWithRetry(url, {
				headers: this.config.headers
			})

			return this.parseDetailPageData(response.data, url)
		} catch (error) {
			throw new Error(`抓取详情页${url}失败: ${error.message}`)
		}
	}

	/**
	 * 解析列表页数据
	 * @param {string} html - HTML内容
	 * @param {string} url - 请求URL
	 * @returns {Array} - 解析结果
	 */
	parseListPageData(html, url) {
		const results = []

		if (this.config.name.includes('山东')) {
			return this.parseShandongListPage(html)
		} else {
			return this.parseCommonListPage(html)
		}
	}

	/**
	 * 解析山东省特殊格式的列表页
	 * @param {string} html - HTML内容
	 * @returns {Array} - 解析结果
	 */
	parseShandongListPage(html) {
		const results = []
		const $ = this.parseHtml(html, { xml: true })

		$('#container record')
			.contents()
			.each((index, element) => {
				if (element.children && element.children[0] && element.children[0].data) {
					const inner$ = this.parseHtml(element.children[0].data, { xml: true })

					inner$('.pagedContent').each((i, recordElement) => {
						const title = $(recordElement).find('a').text().trim()
						const relativeLink = $(recordElement).find('a').attr('href')

						if (!relativeLink || !title.includes(this.config.filterKeyword)) return

						const originalLink = this.convertToAbsoluteUrl(relativeLink, this.config.baseUrl)
						const sourceId = this.extractIdFromUrl(originalLink, /_(\d+)\.html$/)
						const publishTime = $(recordElement).find('td:last').text().trim()

						if (sourceId) {
							results.push(
								this.normalizeData({
									title,
									originalLink,
									sourceId: parseInt(sourceId),
									publishTime,
									category: this.config.category
								})
							)
						}
					})
				}
			})

		return results
	}

	/**
	 * 解析通用格式的列表页
	 * @param {string} html - HTML内容
	 * @returns {Array} - 解析结果
	 */
	parseCommonListPage(html) {
		const results = []
		const $ = this.parseHtml(html)

		$(this.config.selectors.listContainer).each((index, element) => {
			const title = $(element).find(this.config.selectors.titleSelector).text().trim()
			const relativeLink = $(element).find(this.config.selectors.linkSelector).attr('href')

			if (!relativeLink) return

			// 应用过滤条件
			if (this.config.filterKeyword && !title.includes(this.config.filterKeyword)) {
				return
			}

			const originalLink = this.convertToAbsoluteUrl(relativeLink, this.config.baseUrl)
			const sourceId = this.extractIdFromUrl(originalLink, /_(\d+)\.html$/)
			const publishTime = $(element).find(this.config.selectors.dateSelector).text().trim()

			if (sourceId) {
				results.push(
					this.normalizeData({
						title,
						originalLink,
						sourceId: parseInt(sourceId),
						publishTime,
						category: this.config.category
					})
				)
			}
		})

		return results
	}

	/**
	 * 解析详情页数据
	 * @param {string} html - HTML内容
	 * @param {string} url - 请求URL
	 * @returns {Object} - 解析结果
	 */
	parseDetailPageData(html, url) {
		const $ = this.parseHtml(html)

		if (this.config.name.includes('山东')) {
			return this.parseShandongDetailPage($)
		} else if (this.config.name.includes('河北')) {
			return this.parseHebeiDetailPage($)
		} else if (this.config.name.includes('吉林')) {
			return this.parseJilinDetailPage($, url)
		} else if (this.config.name.includes('新疆')) {
			return this.parseXinjiangDetailPage($)
		} else if (this.config.name.includes('内蒙古')) {
			return this.parseNeiMengguDetailPage($)
		}

		return {}
	}

	/**
	 * 解析山东省详情页
	 */
	parseShandongDetailPage($) {
		const container = $('#container')
		const tableEle = $(container).find('table').not('[class]').first()
		const title = $(tableEle).find('.fttt > p').text().trim()
		const content = $(tableEle)
			.find('tbody>tr:nth-child(2)>td>table>tbody>tr:nth-child(2) p')
			.html()

		return { title, content }
	}

	/**
	 * 解析河北省详情页
	 */
	parseHebeiDetailPage($) {
		const container = $('.contentbox')
		const title = $(container).find('h1').text().trim()
		const content = $(container).find('.contentmain .TRS_Editor').html()

		return { title, content }
	}

	/**
	 * 解析吉林省详情页
	 */
	parseJilinDetailPage($, url) {
		const container = $('.content')
		const title = $(container).find('.biaoti_title').text().trim()
		const author = '粮食储备处'
		let content = $(container).find('.TRS_Editor').html()

		// 处理图片相对路径 - 使用基类方法
		content = this.processImageUrls(content, url)

		return { title, author, content }
	}

	/**
	 * 解析新疆详情页
	 */
	parseXinjiangDetailPage($) {
		const container = $('.index_block')
		const title = $(container).find('.subtitle').text().trim()
		const author = $(container)
			.find('.from')
			.text()
			.replace(/来源：/, '')
			.trim()
		const content = $(container).find('.content_p').html()

		return { title, author, content }
	}

	/**
	 * 解析内蒙古详情页
	 */
	parseNeiMengguDetailPage($) {
		const container = $('.box')
		const title = $(container).find('.xl_bt').text().trim()
		const author = $(container).find('#l_lypd').text().trim()
		const content = $(container).find('.trs_editor_view').html()

		return { title, author, content }
	}

	/**
	 * 根据配置名称确定数据源ID
	 */
	determineSourceId(name) {
		const { INFO_SOURCE } = require('../../../constant/enum')

		// 优先匹配具体的数据源名称
		if (name.includes('中国饲料行业信息网') || name.includes('饲料行业'))
			return INFO_SOURCE.ChinaFeedAnalysis.id
		if (
			name.includes('中国农作物行情网') ||
			name.includes('农作物行情') ||
			name.includes('指数分析')
		)
			return INFO_SOURCE.ChinaCropIndex.id

		// 省级粮食储备局匹配
		if (name.includes('山东')) return INFO_SOURCE.SDLscb.id
		if (name.includes('河北')) return INFO_SOURCE.HBLswz.id
		if (name.includes('吉林')) return INFO_SOURCE.JLLswz.id
		if (name.includes('新疆')) return INFO_SOURCE.XJLswz.id
		if (name.includes('内蒙古')) return INFO_SOURCE.NMGLswz.id

		throw new Error(`无法确定数据源ID: ${name}`)
	}
}

module.exports = ProvinceStrategy
