var express = require('express');
var router = express.Router();
const logController = require('../controllers/logController')

router.post('/add', function (req, res, next) {
    logController.addLog(req.body).then(data => {
        res.sendSuccess(data);
    }).catch(err => {
        console.log(err);
        res.sendMessage(err)
    })
});

router.post('/addRecord', function (req, res, next) {
    logController.addActionRecord(req.body).then(data => {
        res.sendSuccess(data);
    }).catch(err => {
        console.log(err);
        res.sendMessage(err)
    })
});

module.exports = router;
