import Vue from 'vue'
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import './styles/app.scss'
import './styles/element-ui-reset.scss'
import App from './App.vue'
import router from './router'
import store from './store'
import { AddDateFormatProto } from './utils/date'

//拓展时间对象原型format方法
AddDateFormatProto()

Vue.use(ElementUI)
Vue.config.productionTip = false

store.dispatch('initBaseData').then(() => {
  new Vue({
    router,
    store,
    render: h => h(App)
  }).$mount('#app')
})
