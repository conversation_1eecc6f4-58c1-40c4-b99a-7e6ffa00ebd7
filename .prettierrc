{"semi": false, "singleQuote": true, "useTabs": false, "tabWidth": 2, "trailingComma": "none", "printWidth": 100, "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "avoid", "endOfLine": "lf", "embeddedLanguageFormatting": "auto", "htmlWhitespaceSensitivity": "css", "insertPragma": false, "jsxSingleQuote": true, "proseWrap": "preserve", "quoteProps": "as-needed", "requirePragma": false, "vueIndentScriptAndStyle": true, "singleAttributePerLine": false}