import http from '@/utils/http'

function queryRegions (name, pageIndex, pageSize) {
	return http.post('admin/region/load', { name, pageIndex, pageSize })
}

// 获取省份的简约版信息
function queryProvinceSimpleList () {
	return http.get('admin/province/preCode')
}

function addRegion (newRegion) {
	return http.post('admin/region/add', newRegion)
}

function editRegion (id, newRegion) {
	return http.put(`admin/region/update/${id}`, newRegion)
}

function deleteRegion (id) {
	return http.delete(`admin/region/delete/${id}`)
}

export default {
	queryRegions,
	queryProvinceSimpleList,
	addRegion,
	editRegion,
	deleteRegion
}
