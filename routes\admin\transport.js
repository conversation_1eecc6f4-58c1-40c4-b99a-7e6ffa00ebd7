const express = require('express')
const router = express.Router()

//控制模块
const transportController = require('../../controllers/transportController')

router.post('/list', function (req, res) {
	transportController
		.queryList(req.body)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

router.get('/list/all', function (req, res) {
	transportController
		.queryAll(req.body)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

router.post('/add', function (req, res) {
	transportController
		.addFactory(req.body)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

router.put('/edit/:id', function (req, res) {
	const { id } = req.params
	transportController
		.editTransportData(id, req.body)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

router.delete('/delete/:id', function (req, res) {
	const { id } = req.params
	transportController
		.deleteFactory(id)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

router.get('/detail/:id', function (req, res) {
	const { id } = req.params
	transportController
		.getInfoById(id)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

router.get('/records/:id', function (req, res) {
	const { id } = req.params
	transportController
		.getTransportHistory(id)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

router.get('/daily-summary', function (req, res) {
	transportController
		.getTransportDailySummary()
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

module.exports = router
