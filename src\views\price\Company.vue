<template>
  <div class="card">
    <div class="search_wrapper">
      <el-cascader
        v-model="searchParams.areaPath"
        :options="areaOptions"
        :props="cascaderProps"
        placeholder="请选择行政区域"
        clearable
        filterable
        :style="{ width: '220px' }"
        @change="onSearchAreaChange"
      />
      <el-select
        v-model="searchParams.cropId"
        placeholder="请选择作物"
        clearable
        :style="{ width: '150px' }"
      >
        <el-option
          v-for="item in crops"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        ></el-option>
      </el-select>
      <el-input v-model="searchParams.name" placeholder="收购厂商名称" clearable></el-input>
      <el-button type="primary" @click="onSearch">查询</el-button>
      <el-button @click="clear">清空</el-button>
      <el-button class="add_btn" type="primary" plain icon="el-icon-plus" @click="showCompanyAdd"
        >新增</el-button
      >
    </div>
    <div class="card-body">
      <el-table
        v-loading="loading"
        :data="tableData"
        border
        fit
        highlight-current-row
        style="width: 100%"
      >
        <el-table-column label="ID" prop="id" align="center" width="80"></el-table-column>
        <el-table-column label="厂商名称" prop="name" align="center"></el-table-column>
        <el-table-column label="收购作物" prop="cropId" align="center" width="100">
          <template slot-scope="{ row }">{{ getCropNameById(row.cropId) }}</template>
        </el-table-column>
        <el-table-column label="计入平均值" prop="level" align="center" width="100">
          <template slot-scope="{ row }">
            <el-tag v-if="row.level" size="mini" type="success">计入</el-tag>
            <el-tag v-else size="mini" type="info">不计入</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="所在区域" prop="cityCode" align="center">
          <template slot-scope="{ row }">{{ getAreaNameByCode(row.cityCode) }}</template>
        </el-table-column>
        <el-table-column label="创建时间" prop="createTime" align="center" width="160">
          <template slot-scope="{ row }">{{
            new Date(row.createTime).format('yyyy-MM-dd hh:mm:ss')
          }}</template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="280">
          <template slot-scope="{ row }">
            <el-button
              type="success"
              size="mini"
              @click="$router.push(`companyDetail?companyId=${row.id}`)"
              >收购标准</el-button
            >
            <el-button type="primary" size="mini" @click="showCompanyEdit(row)">编辑厂商</el-button>
            <el-button size="mini" @click="showPriceInput(row)">录入价格</el-button>
          </template>
        </el-table-column>
      </el-table>
      <Pagination
        v-if="total > 0"
        :total="total"
        :pageIndex.sync="pageIndex"
        :pageSize.sync="pageSize"
        @pagination="loadData"
      />
    </div>
    <el-dialog
      :title="editCompany.id ? '编辑收购厂商' : '新增收购厂商'"
      :visible.sync="companyDialogVisible"
    >
      <el-form label-width="100px">
        <el-form-item label="厂商名称">
          <el-input v-model="editCompany.name" placeholder="请输入收购厂商名称"></el-input>
        </el-form-item>
        <el-form-item label="收购作物">
          <el-select v-model="editCompany.cropId" placeholder="请选择收购作物" style="width: 100%">
            <el-option
              v-for="item in crops"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="计入平均值">
          <el-radio-group v-model="editCompany.level">
            <el-radio :label="1">计入平均值</el-radio>
            <el-radio :label="0">不计入平均值</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="所在区域">
          <el-cascader
            v-model="editCompany.areaPath"
            :options="areaOptions"
            :props="cascaderProps"
            placeholder="请选择省市区"
            clearable
            filterable
            style="width: 100%"
            @change="onAreaCascaderChange"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="companyDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="onCompanyEditConfirm">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="新增报价信息" :visible.sync="priceDialogVisible">
      <el-form label-width="110px">
        <el-form-item label="收购厂商">
          <el-input :value="editPrice.companyName" readonly></el-input>
        </el-form-item>
        <el-form-item label="收购作物">
          <el-select
            v-model="editPrice.cropId"
            placeholder="请选择作物"
            style="width: 100%"
            @change="handleCropChange"
          >
            <el-option
              v-for="item in crops"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="报价">
              <el-input-number v-model="editPrice.price" :precision="4" :step="0.1" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="报价单位">
              <span style="font-size: 15; font-weight: 600">{{ editPrice.unit }}</span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="报价日期">
          <el-date-picker
            v-model="editPrice.date"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择日期"
          ></el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="priceDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="onPriceAddConfirm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import companyManager from '@/manager/companyManager'
  import cropPriceManager from '@/manager/cropPriceManager'
  import areaManager from '@/manager/areaManager'
  import Pagination from '@/components/Pagination'
  import tableMixin from '@/mixins/tableMixin'
  export default {
    name: 'CompanyList',
    components: { Pagination },
    mixins: [tableMixin],
    data() {
      return {
        companyDialogVisible: false,
        priceDialogVisible: false,
        searchParams: {
          areaPath: [], // 搜索的行政区域路径
          area: null, // 搜索的具体区域代码（后端兼容）
          name: null,
          cropId: null
        },
        editCompany: {
          id: null,
          name: null,
          cropId: null,
          level: 1,
          areaPath: [] // 级联选择器的值 [provinceCode, cityCode, areaCode]
        },
        editPrice: {
          companyName: null,
          companyId: null,
          cropId: null,
          price: null,
          unit: '',
          date: null
        },
        areaOptions: [], // 级联选择器的树形数据
        cascaderProps: {
          value: 'code',
          label: 'name',
          children: 'children',
          checkStrictly: false
        },
        allAreaMap: {}, // { code: { code, name, level, parentCode } }
        areaDataLoaded: false, // 标记区域数据是否已加载
        tableDataLoaded: false // 标记表格数据是否已加载
      }
    },
    computed: {
      // 从store获取作物数据，过滤掉有子级的作物（相当于 ignoreChildren: 1）
      crops() {
        return this.$store.state.crop.cropArr.filter(
          crop => !crop.children || crop.children.length === 0
        )
      }
    },
    created() {
      // 重写 tableMixin 的 created，先加载区域数据
      this.initializeData()
    },
    mounted() {
      // mounted 中不需要做任何操作
    },
    methods: {
      // 初始化数据加载序列
      async initializeData() {
        await this.loadAllAreaData()
        this.buildCascaderOptions()
        this.loadData()
      },

      async loadAllAreaData() {
        try {
          const allAreas = await areaManager.getAllAreas()
          allAreas.forEach(item => {
            this.allAreaMap[item.code] = item
          })

          this.areaDataLoaded = true
          console.log('共加载', Object.keys(this.allAreaMap).length, '条行政区划记录')
        } catch (error) {
          console.error('加载行政区划数据失败:', error)
        }
      },

      getAreaNameByCode(areaCode) {
        if (!areaCode) return ''
        if (!this.areaDataLoaded) return '加载中...'

        const area = this.allAreaMap[areaCode]
        if (!area) return `${areaCode}`

        const names = []
        let currentCode = areaCode

        // 向上查找所有父级区域
        while (currentCode && this.allAreaMap[currentCode]) {
          const current = this.allAreaMap[currentCode]
          names.unshift(current.name)
          // 查找父级代码
          if (current.level === 1) break // 省级，没有父级

          if (current.level === 2) {
            // 市级，父级是省
            currentCode = currentCode.toString().substr(0, 2) + '0000000000'
          } else if (current.level === 3) {
            // 区县级，父级是市
            currentCode = currentCode.toString().substr(0, 4) + '00000000'
          } else {
            break
          }
        }

        return names.join(' / ')
      },

      getCropNameById(cropId) {
        if (!cropId) return ''
        const crop = this.crops.find(item => item.id === cropId)
        return crop ? crop.name : `未知作物: ${cropId}`
      },

      buildCascaderOptions() {
        if (Object.keys(this.allAreaMap).length === 0) return
        // 获取所有省份
        const provinces = Object.values(this.allAreaMap)
          .filter(item => item.level === 1)
          .sort((a, b) => a.code - b.code)

        this.areaOptions = provinces.map(province => {
          const provinceNode = {
            code: province.code,
            name: province.name,
            children: []
          }
          // 获取该省份下的所有城市
          const cities = Object.values(this.allAreaMap)
            .filter(
              item =>
                item.level === 2 &&
                item.code.toString().substr(0, 2) === province.code.toString().substr(0, 2)
            )
            .sort((a, b) => a.code - b.code)

          provinceNode.children = cities.map(city => {
            const cityNode = {
              code: city.code,
              name: city.name,
              children: []
            }
            // 获取该城市下的所有区县
            const areas = Object.values(this.allAreaMap)
              .filter(
                item =>
                  item.level === 3 &&
                  item.code.toString().substr(0, 4) === city.code.toString().substr(0, 4)
              )
              .sort((a, b) => a.code - b.code)

            cityNode.children = areas.map(area => ({
              code: area.code,
              name: area.name
            }))

            return cityNode
          })

          return provinceNode
        })

        console.log('级联选择器数据构建完成，共', this.areaOptions.length, '个省份')
      },

      onAreaCascaderChange(value) {
        // value 是选中值的数组，如 ['110000', '110100', '110101']
        this.editCompany.areaPath = value || []
      },

      onSearchAreaChange(value) {
        this.searchParams.areaPath = value || []
        if (value && value.length > 0) {
          // 使用最后一级（最精确）的代码作为查询值
          this.searchParams.area = value[value.length - 1]
        } else {
          this.searchParams.area = null
        }
      },

      // 根据cityCode设置级联选择器的值（编辑时使用）
      setCascaderValueFromCityCode(cityCode) {
        if (!cityCode) {
          this.editCompany.areaPath = []
          return
        }

        const codeStr = String(cityCode)
        const path = []
        const area = this.allAreaMap[codeStr]

        if (!area) {
          console.warn('未找到对应的行政区划:', cityCode)
          this.editCompany.areaPath = []
          return
        }

        // 根据层级构建路径
        if (area.level === 1) {
          // 省级
          path.push(area.code)
        } else if (area.level === 2) {
          // 市级，需要找到对应的省
          const provinceCode = codeStr.substr(0, 2) + '0000000000'
          const province = this.allAreaMap[provinceCode]
          if (province) {
            path.push(province.code, area.code)
          }
        } else if (area.level === 3) {
          // 区县级，需要找到对应的省和市
          const provinceCode = codeStr.substr(0, 2) + '0000000000'
          const cityCode = codeStr.substr(0, 4) + '00000000'

          const province = this.allAreaMap[provinceCode]
          const city = this.allAreaMap[cityCode]

          if (province && city) {
            path.push(province.code, city.code, area.code)
          } else if (province) {
            // 如果没找到对应市级，尝试找到第一个匹配的市级
            const matchedCity = Object.values(this.allAreaMap).find(
              item =>
                item.level === 2 && item.code.toString().substr(0, 2) === provinceCode.substr(0, 2)
            )
            if (matchedCity) {
              path.push(province.code, matchedCity.code, area.code)
            }
          }
        }

        this.editCompany.areaPath = path
      },

      loadData() {
        // 如果区域数据还没加载完成，等待区域数据加载完成后再加载表格数据
        if (!this.areaDataLoaded) {
          console.log('等待区域数据加载完成...')
          return
        }

        const {
          searchParams: { area, cropId, name },
          pageIndex,
          pageSize
        } = this

        let cityCode = area || null
        this.loading = true
        companyManager
          .queryCompanies(cityCode, cropId, name, pageIndex, pageSize)
          .then(res => {
            const { total, list } = res
            this.total = total
            this.tableData = list
            this.tableDataLoaded = true
          })
          .catch(err => {
            this.$message.error(err)
          })
          .then(() => {
            this.loading = false
          })
      },

      onSearch() {
        this.pageIndex = 0 // 搜索时重置到第一页
        this.loadData()
      },

      clear() {
        this.searchParams.areaPath = []
        this.searchParams.area = null
        this.searchParams.cropId = null
        this.searchParams.name = null
        this.pageIndex = 0 // 清空时重置到第一页
        this.loadData()
      },
      showCompanyAdd() {
        if (this.editCompany.id) {
          // 先前的操作是编辑，清空数据
          this.editCompany = {
            id: null,
            name: null,
            cropId: null,
            level: 1,
            areaPath: []
          }
        }
        this.companyDialogVisible = true
      },
      showCompanyEdit(row) {
        const { id, cityCode, cropId, level, name } = row
        Object.assign(this.editCompany, {
          id,
          cropId,
          level,
          name,
          areaPath: []
        })

        // 根据cityCode设置级联选择器的值
        if (cityCode) {
          this.setCascaderValueFromCityCode(cityCode)
        }

        this.companyDialogVisible = true
      },
      onCompanyEditConfirm() {
        const { id, cropId, level, name, areaPath } = this.editCompany
        if (!name?.trim()) {
          this.$message.error('请输入企业名称')
          return
        }

        if (!areaPath || areaPath.length === 0) {
          this.$message.error('请选择所在区域')
          return
        }

        // 从 areaPath 获取最后一级的区域代码作为 cityCode
        const cityCode = areaPath[areaPath.length - 1]
        if (!cityCode) {
          this.$message.error('请选择具体区域')
          return
        }

        this.loading = true
        const task = id
          ? companyManager.updateCompany(id, cityCode, cropId, level, name)
          : companyManager.addCompany(cityCode, cropId, level, name)
        task
          .then(data => {
            this.companyDialogVisible = false
            this.$message.success(data)
            this.loadData()
          })
          .catch(err => {
            this.$message.error(err)
            this.loading = false
          })
      },
      showPriceInput(row) {
        const { id, name, cropId } = row
        Object.assign(this.editPrice, {
          companyId: id,
          companyName: name,
          cropId,
          date: new Date().format('yyyy-MM-dd')
        })
        this.handleCropChange(cropId)
        this.priceDialogVisible = true
      },

      handleCropChange(id) {
        this.editPrice.unit = this.crops.find(item => item.id === id)?.priceUnit
      },
      onPriceAddConfirm() {
        const { cropId, companyId, companyName, price, unit, date } = this.editPrice
        if (!cropId) {
          this.$message.error('请选择农作物')
        } else if (!price) {
          this.$message.error('请录入收购价')
        } else if (!date) {
          this.$message.error('选择保价日期')
        } else {
          this.loading = true
          cropPriceManager
            .addPrice(cropId, companyId, companyName, price, unit, date)
            .then(data => {
              this.$message.success(data)
              this.loading = false
              this.priceDialogVisible = false
            })
            .catch(err => {
              this.$message.error(err)
            })
            .then(() => {
              this.loading = false
            })
        }
      }
    }
  }
</script>

<style scoped>
  .search_wrapper {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
    flex-wrap: wrap;
  }

  .search_wrapper .el-cascader,
  .search_wrapper .el-select,
  .search_wrapper .el-input {
    flex-shrink: 0;
  }

  .search_wrapper .el-input {
    width: 200px;
  }

  @media (max-width: 768px) {
    .search_wrapper {
      flex-direction: column;
      align-items: stretch;
      gap: 8px;
    }

    .search_wrapper .el-cascader,
    .search_wrapper .el-select,
    .search_wrapper .el-input {
      width: 100% !important;
    }
  }
</style>
