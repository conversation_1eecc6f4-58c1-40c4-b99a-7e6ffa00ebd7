const express = require('express');
const router = express.Router();

const orderController = require('../../controllers/orderController');

router.post('/load', function (req, res) { // 查询订单列表
    orderController.queryOrders(req.body).then(data => {
        res.sendSuccess(data)
    }).catch(err => {
        res.sendMessage(err)
    })
})

router.delete('/delete/:id', function (req, res) { // 删除列表
    const { id } = req.params;
    res.sendMessage('接口未开放')
})

module.exports = router;