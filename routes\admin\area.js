const express = require('express')
const router = express.Router()
const areaController = require('../../controllers/areaController')
const commonController = require('../../controllers/commonController')

// 查询所有城市数据
router.post('/list', async function (req, res) {
	try {
		const options = req.body
		const result = await areaController.queryAreasWithPagination(options)
		res.sendSuccess(result)
	} catch (error) {
		console.error('查询城市列表失败:', error)
		res.sendMessage(`查询城市列表失败: ${error.message}`)
	}
})

// 获取所有行政区划数据
router.get('/all', async function (req, res) {
	try {
		const allAreas = await areaController.queryAllAreas()
		res.sendSuccess(allAreas)
	} catch (error) {
		console.error('获取所有行政区划数据失败:', error)
		res.sendMessage(`获取所有行政区划数据失败: ${error.message}`)
	}
})

// 获取所有省份
router.get('/provinces', async function (req, res) {
	try {
		const provinces = await areaController.getProvinces()
		res.sendSuccess(provinces)
	} catch (error) {
		console.error('获取省份列表失败:', error)
		res.sendMessage(`获取省份列表失败: ${error.message}`)
	}
})

// 根据省份代码获取城市列表
router.get('/cities/:provinceCode', async function (req, res) {
	try {
		const { provinceCode } = req.params
		if (!provinceCode) {
			return res.sendMessage('省份代码不能为空')
		}

		const cities = await areaController.getCitiesByProvince(provinceCode)
		res.sendSuccess(cities)
	} catch (error) {
		console.error('获取城市列表失败:', error)
		res.sendMessage(`获取城市列表失败: ${error.message}`)
	}
})

// 根据城市代码获取区县列表
router.get('/areas/:cityCode', async function (req, res) {
	try {
		const { cityCode } = req.params
		if (!cityCode) {
			return res.sendMessage('城市代码不能为空')
		}

		const areas = await areaController.getCountiesByCity(cityCode)
		res.sendSuccess(areas)
	} catch (error) {
		console.error('获取区县列表失败:', error)
		res.sendMessage(`获取区县列表失败: ${error.message}`)
	}
})

// 根据区县代码获取街道列表
router.get('/streets/:areaCode', async function (req, res) {
	try {
		const { areaCode } = req.params
		if (!areaCode) {
			return res.sendMessage('区县代码不能为空')
		}

		const streets = await areaController.getStreetsByCounty(areaCode)
		res.sendSuccess(streets)
	} catch (error) {
		console.error('获取街道列表失败:', error)
		res.sendMessage(`获取街道列表失败: ${error.message}`)
	}
})

// 通用下级查询接口
router.get('/children/:parentCode', async function (req, res) {
	try {
		const { parentCode } = req.params
		const { level } = req.query

		if (!parentCode) {
			return res.sendMessage('父级代码不能为空')
		}

		const children = await areaController.queryChildrenByParentCode(
			parentCode,
			level ? parseInt(level) : null
		)
		res.sendSuccess(children)
	} catch (error) {
		console.error('获取下级区划失败:', error)
		res.sendMessage(`获取下级区划失败: ${error.message}`)
	}
})

// 添加文件解析处理接口
router.post('/process', async function (req, res) {
	const { url, originalname, size } = req.body
	try {
		const result = await areaController.processUploadedAreaData(url, originalname, size)

		if (result.success) {
			res.sendSuccess({
				message: result.message,
				processedRecords: result.processedRecords,
				totalRecords: result.totalRecords,
				duplicateRecords: result.duplicateRecords
			})
		} else {
			res.sendMessage(result.message)
		}
	} catch (error) {
		console.error('行政区划文件处理失败:', error)
		res.sendMessage(`行政区划文件处理失败: ${error.message}`)
	}
})

module.exports = router
