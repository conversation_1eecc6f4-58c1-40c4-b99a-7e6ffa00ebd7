const fsp = require('fs').promises
const path = require('path')
const formidable = require('formidable')
const { getUuid } = require('../utils/common')
const ossController = require('./ossController')

/**
 * 处理本地文件上传的通用函数
 * @param {import('http').IncomingMessage} req - HTTP请求对象
 * @param {string} subDir - 文件存储的子目录 (e.g., 'images', 'videos')
 * @returns {Promise<string>} 返回文件的相对路径
 */
async function _handleLocalUpload(req, subDir) {
	const uploadDir = path.join(__dirname, '../public/upload/', subDir)
	await fsp.mkdir(uploadDir, { recursive: true })

	const form = formidable({
		uploadDir: uploadDir,
		keepExtensions: true
	})

	const { files } = await new Promise((resolve, reject) => {
		form.parse(req, (err, fields, files) => {
			if (err) {
				return reject(new Error(`文件解析失败: ${err.message}`))
			}
			resolve({ fields, files })
		})
	})

	const allFiles = Object.values(files).flat()
	const uploadedFile = allFiles[0]
	if (!uploadedFile) {
		throw new Error('未检测到有效文件上传')
	}

	const tempFilePath = uploadedFile.filepath
	try {
		const ext = path.extname(uploadedFile.originalFilename)
		const newName = `order_${getUuid(12)}${ext}`
		const newPath = path.join(uploadDir, newName)

		await fsp.rename(tempFilePath, newPath)
		return `/upload/${subDir}/${newName}`
	} catch (renameErr) {
		// 如果重命名失败，尝试删除临时文件
		try {
			await fsp.unlink(tempFilePath)
		} catch (unlinkErr) {
			console.error('临时文件删除失败:', tempFilePath, unlinkErr)
		}
		// 在 async 函数中，直接抛出错误
		throw new Error(`文件保存失败: ${renameErr.message}`)
	}
}

/**
 * 上传图片文件
 * @param {import('http').IncomingMessage} req - HTTP请求对象
 */
function uploadPhoto(req) {
	return _handleLocalUpload(req, 'images')
}

/**
 * 上传视频文件
 * @param {import('http').IncomingMessage} req - HTTP请求对象
 */
function uploadVideo(req) {
	return _handleLocalUpload(req, 'videos')
}

/**
 * 通用文件上传并转存到OSS
 *
 * @param {import('http').IncomingMessage} req - HTTP请求对象，包含文件数据。
 * @param {string} [targetNamespace='common'] - 强制指定的OSS存储目录，具有最高优先级。
 * @param {Object} [options] - 可选的上传配置参数。
 * @param {number} [options.maxFileSize] - 文件大小限制（字节），默认100MB。
 * @param {string[]} [options.allowedMimeTypes] - 允许的MIME类型数组。
 * @param {RegExp} [options.allowedExtensions] - 允许的文件扩展名正则表达式。
 * @param {string} [options.filePrefix] - 文件名前缀。
 * @param {string} [options.errorMessage] - 自定义的文件类型错误信息。
 * @returns {Promise<{url: string, fields: object}>} 返回包含OSS URL和原始表单字段的对象。
 */
async function uploadFileToOSS(req, targetNamespace = 'common', options = {}) {
	const tempDir = path.join(__dirname, '../temp')
	await fsp.mkdir(tempDir, { recursive: true })
	const {
		maxFileSize = 100 * 1024 * 1024, // 默认100MB
		allowedMimeTypes = null,
		allowedExtensions = null,
		filePrefix = '',
		errorMessage = '文件类型不符合要求'
	} = options

	const form = formidable({
		uploadDir: tempDir,
		keepExtensions: true,
		maxFileSize: maxFileSize,
		filename: (name, ext) => {
			const prefix = filePrefix ? `${filePrefix}-` : ''
			return `${prefix}${name}-${Date.now()}${ext}`
		},
		filter: function ({ name, originalFilename, mimetype }) {
			if (allowedMimeTypes || allowedExtensions) {
				let isValidMimeType = true
				let isValidExtension = true

				if (allowedMimeTypes) {
					isValidMimeType = allowedMimeTypes.includes(mimetype)
				}

				if (allowedExtensions) {
					isValidExtension = allowedExtensions.test(originalFilename)
				}

				if (!isValidMimeType || !isValidExtension) {
					throw new Error(errorMessage)
				}
			}

			return true
		}
	})

	// 监听进度事件
	form.on('progress', (bytesReceived, bytesExpected) => {
		if (bytesExpected > 0) {
			const percent = ((bytesReceived / bytesExpected) * 100).toFixed(2)
			console.log(`文件上传进度: ${percent}%`)
		}
	})

	const { fields, files } = await new Promise((resolve, reject) => {
		form.parse(req, (err, fields, files) => {
			if (err) {
				if (err.code === 'LIMIT_FILE_SIZE') {
					const sizeLimitMB = Math.round(maxFileSize / (1024 * 1024))
					return reject(new Error(`文件大小不能超过${sizeLimitMB}MB`))
				}
				return reject(new Error(`文件解析失败: ${err.message}`))
			}
			resolve({ fields, files })
		})
	})
	// 动态查找第一个上传的文件，而不是硬编码 'file' 字段。
	// 这使得接口更健壮，能适应不同的前端字段名（如 'image', 'upload' 等）。
	const allFiles = Object.values(files).flat()
	const uploadedFile = allFiles[0]

	if (!uploadedFile) {
		throw new Error('未检测到有效文件上传')
	}

	const tempFilePath = uploadedFile.filepath
	try {
		await fsp.access(tempFilePath)

		// 安全策略：直接使用后端调用时强制指定的 targetNamespace。
		// 忽略任何前端通过表单传递的 'namespace' 字段，防止安全漏洞。
		const ossUrl = await ossController.uploadToOSS(tempFilePath, targetNamespace)

		const enhancedFields = {
			...fields,
			originalname: uploadedFile.originalFilename,
			size: uploadedFile.size,
			mimetype: uploadedFile.mimetype
		}

		return { url: ossUrl, fields: enhancedFields }
	} catch (uploadErr) {
		throw new Error(`文件处理或上传OSS失败: ${uploadErr.message}`)
	} finally {
		try {
			await fsp.unlink(tempFilePath)
		} catch (unlinkErr) {
			console.error('临时文件删除失败:', tempFilePath, unlinkErr)
		}
	}
}

module.exports = {
	uploadPhoto,
	uploadVideo,
	uploadFileToOSS
}
