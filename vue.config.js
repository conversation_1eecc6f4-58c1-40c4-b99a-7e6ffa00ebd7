const { defineConfig } = require('@vue/cli-service')
const isProd = process.env.NODE_ENV === 'production'
const indexPath = isProd ? 'admin/index.html' : 'index.html'

module.exports = defineConfig({
  transpileDependencies: true,
  productionSourceMap: false,
  indexPath,
  lintOnSave: !isProd,
  devServer: {
    open: true,
    host: '0.0.0.0',
    port: 8080,
    proxy: {
      '/api': {
        target: process.env.VUE_APP_API_BASE_URL,
        changeOrigin: true,
        secure: false
      }
    }
  },

  configureWebpack: config => {
    if (isProd) {
      config.optimization = {
        splitChunks: {
          chunks: 'all',
          cacheGroups: {
            vendor: {
              name: 'chunk-vendors',
              test: /[\\/]node_modules[\\/]/,
              priority: 10,
              chunks: 'initial'
            },
            elementUI: {
              name: 'chunk-elementUI',
              priority: 20,
              test: /[\\/]node_modules[\\/]_?element-ui(.*)/
            }
          }
        }
      }
    }
  },

  // CSS相关配置
  css: {
    extract: isProd,
    sourceMap: !isProd
  }
})
