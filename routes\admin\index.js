const express = require('express')
const router = express.Router()

//控制模块
const logController = require('../../controllers/logController')
const userController = require('../../controllers/userController')
const systemController = require('../../controllers/systemController')
const regionController = require('../../controllers/regionController')

// 管理后台子路由
const cropConfigRouter = require('./cropConfig')
const logsRouter = require('./logs')
const orderRouter = require('./order')
const orderConfigRouter = require('./orderConfig')
const priceCompanyRouter = require('./priceCompany')
const regionRouter = require('./region')
const userRouter = require('./user')
const infoRouter = require('./info')
const transportRouter = require('./transport')
const wechatMessageRouter = require('./wechatMessage')
const qrcodeRouter = require('./qrcode')
const areaRouter = require('./area')

router.use('/cropConfig', cropConfigRouter)
router.use('/logs', logsRouter)
router.use('/order', orderRouter)
router.use('/orderConfig', orderConfigRouter)
router.use('/priceCompany', priceCompanyRouter)
router.use('/region', regionRouter)
router.use('/user', userRouter)
router.use('/info', infoRouter)
router.use('/transport', transportRouter)
router.use('/wechatMsg', wechatMessageRouter)
router.use('/qrcode', qrcodeRouter)
router.use('/area', areaRouter)

router.get('/current/userInfo', function (req, res) {
	// 加载管理员信息
	const { userId } = req.body
	userController
		.getUserInfo(userId)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

router.post('/log/record/load', function (req, res) {
	// 查询用户分享记录列表
	logController
		.queryRecords(req.body)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

router.get('/system/user/load', function (req, res) {
	// 查询系统管理员
	userController
		.queryAllAdmins()
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

router.put('/sysConfig', function (req, res) {
	// 更新小程序配置
	systemController
		.updateConfig(req.body)
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

router.get('/province/preCode', function (req, res) {
	//查询用户列表
	regionController
		.queryProvincePreCode()
		.then(data => {
			res.sendSuccess(data)
		})
		.catch(err => {
			res.sendMessage(err)
		})
})

module.exports = router
