// import Tesseract from 'tesseract.js';
const Tesseract = require('tesseract.js')

function getImgInfo (url) {
    // console.log(url);
    // url = 'https://tesseract.projectnaptha.com/img/eng_bw.png'
    url = 'http://www.grainmarket.com.cn:8801/Web/Uploads/ArtImg/20230814083319428.PNG';
    return new Promise(function (resolve, reject) {
        Tesseract.recognize(
            url,
            'eng',
            {
                logger: m => {
                    console.log(1111)
                    console.log(m)
                }
            }
        ).then(({ data: { text } }) => {
            console.log(2222)
            console.log(text);
            resolve(text)
            // console.log(text);
        }).catch(err => {
            reject(err)
        })
    })
}



module.exports = {
    getImgInfo
}

