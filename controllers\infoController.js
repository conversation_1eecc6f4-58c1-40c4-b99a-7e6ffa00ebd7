const dbController = require('./dbController')
const { resFilter, getKeysObj } = require('../utils/filter')
const { INFORMATION } = require('../db').tableNames
const ScraperManager = require('../services/scraper/ScraperManager')
const ScraperStrategyFactory = require('../services/scraper/ScraperStrategyFactory')

const infoTableKeys = [
	'title',
	'cropIds',
	'content',
	'source',
	'sourceId',
	'author',
	'photos',
	'files',
	'originalLink',
	'summary',
	'publishTime',
	'category'
]

// 添加资讯信息
async function addInfo(newInfo) {
	try {
		const total = await dbController.countId(INFORMATION, { originalLink: newInfo.originalLink })
		if (total > 0) {
			throw new Error('资讯已存在')
		}

		const newData = getKeysObj(newInfo, infoTableKeys)
		await dbController.add(INFORMATION, newData)
		return '资讯添加成功'
	} catch (error) {
		throw error
	}
}

// 编辑资讯信息
async function editInfo(id, newInfo) {
	try {
		const total = await dbController.countId(INFORMATION, { id })
		if (total === 0) {
			throw new Error('数据不存在')
		}

		const newData = getKeysObj(newInfo, infoTableKeys)
		await dbController.update(INFORMATION, { id }, newData)
		return '资讯更新成功'
	} catch (error) {
		throw error
	}
}

// 删除资讯信息
function deleteInfo(id) {
	return dbController.deleteById(INFORMATION, id)
}

// 查询单个资讯，可以返回全部的字段
function getInfoById(id) {
	return dbController.queryById(INFORMATION, id).then(data => resFilter(data))
}

// 列表查询，将不返回 content 字段
async function getInfoList(pageIndex, pageSize, reqBody) {
	const { source, publishTime, cropId, title, summary } = reqBody
	const queryOptions = {
		source: source && source.split(','),
		publishTime,
		'cropIds.findIn': cropId,
		'title.like': title,
		'summary.like': summary
	}

	try {
		const total = await dbController.countId(INFORMATION, queryOptions)
		if (total === 0) {
			return { total: 0, list: [] }
		}

		// 列表查询，指定特定字段来进行查询
		const columns = ['id', 'updateTime', 'createTime']
			.concat(infoTableKeys)
			.filter(name => name != 'content')

		const rows = await dbController.queryColumns(INFORMATION, columns, queryOptions, {
			pageIndex,
			pageSize,
			orderBy: 'publish_time'
		})

		return {
			total,
			list: rows.map(item => resFilter(item))
		}
	} catch (error) {
		throw error
	}
}

/**
 * 初始化数据源数据（首次大量抓取）
 * @param {number} sourceId - 数据源ID
 * @param {number} pagesToScrape - 要抓取的页数，默认3页
 * @returns {Promise} - 抓取结果
 */
async function initializeSourceData(sourceId, pagesToScrape = 3) {
	try {
		return await ScraperManager.initializeSourceData(sourceId, { pagesToScrape })
	} catch (error) {
		throw new Error(`初始化数据源${sourceId}失败: ${error.message}`)
	}
}

/**
 * 刷新数据源数据（增量更新）
 * @param {number} sourceId - 数据源ID
 * @param {number} pagesToScrape - 要抓取的页数，默认1页
 * @returns {Promise} - 抓取结果
 */
async function refreshSourceData(sourceId, pagesToScrape = 1) {
	try {
		return await ScraperManager.updateLatestData(sourceId, { pagesToScrape })
	} catch (error) {
		throw new Error(`刷新数据源${sourceId}失败: ${error.message}`)
	}
}

// 批量更新所有数据源
async function batchUpdateAllSources(options = {}) {
	// 动态获取所有支持的数据源，避免硬编码
	const supportedSources = ScraperStrategyFactory.getSupportedSources()
	const sources = supportedSources.map(source => source.id)

	return await ScraperManager.batchUpdateSources(sources, options)
}

// 获取支持的数据源列表
function getSupportedSources() {
	return ScraperStrategyFactory.getSupportedSources()
}

// 通用的数据源操作函数
async function operateDataSource(sourceId, operation, options = {}) {
	const validOperations = ['initialize', 'update', 'info']

	if (!validOperations.includes(operation)) {
		throw new Error(`不支持的操作类型: ${operation}`)
	}

	try {
		switch (operation) {
			case 'initialize':
				return await ScraperManager.initializeSourceData(sourceId, options)
			case 'update':
				return await ScraperManager.updateLatestData(sourceId, options)
			case 'info':
				return ScraperStrategyFactory.getSourceConfig(sourceId)
			default:
				throw new Error(`未实现的操作: ${operation}`)
		}
	} catch (error) {
		throw new Error(`操作数据源${sourceId}失败: ${error.message}`)
	}
}

module.exports = {
	addInfo,
	editInfo,
	deleteInfo,
	getInfoList,
	getInfoById,

	initializeSourceData, // 初始化数据源
	refreshSourceData, // 刷新数据源

	batchUpdateAllSources,
	getSupportedSources,
	operateDataSource
}
