<template>
  <el-dialog
    :title="isEdit ? '编辑微信二维码' : '新增微信二维码'"
    :visible.sync="visible"
    width="600px"
    @close="handleClose"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-width="120px">
      <el-form-item label="归属用户" prop="userId">
        <template v-if="!isEdit">
          <el-autocomplete
            v-model="userSearchText"
            :fetch-suggestions="queryUserSuggestions"
            placeholder="请输入用户名或手机号搜索"
            clearable
            style="width: 100%"
            :trigger-on-focus="false"
            @select="handleUserSelect"
            @clear="handleUserClear"
          >
            <template slot-scope="{ item }">
              <div class="user-suggestion-item">
                <span class="user-name">{{ item.name }}</span>
                <span class="user-mobile">{{ item.mobile }}</span>
                <span class="user-type">{{ getUserTypeText(item.type) }}</span>
              </div>
            </template>
          </el-autocomplete>
        </template>
        <template v-else>
          <el-input
            :value="`${form.userName} (${form.userMobile})`"
            readonly
            style="width: 100%"
          ></el-input>
          <div style="color: #999; font-size: 12px">用户信息不可修改</div>
        </template>
      </el-form-item>

      <el-form-item label="二维码图片" prop="qrCodeUrl">
        <el-upload
          class="qrcode-uploader"
          :action="uploadAction"
          :headers="uploadHeaders"
          :show-file-list="false"
          :on-success="handleUploadSuccess"
          :on-error="handleUploadError"
          :before-upload="beforeUpload"
          accept="image/*"
        >
          <img v-if="form.qrCodeUrl" :src="form.qrCodeUrl" class="qrcode-image" />
          <i v-else class="el-icon-plus qrcode-uploader-icon"></i>
        </el-upload>
        <div style="color: #999; font-size: 12px">支持 jpg、png 格式，建议尺寸 400x400 像素</div>
      </el-form-item>

      <el-form-item label="状态" prop="isActive">
        <el-radio-group v-model="form.isActive">
          <el-radio :label="1">启用</el-radio>
          <el-radio :label="0">禁用</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="描述信息" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请输入二维码描述信息"
          maxlength="100"
          show-word-limit
        ></el-input>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取 消</el-button>
      <el-button type="primary" :loading="loading" @click="handleConfirm">
        {{ isEdit ? '更 新' : '确 定' }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
	import qrcodeManager from '@/manager/qrcodeManager'
	import userManager from '@/manager/userManager'

	export default {
		name: 'QrcodeDialog',
		props: {
			visible: {
				type: Boolean,
				default: false
			},
			item: {
				type: Object,
				default: null
			},
			userTypeMap: {
				type: Object,
				default: () => ({})
			}
		},
		data() {
			return {
				loading: false,
				form: {
					id: null,
					userId: null,
					qrCodeUrl: '',
					description: '',
					isActive: 1
				},
				userSearchText: '',
				rules: {
					userId: [{ required: true, message: '请选择归属用户', trigger: 'change' }],
					qrCodeUrl: [{ required: true, message: '请上传二维码图片', trigger: 'change' }],
					isActive: [{ required: true, message: '请选择状态', trigger: 'change' }]
				},
				uploadAction: '/api/v1/common/upload/unified?type=qrcode',
				uploadHeaders: {}
			}
		},
		computed: {
			isEdit() {
				return this.item && this.item.id
			}
		},
		watch: {
			visible(val) {
				if (val) {
					this.initForm()
				}
			},
			item: {
				handler(val) {
					if (val && this.visible) {
						this.initForm()
					}
				},
				deep: true
			}
		},
		methods: {
			initForm() {
				if (this.isEdit) {
					this.form = {
						id: this.item.id,
						userId: this.item.userId,
						userName: this.item.userName || '',
						userMobile: this.item.userMobile || '',
						qrCodeUrl: this.item.qrCodeUrl,
						description: this.item.description || '',
						isActive: this.item.isActive
					}
				} else {
					this.resetForm()
				}
			},

			// 重置表单（新建模式）
			resetForm() {
				this.form = {
					id: null,
					userId: null,
					qrCodeUrl: '',
					description: '',
					isActive: 1
				}
				this.userSearchText = ''
				if (this.$refs.form) {
					this.$refs.form.clearValidate()
				}
			},

			queryUserSuggestions(queryString, callback) {
				if (!queryString || queryString.length < 1) {
					callback([])
					return
				}

				userManager
					.queryUsers({ name: queryString, pageIndex: 0, pageSize: 10 })
					.then(res => {
						const suggestions = res.list || []
						const formattedSuggestions = suggestions.map(user => ({
							...user,
							value: `${user.name} (${user.mobile})`
						}))
						callback(formattedSuggestions)
					})
					.catch(err => {
						console.error('搜索用户失败:', err)
						callback([])
					})
			},

			handleUserSelect(item) {
				this.form.userId = item.id
				this.userSearchText = `${item.name} (${item.mobile})`
			},

			handleUserClear() {
				this.form.userId = null
				this.userSearchText = ''
			},

			getUserTypeText(type) {
				return this.userTypeMap[type] || '普通用户'
			},

			beforeUpload(file) {
				const isImage = file.type.indexOf('image/') === 0
				const isLt2M = file.size / 1024 / 1024 < 10

				if (!isImage) {
					this.$message.error('只能上传图片文件!')
					return false
				}
				if (!isLt2M) {
					this.$message.error('上传图片大小不能超过 10MB!')
					return false
				}
				return true
			},

			handleUploadSuccess(response) {
				if (response.code === 1) {
					this.form.qrCodeUrl = response.data.url
					this.$message.success('图片上传成功')
				} else {
					this.$message.error(response.message || '上传失败')
				}
			},

			handleUploadError() {
				this.$message.error('图片上传失败，请重试')
			},

			handleCancel() {
				this.$emit('update:visible', false)
				this.$refs.form.resetFields()
			},

			handleClose() {
				this.$emit('update:visible', false)
				this.$refs.form.resetFields()
			},

			handleConfirm() {
				this.$refs.form.validate(valid => {
					if (valid) {
						this.loading = true

						if (this.isEdit) {
							const updateData = {
								qrCodeUrl: this.form.qrCodeUrl,
								description: this.form.description,
								isActive: this.form.isActive
							}
							qrcodeManager
								.updateQrcode(this.form.id, updateData)
								.then(() => {
									this.$message.success('更新成功')
									this.$emit('success')
									this.$emit('update:visible', false)
								})
								.catch(err => {
									this.$message.error(err)
								})
								.then(() => {
									this.loading = false
								})
						} else {
							qrcodeManager
								.createQrcode(this.form)
								.then(() => {
									this.$message.success('新增成功')
									this.$emit('success')
									this.$emit('update:visible', false)
								})
								.catch(err => {
									this.$message.error(err)
								})
								.then(() => {
									this.loading = false
								})
						}
					}
				})
			}
		}
	}
</script>

<style scoped>
	.qrcode-uploader {
		border: 1px dashed #d9d9d9;
		border-radius: 6px;
		cursor: pointer;
		position: relative;
		overflow: hidden;
		width: 150px;
		height: 150px;
	}

	.qrcode-uploader:hover {
		border-color: #409eff;
	}

	.qrcode-uploader-icon {
		width: 150px;
		height: 150px;
		font-size: 28px;
		color: #8c939d;
		line-height: 150px;
		text-align: center;
	}

	.qrcode-image {
		width: 150px;
		height: 150px;
		object-fit: cover;
	}

	.user-suggestion-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 5px 0;
	}

	.user-name {
		font-weight: 500;
		color: #303133;
	}

	.user-mobile {
		color: #909399;
		font-size: 13px;
		margin-left: 10px;
	}

	.user-type {
		color: #409eff;
		font-size: 12px;
		background: #ecf5ff;
		padding: 2px 6px;
		border-radius: 3px;
	}
</style>
