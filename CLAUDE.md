# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

- `npm run dev` - Start development server (opens at localhost:8080)
- `npm run dev:prod` - Start development server in production mode
- `npm run build` - Build for production
- `npm run build:dev` - Build for development mode
- `npm run lint` - Run ESLint to check code quality
- `npm run lint:fix` - Fix ESLint issues automatically
- `npm run format` - Format all source files with Prettier
- `npm run format:check` - Check if files are properly formatted

## Project Architecture

This is a Vue 2.7 admin dashboard for "千万仓" (agricultural trading platform) built with Element UI. It manages crop prices, companies, user orders, and system configuration.

### Tech Stack
- Vue 2.7 with Vue Router 3.x and Vuex 3.x
- Element UI 2.x for components
- Axios for HTTP requests
- SCSS for styling
- Node.js 20.19.4 required

### Core Architecture Patterns

**Route Structure**: Uses nested routing with a main Layout component containing sidebar and navbar. Routes are split into:
- `constantRoutes` - Public routes (login)
- `asyncRoutes` - Protected routes requiring authentication

**Authentication Flow**: 
- Route guards in `src/router/guards.js` check auth status on every navigation
- Auth state managed in `src/store/modules/auth.js` 
- HTTP interceptors handle token validation and logout on 401/403

**State Management**: Vuex store organized by modules:
- `auth` - User authentication and profile
- `layout` - UI state (sidebar collapse)  
- `crop` - Crop data mapping
- `area` - Regional data

**Manager Pattern**: Business logic abstracted into manager classes in `src/manager/`:
- Each domain (user, crop, company, etc.) has its own manager
- Managers handle API calls and data transformation
- Used by mixins to provide reusable functionality

**Mixin Usage**: Common functionality through mixins:
- `userMixin.js` - User-related operations
- `tableMixin.js` - Table/pagination logic
- `areaMixin.js` - Area/region functionality

### Key Configuration

**Environment Variables**:
- Development: API base URL set to `http://localhost:3000` 
- Production: Builds to `/admin/` path for deployment

**Webpack Configuration**:
- Development proxy: `/api` routes proxy to `VUE_APP_API_BASE_URL`
- Production: Code splitting with separate chunks for vendors and Element UI
- Source maps disabled in production

**HTTP Client (`src/utils/http.js`)**:
- Axios instance with 15s timeout
- Automatic parameter cleaning (removes empty values)  
- POST requests use form-encoded data via qs.stringify()
- Centralized error handling with Element UI notifications
- Auto-logout on authentication failure (code: -1)

### Component Structure

**Layout System**: 
- Main layout in `src/layout/index.vue` with sidebar + navbar + main content
- Responsive sidebar that collapses to 64px width
- Fixed sidebar with responsive main content margin

**Page Organization**:
- Feature-based folders: `/info`, `/price`, `/system`, `/user`, `/qrcode`
- Each feature may contain `components/` subfolder for reusable parts
- Common components in `src/components/` (Pagination, Screenfull)

## Code Standards and Best Practices

### Code Formatting (2024 Industry Standards)
- **No semicolons** - ESLint enforces `semi: false`
- **Single quotes** - Use single quotes for strings and JSX
- **Spaces for indentation** - 2 spaces (not tabs) for consistency with modern tooling
- **Trailing commas**: Never use trailing commas (`trailingComma: "none"`)
- **Print width**: 100 characters max for readability
- **Bracket spacing**: `{ key: value }` (spaces around object braces)
- **Arrow function parentheses**: Avoid when possible (`arrowParens: "avoid"`)
- **End of line**: LF for cross-platform compatibility
- **Vue formatting**: Script and style blocks not indented (`vueIndentScriptAndStyle: false`)
- **HTML attribute formatting**: Smart single-line vs multi-line based on length

### ESLint Configuration (Optimized for Vue 2.7 + ESLint 8.0)
- **Vue rules**: Uses `plugin:vue/recommended` for Vue.js 2.7 compatibility
- **Code quality focus**: ESLint handles logic and Vue patterns, Prettier handles formatting
- **Flexible prop handling**: Allows Vue 2 patterns like prop mutations and .sync
- **Layout rules disabled**: All formatting rules delegated to Prettier
- **Component naming**: Flexible single-word component names allowed

### Vue Component Standards
- **Component naming**: Use PascalCase for component names in both file names and template usage
- **Props**: Always define with proper types and defaults
- **Event emissions**: Use `$emit` with kebab-case event names
- **Template structure**: Max 3 attributes per line for single-line, 1 per line for multiline
- **HTML self-closing**: Allow self-closing tags for all elements (void, normal, component)
- **Template indentation**: Use 2 spaces for HTML template consistency

### JavaScript/ES6 Patterns
- **ES6 imports**: Always use ES6 import/export syntax
- **Object destructuring**: Use destructuring for cleaner code, especially in function parameters
- **Arrow functions**: Prefer arrow functions for callbacks and short functions
- **Template literals**: Use backticks for string interpolation
- **Async/await**: Prefer async/await over Promise chains

### Vue-Specific Patterns
- **Mixins**: Use for reusable functionality (tableMixin, userMixin, areaMixin)
- **Manager pattern**: Business logic goes in `/manager` files, not components
- **Store structure**: Organize by feature modules (auth, layout, crop, area)
- **Computed vs Methods**: Use computed for derived data, methods for actions
- **Event handling**: Emit events up, pass props down

### API Integration Standards
- **HTTP client**: Use the configured axios instance from `@/utils/http`
- **Data encoding**: All POST requests are form-encoded via qs.stringify()
- **Error handling**: Errors throw with message property, components catch and show via $message
- **Parameter cleaning**: Empty values are automatically filtered out
- **Response structure**: Expect `{ code, data, message }` format

### CSS/SCSS Conventions
- **Variables**: Define theme colors in `variables.scss` using SCSS variables
- **Scoped styles**: Use `scoped` attribute for component-specific styles
- **Class naming**: Use kebab-case for CSS class names
- **Element UI overrides**: Use `::v-deep` for deep selector modification
- **Responsive design**: Mobile-first approach with sidebar collapse functionality

### File Organization
- **Components**: Feature components in `/views/[feature]/components/`
- **Shared components**: Common components in `/src/components/`
- **Business logic**: API calls and business logic in `/src/manager/`
- **Utilities**: Helper functions in `/src/utils/`
- **Assets**: Images and static files in `/src/images/`

### Naming Conventions
- **Files**: kebab-case for all files except components (PascalCase)
- **Variables**: camelCase for JavaScript variables
- **Constants**: UPPER_SNAKE_CASE for constants
- **Functions**: camelCase with descriptive verbs (getUserInfo, handleSubmit)
- **CSS classes**: kebab-case with BEM methodology where appropriate

### Error Handling Patterns
- **User feedback**: Use Element UI Message/MessageBox for user notifications
- **Validation**: Client-side validation with Element UI form rules
- **API errors**: Centralized error handling in HTTP interceptor
- **Loading states**: Always show loading indicators for async operations
- **Confirmation dialogs**: Use $confirm for destructive actions

### Development Workflow
- **Before committing**: Always run `npm run lint:fix` and `npm run format`
- **Code formatting**: Prettier handles formatting, ESLint handles code quality
- **IDE integration**: Configure your editor to format on save using Prettier
- **Separation of concerns**: ESLint for code quality, Prettier for consistent formatting

### Security Best Practices
- **Input validation**: Validate all user inputs on both client and server
- **XSS prevention**: Use text interpolation, avoid v-html with user content  
- **Authentication**: JWT tokens managed automatically by HTTP interceptors
- **Route protection**: All routes except `/login` require authentication via guards

## Important Notes

- This codebase uses hash routing (not history mode)
- All API requests expect form-encoded data, not JSON
- Authentication tokens are handled automatically by HTTP interceptors
- Default route redirects to `/system/config`
- Development server opens automatically on `0.0.0.0:8080`