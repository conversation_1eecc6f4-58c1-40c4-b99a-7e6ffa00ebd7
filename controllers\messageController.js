//  农舟LOG模块
const dbController = require('./dbController');
const { resFilter } = require('../utils/filter');
const { MESSAGE, USER, ORDER } = require('../db').tableNames;

// 新增
function addMessage (reqBody) {
    const { userId, toUserId, orderId, content } = reqBody
    return new Promise(function (resolve, reject) {
        dbController.add(MESSAGE, { userId, toUserId, orderId, content }, (err, rows) => {
            if (err) {
                reject(err);
            } else {
                resolve('消息发送成功');
            }
        })
    });
}


// 查询消息列表
function queryMessages (userId, otherUserId, orderId, pageIndex, pageSize) {
    return new Promise(function (resolve, reject) {
        const params = { orderId };
        const queryMyAndAnother = userId && otherUserId; // 是否查询自己和他人的通讯记录
        if (queryMyAndAnother) { // 查询两个人之间的通讯记录
            params._special = '(`user_id`=' + userId + ' AND `to_user_id`=' + otherUserId + ') OR (`user_id`=' + otherUserId + ' AND `to_user_id`=' + userId + ')'
        } else {
            params._special = '`user_id`=' + userId + ' OR `to_user_id`=' + userId
        }
        countMessage(params).then(total => {
            dbController.query(MESSAGE, params, { pageIndex, pageSize, orderBy: 'id', orderSort: 'DESC' }, (err, rows) => {
                if (err) {
                    reject(err)
                } else {
                    const orderIdArr = [];
                    const userIdArr = [];
                    rows.forEach(item => {
                        // if (queryMyAndAnother && item.order_id) {
                        //     orderIdArr.push(item.order_id)
                        // }
                        if (item.order_id) {
                            orderIdArr.push(item.order_id)
                        }
                        userIdArr.push(item.user_id, item.to_user_id);
                    })
                    const task1 = queryOrderMap(orderIdArr);
                    const task2 = queryUserMap(userId, userIdArr);
                    Promise.all([task1, task2]).then(resArr => {
                        resolve({
                            total,
                            orders: resArr[0],
                            users: resArr[1],
                            list: rows.map(item => resFilter(item))
                        })
                    }).catch(err => {
                        reject(err)
                    })
                }
            })
        }).catch(err => {
            reject(err)
        })
    })
}

// 设置消息成已读状态
function setMessageReadStatus (idArr) {
    dbController.update(MESSAGE, { id: idArr }, { status: 1 })
}

function queryUserMap (userId, userIdArr) {
    return new Promise(function (resolve, reject) {
        const arr = [];
        userIdArr.forEach(id => { // 只查询别人
            if (arr.indexOf(id) === -1 && id != userId) {
                arr.push(id)
            }
        })
        dbController.query(USER, { id: arr }, null, (err, rows) => {
            if (err) {
                reject(err)
            } else {
                resolve(rows.map(user => {
                    return {
                        id: user.id,
                        name: user.name,
                        avatar: user.avatar
                    }
                }))
            }
        })
    })
}
function queryOrderMap (orderIdArr) {
    return new Promise(function (resolve, reject) {
        if (orderIdArr.length > 0) {
            const arr = [];
            orderIdArr.forEach(id => {
                if (arr.indexOf(id) === -1) {
                    arr.push(id)
                }
            })
            dbController.query(ORDER, { id: arr }, null, (err, rows) => {
                if (err) {
                    reject(err)
                } else {
                    resolve(rows.map(order => resFilter(order)))
                }
            })
        } else {
            resolve([])
        }
    })
}

function countMessage (params) {
    return new Promise(function (resolve, reject) {
        dbController.count(MESSAGE, params, (err, rows) => {
            if (err) {
                reject(err)
            } else {
                resolve(rows[0]['count(*)']);
            }
        })
    })
}

module.exports = {
    addMessage,
    queryMessages,
    setMessageReadStatus
}