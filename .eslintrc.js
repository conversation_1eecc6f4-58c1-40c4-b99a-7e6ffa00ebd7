module.exports = {
  root: true,
  env: {
    node: true,
    browser: true,
    es2020: true
  },
  extends: [
    'eslint:recommended',
    'plugin:vue/recommended', // Compatible with eslint-plugin-vue ^9.33.0
    'prettier' // Must be last to override conflicting rules
  ],
  parserOptions: {
    parser: '@babel/eslint-parser',
    ecmaVersion: 2020,
    sourceType: 'module',
    requireConfigFile: false
  },
  rules: {
    // ESLint core rules
    'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'no-unused-vars': ['warn', { argsIgnorePattern: '^_' }],
    'no-empty': 'warn',
    'no-constant-condition': 'warn',

    // Vue.js 2.7 specific rules - optimized for project patterns
    'vue/multi-word-component-names': 'off', // Allow single-word component names
    'vue/no-mutating-props': 'off', // Allow .sync pattern common in Vue 2
    'vue/no-v-html': 'off', // Allow v-html (ensure content is sanitized)
    'vue/no-template-shadow': 'off', // Allow template variable shadowing
    'vue/require-default-prop': 'off', // Optional prop defaults
    'vue/require-prop-types': 'off', // Optional prop type definitions
    'vue/attribute-hyphenation': 'off', // Allow both camelCase and kebab-case
    'vue/component-name-in-template-casing': 'off', // Flexible component casing
    'vue/name-property-casing': 'off', // Flexible name property casing
    
    // Layout and formatting rules (handled by Prettier)
    'vue/html-indent': 'off',
    'vue/max-attributes-per-line': 'off',
    'vue/html-self-closing': 'off',
    'vue/singleline-html-element-content-newline': 'off',
    'vue/multiline-html-element-content-newline': 'off',
    'vue/first-attribute-linebreak': 'off',
    
    // Allow common patterns in existing codebase
    'vue/no-unused-components': 'warn',
    'vue/no-unused-vars': 'warn',
    'vue/valid-template-root': 'error',
    'vue/no-parsing-error': 'error'
  },
  overrides: [
    {
      files: ['**/__tests__/*.{j,t}s?(x)', '**/tests/unit/**/*.spec.{j,t}s?(x)'],
      env: {
        jest: true
      }
    }
  ]
}
