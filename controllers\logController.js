//  农舟LOG模块
const dbController = require('./dbController');
const { resFilter } = require('../utils/filter');
const { LOG, INVITE_RECORD } = require('../db').tableNames;
const dateUtil = require('../utils/date')

// 新增log
function addLog (reqBody) {
    const { key, userId, detail } = reqBody
    return new Promise(function (resolve, reject) {
        dbController.add(LOG, { key, userId, detail }, (err, rows) => {
            if (err) {
                reject(err);
            } else {
                resolve('log添加成功');
            }
        })
    });
}

// 新增行为记录
function addActionRecord (reqBody) {
    const { userId, shareId, action } = reqBody
    return new Promise(function (resolve, reject) {
        dbController.add(INVITE_RECORD, { userId, shareId, action }, (err, rows) => {
            if (err) {
                reject(err);
            } else {
                resolve('记录添加成功');
            }
        })
    });
}

// 查询log
function queryLogs (reqBody) {
    const { key, userId, pageIndex, pageSize } = reqBody;
    return new Promise(function (resolve, reject) {
        dbController.count(LOG, { key, userId }, (err, rows) => {
            if (err) {
                reject(err);
            } else {
                const total = rows[0]['count(*)'];
                dbController.query(LOG, { key, userId }, { pageIndex, pageSize }, (err, rows) => {
                    if (err) {
                        reject(err);
                    } else {
                        resolve({
                            total,
                            list: rows.map(item => resFilter(item))
                        })
                    }
                })
            }
        })
    })
}

function countLogs (key, userId) {
    return new Promise(function (resolve, reject) {
        dbController.count(LOG, { key, userId }, (err, rows) => {
            if (err) {
                reject(err)
            } else {
                resolve(rows[0]['count(*)']);
            }
        })
    })
}

// 运营人员查看个人分享的统计总量
function countRecords (reqBody) {
    const { userId, startTime, endTime } = reqBody
    return new Promise(function (resolve, reject) {
        const queryParams = {
            shareId: userId,
            'userId.not': userId
        }
        if (startTime && endTime) {//设定了查询时间范围
            queryParams._special = `create_time between "${startTime}" and "${endTime}"`
        }
        dbController.count(INVITE_RECORD, queryParams, (err, rows) => {
            if (err) {
                reject(err);
            } else {
                const total = rows[0]['count(*)'];
                resolve(total)
            }
        })
    })
}

// 管理员查询查询Record
function queryRecords (reqBody) {
    const { action, shareId, startTime, endTime, pageIndex, pageSize } = reqBody
    return new Promise(function (resolve, reject) {
        const queryParams = { action, shareId }
        if (startTime && endTime) {//设定了查询时间范围
            queryParams._special = `create_time between "${startTime}" and "${endTime}"`
        }
        dbController.count(INVITE_RECORD, queryParams, (err, rows) => {
            if (err) {
                reject(err);
            } else {
                const total = rows[0]['count(*)'];
                dbController.query(INVITE_RECORD, queryParams, { pageIndex, pageSize, orderBy: 'id' }, (err, rows) => {
                    if (err) {
                        reject(err);
                    } else {
                        resolve({
                            total,
                            list: rows.map(item => resFilter(item))
                        })
                    }
                })
            }
        })
    })
}


module.exports = {
    addLog,
    queryLogs,
    countLogs,
    addActionRecord,
    queryRecords,
    countRecords,
}