/**
 * 简化的HTTP客户端 - 自动重试和错误处理
 *
 * 设计理念：
 * 1. 开箱即用 - 无需复杂配置
 * 2. 自动重试 - 智能处理网络问题
 * 3. 友好错误 - 清晰的错误信息
 * 4. 防止封禁 - 自动请求头和延迟
 */

const axios = require('axios')

class HttpClient {
	constructor() {
		// 创建axios实例，统一配置
		this.client = axios.create({
			timeout: 30000, // 30秒超时，避免长时间等待
			headers: {
				// 模拟真实浏览器，避免被反爬虫识别
				'User-Agent':
					'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
				Accept: 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
				'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
				'Accept-Encoding': 'gzip, deflate, br',
				Connection: 'keep-alive',
				'Upgrade-Insecure-Requests': '1'
			}
		})

		// 重试配置 - 简化但实用
		this.retryConfig = {
			maxRetries: 3, // 最多重试3次
			baseDelay: 1000, // 基础延迟1秒
			maxDelay: 10000, // 最大延迟10秒
			backoffFactor: 2 // 指数退避因子
		}

		// 请求统计 - 用于监控和调试
		this.stats = {
			totalRequests: 0,
			successRequests: 0,
			failedRequests: 0,
			retriedRequests: 0
		}

		console.log('🌐 HTTP客户端已初始化')
	}

	/**
	 * 主要的GET请求方法
	 * @param {string} url - 请求URL
	 * @param {Object} options - 额外选项
	 * @returns {Promise<string>} HTML内容
	 */
	async get(url, options = {}) {
		console.log(`🔗 正在请求: ${url}`)
		this.updateStats('totalRequests')

		try {
			const response = await this.requestWithRetry('GET', url, null, options)
			this.updateStats('successRequests')
			console.log(`✅ 请求成功: ${url}`)
			return response.data
		} catch (error) {
			this.updateStats('failedRequests')
			console.error(`❌ 请求失败: ${url} - ${error.message}`)
			throw new Error(`网络请求失败: ${this.simplifyErrorMessage(error)}`)
		}
	}

	/**
	 * 带重试的请求方法
	 * 自动处理临时网络问题和服务器错误
	 */
	async requestWithRetry(method, url, data = null, options = {}) {
		let lastError = null

		// 执行重试循环
		for (let attempt = 0; attempt <= this.retryConfig.maxRetries; attempt++) {
			try {
				// 第一次尝试不延迟，后续尝试有延迟
				if (attempt > 0) {
					const delay = this.calculateRetryDelay(attempt)
					console.log(`⏳ 第${attempt}次重试，等待 ${delay}ms...`)
					await this.sleep(delay)
					this.updateStats('retriedRequests')
				}

				// 发起请求
				const response = await this.client.request({
					method,
					url,
					data,
					...options
				})

				// 检查响应状态
				this.validateResponse(response, url)

				return response
			} catch (error) {
				lastError = error

				// 判断是否需要重试
				if (!this.shouldRetry(error, attempt)) {
					break
				}

				console.warn(`⚠️ 请求失败，准备重试: ${error.message}`)
			}
		}

		// 所有重试都失败了，抛出最后一个错误
		throw lastError
	}

	/**
	 * 计算重试延迟时间
	 * 使用指数退避策略，避免对服务器造成压力
	 */
	calculateRetryDelay(attempt) {
		// 限制最大指数，防止过长延迟
		const limitedAttempt = Math.min(attempt, 5)
		
		// 指数退避: baseDelay * (backoffFactor ^ attempt)
		const delay = this.retryConfig.baseDelay * Math.pow(this.retryConfig.backoffFactor, limitedAttempt)

		// 减少抖动范围，避免过长延迟
		const jitter = Math.random() * Math.min(500, delay * 0.1)

		// 限制最大延迟时间
		return Math.min(delay + jitter, this.retryConfig.maxDelay)
	}

	/**
	 * 判断是否应该重试
	 * 只对临时性错误进行重试
	 */
	shouldRetry(error, attempt) {
		// 如果已经达到最大重试次数，不再重试
		if (attempt >= this.retryConfig.maxRetries) {
			return false
		}

		// 网络错误通常是临时的，应该重试
		if (
			error.code === 'ECONNRESET' ||
			error.code === 'ECONNREFUSED' ||
			error.code === 'ETIMEDOUT' ||
			error.code === 'ENOTFOUND'
		) {
			return true
		}

		// HTTP状态码错误
		if (error.response) {
			const status = error.response.status

			// 5xx服务器错误通常是临时的
			if (status >= 500 && status < 600) {
				return true
			}

			// 429 Too Many Requests 应该重试
			if (status === 429) {
				return true
			}

			// 408 Request Timeout 应该重试
			if (status === 408) {
				return true
			}

			// 4xx客户端错误通常不应重试（除了上面的特殊情况）
			if (status >= 400 && status < 500) {
				return false
			}
		}

		// 其他未知错误，保险起见不重试
		return false
	}

	/**
	 * 验证响应内容
	 * 确保获取到有效的HTML内容
	 */
	validateResponse(response, url) {
		// 检查状态码
		if (response.status < 200 || response.status >= 300) {
			throw new Error(`HTTP状态码错误: ${response.status}`)
		}

		// 检查内容类型（宽松检查，因为有些网站配置不标准）
		const contentType = response.headers['content-type'] || ''
		if (
			!contentType.includes('text/html') &&
			!contentType.includes('text/') &&
			!contentType.includes('application/')
		) {
			console.warn(`⚠️ 可能的内容类型问题: ${contentType} for ${url}`)
		}

		// 检查内容长度
		const content = response.data || ''
		if (content.length < 100) {
			console.warn(`⚠️ 响应内容过短 (${content.length} 字符)，可能是空页面: ${url}`)
		}

		// 检查是否包含常见的HTML标签
		if (typeof content === 'string' && !content.includes('<') && !content.includes('>')) {
			console.warn(`⚠️ 响应内容不像HTML: ${url}`)
		}
	}

	/**
	 * 简化错误信息，让开发者更容易理解
	 */
	simplifyErrorMessage(error) {
		// 网络连接问题
		if (error.code === 'ECONNREFUSED') {
			return '连接被拒绝，服务器可能没有响应'
		}

		if (error.code === 'ETIMEDOUT') {
			return '请求超时，网络可能有问题'
		}

		if (error.code === 'ENOTFOUND') {
			return '找不到服务器，请检查URL是否正确'
		}

		// HTTP状态错误
		if (error.response) {
			const status = error.response.status

			switch (status) {
				case 403:
					return '访问被禁止，可能需要登录或被反爬虫拦截'
				case 404:
					return '页面不存在'
				case 429:
					return '请求过于频繁，被服务器限制'
				case 500:
					return '服务器内部错误'
				case 502:
					return '网关错误，服务器可能暂时不可用'
				case 503:
					return '服务暂时不可用'
				default:
					return `HTTP错误 ${status}`
			}
		}

		// 返回原始错误信息
		return error.message || '未知网络错误'
	}

	/**
	 * 获取请求统计信息
	 * 用于监控和调试
	 */
	getStats() {
		const successRate =
			this.stats.totalRequests > 0
				? ((this.stats.successRequests / this.stats.totalRequests) * 100).toFixed(1)
				: '0.0'

		return {
			...this.stats,
			successRate: `${successRate}%`
		}
	}

	/**
	 * 重置统计信息
	 */
	resetStats() {
		this.stats = {
			totalRequests: 0,
			successRequests: 0,
			failedRequests: 0,
			retriedRequests: 0
		}
	}

	/**
	 * 睡眠函数 - 用于延迟
	 */
	sleep(ms) {
		return new Promise(resolve => setTimeout(resolve, ms))
	}

	/**
	 * 安全更新统计信息，防止溢出
	 */
	updateStats(type) {
		this.stats[type]++
		
		// 防止数值溢出
		if (this.stats[type] > Number.MAX_SAFE_INTEGER - 1000) {
			this.resetStats()
		}
	}

	/**
	 * 更新请求头 - 可用于模拟不同的浏览器
	 */
	updateHeaders(headers) {
		Object.assign(this.client.defaults.headers, headers)
		console.log('🔧 HTTP请求头已更新')
	}

	/**
	 * 设置代理 - 用于需要代理访问的场景
	 */
	setProxy(proxyConfig) {
		this.client.defaults.proxy = proxyConfig
		console.log('🔧 代理设置已更新')
	}
}

module.exports = HttpClient
