// 农舟服务号模块组件
const dbController  = require('./dbController');
const wxCommonApi = require('../utils/wxCommonApi');
const { resFilter } = require('../utils/filter');
const { NZ_SERVICE, NZ_BUYER_SERVICE } = require('../constant/appName');
const { NZ_USERS, NZ_BUYERS } = require('../constant/tableName');
const { ORDER_NOT_ACCEPT, ORDER_IS_ACCEPT } = require('../constant/messageTemplate');


// 获取微信注册页面js签名
function getJsSign(url, noncestr, timestamp, appName){
    return wxCommonApi.requestJsSign(appName, url, noncestr, timestamp)
}

// 更新用户信息或商贩信息，需要做检查，如果发现没有变化就不做更新，这里过滤掉空值
function updateUser(user, wxUser, userTable){
    const { subscribe, openid, unionid, sex, headimgurl, nickname } = wxUser;
    let updateData = {
        wx_name: nickname,
        gender: sex,
        service_wx_open_id: openid,
        wx_union_id: unionid,
        service_subscribe: subscribe,
        wx_avatar: headimgurl
    }
    // 清理掉没有用的值
    let needDoUpdate = false;
    for (const key in updateData) {
        if(updateData[key] === undefined || updateData[key] === null){
            delete updateData[key];
        }else{
            if(updateData[key] === user[key]){// 该字段跟数据库中一致
                delete updateData[key];
            }else{
                needDoUpdate = true; // 有不一样的值，对数据库更新
            }
        }
    }

    if(needDoUpdate){
        return new Promise((resolve, reject) => {
            dbController.update(userTable, { id: user.id }, updateData, (err, rows) => {
                if(err){
                    reject(err)
                }else{
                    resolve(updateData)
                }
            })
        })
    }else{
        return Promise.resolve(null);
    }
}

// 添加新用户或新商贩
function addUser(wxUser, userTable){
    const { subscribe, openid, unionid, sex, headimgurl, nickname } = wxUser;
    let updateData = {
        wx_name: nickname,
        gender: sex,
        service_wx_open_id: openid,
        wx_union_id: unionid,
        service_subscribe: subscribe,
        wx_avatar: headimgurl
    }
    return new Promise((resolve, reject) => {
        dbController.add(userTable, updateData, (err, res) => {
            if(err){
                reject(err);
            }else{
                const id = res.insertId;
                dbController.query(userTable, { id }, null, (err, rows) => {
                    if(err){
                        reject(err);
                    }else{
                        resolve(rows[0]);
                    }
                })
            }
        })
    })
}

function wxLoginByCode(code, appName){
    let userTable;
    if(appName === NZ_SERVICE){
        userTable = NZ_USERS;
    }
    if(appName === NZ_BUYER_SERVICE){
        userTable = NZ_BUYERS;
    }
    return new Promise(function(resolve, reject){
        // https://developers.weixin.qq.com/doc/offiaccount/User_Management/Get_users_basic_information_UnionID.html#UinonId
        wxCommonApi.requestServiceUser(appName, code).then(wxUser => {
            const { openid, unionid } = wxUser;
            let _special = '`service_wx_open_id`="' + openid + '"';
            if(unionid){ // 包含 unionid 尝试匹配
                _special += ' OR `wx_union_id`="' + unionid + '"';
            }
            dbController.query(userTable, { _special }, null, (err, rows) => {
                if(err){
                    reject(err)
                }else if(rows.length > 0){ // 找到现有的用户，尝试更新信息
                    updateUser(rows[0], wxUser, userTable).then(updateData => {
                        // 将更新的数据跟拿到的user进行合并并返回
                        let updatedUser = resFilter(Object.assign(rows[0], updateData));
                        updatedUser.extWxUser = wxUser;
                        resolve(updatedUser);
                    }).catch(err => {
                        reject(err);
                    })
                }else{
                    addUser(wxUser, userTable).then(data => {
                        let user = resFilter(data);
                        user.extWxUser = wxUser;
                        resolve(user);
                    })
                }
            })
        }).catch(err => {
            reject(err);
        })
    })
}

// 获取微信用户
function requestWxUser(code, appName){
    return wxCommonApi.requestServiceUser(appName, code);
}

function sendWxInfoToUser(userId, type, data, messageUrl, miniProgram){
    return new Promise((resolve, reject) => {
        dbController.query(NZ_USERS, { id: userId }, null, (err, rows) => {
            if(err){
                reject(err);
            }else{
                const user = rows[0];
                if(user){
                    const { service_wx_open_id } = user;
                    let templateId;
                    if(type === 'ACCEPT'){
                        templateId = ORDER_IS_ACCEPT;
                    }else if(type === 'REJECT'){
                        templateId = ORDER_NOT_ACCEPT;
                    }
                    wxCommonApi.sendSubscribeMessage(NZ_SERVICE, service_wx_open_id, templateId, data, messageUrl, miniProgram).then(data => {
                        resolve(data);
                    }).catch(err => {
                        reject(err);
                    })
                }else{
                    reject('用户不存在')
                }
            }
        })
    })
}

function sendMessageTest(userId){
    return new Promise((resolve, reject) => {
        dbController.query(NZ_USERS, { id: userId }, null, (err, rows) => {
            if(err){
                reject(err);
            }else{
                const user = rows[0];
                if(user){
                    const { mini_wx_open_id } = user;
                    wxCommonApi.sendMiniOrderChangeMessage(mini_wx_open_id, 1,
                        '囤玉米30囤，要价1.35元/斤', '竞价失败', '收到多个拒绝，请降价后重新竞价',
                        'pages/my/record'
                        ).then(data => {
                        resolve(data);
                    }).catch(err => {
                        reject(err)
                    })
                }else{
                    reject('用户不存在')
                }
            }
        })
    })
}

module.exports = {
    getJsSign,
    wxLoginByCode,
    requestWxUser,
    sendWxInfoToUser,
    sendMessageTest
}
