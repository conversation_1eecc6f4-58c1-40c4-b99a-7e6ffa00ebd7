const { INFO_SOURCE } = require('../../../constant/enum')

/**
 * 爬虫数据源统一配置文件
 *
 * 配置说明：
 * - 所有数据源必须包含基础字段：id, name, type, baseUrl, selectors
 * - 根据不同爬虫类型(type)，使用对应的扩展字段
 * - 选择器支持多个备用选项，用逗号分隔，按优先级从左到右
 * - 所有字段都有详细的中文注释说明用途
 *
 * 当前支持的12个数据源：
 * 1. 中国农业农村部 - 供需形势分析
 * 2. 国家粮食交易中心 - API接口数据
 * 3. 中国大豆产业协会 - 大豆相关信息
 * 4. 中国棉花协会 - 棉花行业信息
 * 5. 山东省粮食和物资储备局 - 收购信息
 * 6. 河北省粮食和物资储备局 - 市场分析与通知公告
 * 7. 吉林省粮食和物资储备局 - 收购信息
 * 8. 新疆维吾尔自治区粮食和物资储备局 - 收购信息
 * 9. 内蒙古自治区粮食和物资储备局 - 多品种农产品信息
 * 11. 中国饲料行业信息网 - 行业分析
 * 12. 中国农作物行情网 - 指数分析
 */

// =============================================================================
// 配置模板说明 - 新增数据源时请参考此模板
// =============================================================================
const CONFIG_TEMPLATE = {
	// 【必填】数据源唯一标识，从 INFO_SOURCE 枚举中获取
	id: 'INFO_SOURCE.YourSource.id',

	// 【必填】数据源显示名称，用于日志和管理界面显示
	name: '数据源中文名称',

	// 【必填】爬虫类型，决定使用哪个策略类，可选值：'html' | 'api' | 'province' | 'complex'
	type: 'html',

	// 【必填】数据源基础URL，不包含具体页面路径
	baseUrl: 'https://example.com',

	// 【必填】页面元素选择器配置，支持多个备用选择器
	selectors: {
		// 列表容器选择器 - 包含所有文章项的容器元素
		listContainer: '.list-container, .news-list, ul.items',

		// 标题选择器 - 文章标题元素
		titleSelector: 'h3 a, .title, h2',

		// 链接选择器 - 文章详情页链接
		linkSelector: 'h3 a, .title a, a[href]',

		// 日期选择器 - 文章发布日期
		dateSelector: '.date, .time, span.publish-time',

		// 内容选择器 - 文章正文内容（详情页使用）
		contentSelector: '.content, .article-body, .main-content',

		// 摘要选择器 - 文章摘要或简介（可选）
		summarySelector: '.summary, .excerpt, .intro',

		// 作者选择器 - 文章作者（可选）
		authorSelector: '.author, .writer, .by'
	},

	// 【可选】统一分页配置 - 数据源级别的分页策略（推荐使用）
	pagination: {
		type: 'template', // 分页类型：'template' | 'numeric' | 'query' | 'path'
		pattern: 'index{_page}.html', // 模板分页：index.html, index_1.html
		pageSize: 20, // 数字分页：每页条数
		pageParam: 'page', // 查询参数分页：参数名
		pathPattern: '/page/{page}' // 路径分页：路径模式
	},

	// 【可选】分类配置 - 多分类数据源使用
	categories: [
		{ url: 'category1/', name: '分类名称1' },
		{ url: 'category2/', name: '分类名称2' }
	],

	// 【可选】API配置 - API类型数据源使用
	apiConfig: {
		list: { method: 'getList', pageSize: 10 },
		detail: { method: 'getDetail' }
	},

	// 【可选】URL模式 - 分页或多页面爬取使用
	urlPattern: 'index_{page}.html',

	// 【可选】过滤关键词 - 只爬取包含特定关键词的内容
	filterKeyword: '关键词',

	// 【可选】农作物关键词映射 - 自动识别农作物类型
	cropKeywords: ['玉米', '小麦', '大豆'],

	// 【可选】默认农作物ID - 无法识别时使用
	defaultCropId: '1',

	// 【可选】请求配置 - 特殊请求参数
	requestConfig: {
		headers: { 'User-Agent': 'custom-agent' },
		timeout: 10000,
		encoding: 'utf8'
	}
}

// =============================================================================
// 生产环境数据源配置
// =============================================================================
module.exports = {
	// 中国农业农村部 - 供需形势分析
	[INFO_SOURCE.ChineseAgriculturalSupplyAndDemandEstimates.id]: {
		id: INFO_SOURCE.ChineseAgriculturalSupplyAndDemandEstimates.id,
		name: '中国农业农村部',
		type: 'html', // HTML网页爬取类型
		baseUrl: 'http://www.agri.cn/sj',

		// 多分类配置 - 支持多个栏目同时爬取
		categories: [
			{ type: 'gxxs', name: '供需形势分析' },
			{ type: 'jcyj', name: '基础研究' }
		],

		// 分页配置 - 支持多页面爬取
		pages: ['index.htm', 'index_1.htm', 'index_2.htm'],

		selectors: {
			// 文章列表容器 - 包含所有新闻条目的区域
			listContainer: '.list_li_con',

			// 文章标题 - 新闻标题文本
			titleSelector: '.con_tit',

			// 详情链接 - 跳转到文章详情页的链接
			linkSelector: '.con_tit a',

			// 文章摘要 - 新闻简介或预览文本
			summarySelector: '.con_text',

			// 发布日期 - 新闻发布时间
			dateSelector: '.con_date_span',

			// 正文内容 - 文章详情页的主要内容
			contentSelector: '.TRS_Editor',

			// 文章作者 - 发布者或作者信息
			authorSelector: '.updateInfo_mess .mess_text'
		},

		// 农作物关键词 - 用于自动分类农作物相关新闻
		cropKeywords: ['小麦', '玉米', '大豆', '水稻', '棉花'],
		defaultCropId: '1'
	},

	// 国家粮食交易中心 - API接口类型
	[INFO_SOURCE.NationalGrainTradeCenter.id]: {
		id: INFO_SOURCE.NationalGrainTradeCenter.id,
		name: '国家粮食交易中心',
		type: 'api', // API接口爬取类型
		baseUrl: 'http://www.grainmarket.com.cn',

		// API接口配置 - 使用JSON API获取数据
		apiUrl: 'http://www.grainmarket.com.cn/centerweb/getData',
		apiConfig: {
			// 列表接口配置
			list: {
				method: 'tradeCenterOtherNewsList', // API方法名
				pageSize: 15, // 每页数据条数
				articleTypeID: '10' // 文章类型ID
			},
			// 详情接口配置
			detail: {
				method: 'tradeCenterNewsDetail' // 获取详情的API方法
			}
		},

		cropKeywords: ['粮食', '小麦', '玉米', '稻谷', '交易'],
		defaultCropId: '1'
	},

	// 中国大豆产业协会 - 多分类HTML爬取
	[INFO_SOURCE.ChinaSoybean.id]: {
		id: INFO_SOURCE.ChinaSoybean.id,
		name: '中国大豆产业协会',
		type: 'html',
		baseUrl: 'http://www.chinasoybean.org.cn',

		// 多分类页面配置 - 不同栏目使用不同URL路径
		categories: [
			{ url: 'news.php?cid=24', name: '国内资讯' },
			{ url: 'cyxx.php?cid=26', name: '产业信息' },
			{ url: 'cyxx.php?cid=27', name: '国际资讯' }
		],

		selectors: {
			listContainer: '.new_r > ul > li',
			titleSelector: 'h1, .title', // 多个备用选择器，优先使用h1
			linkSelector: 'a[href]',
			dateSelector: 'span.date, .time',
			contentSelector: '#textarea, .content',
			authorSelector: '#textarea p:last, .author'
		},

		// 专门针对大豆相关的关键词
		cropKeywords: ['大豆', '豆粕', '豆油', '进口大豆', '国产大豆'],
		defaultCropId: '3' // 大豆作物ID
	},

	// 中国棉花协会 - 复杂多分类配置
	[INFO_SOURCE.ChinaCotton.id]: {
		id: INFO_SOURCE.ChinaCotton.id,
		name: '中国棉花协会',
		type: 'complex', // 复杂类型，需要特殊处理策略
		baseUrl: 'https://www.china-cotton.org',

		// 统一分页配置 - 使用数字分页：/category/20/1, /category/20/2
		pagination: {
			type: 'numeric', // 分页类型：template, numeric, query, path
			pattern: null, // 不适用于数字分页
			pageSize: 20 // 每页20条记录
		},

		// 多个不同类型的分类配置
		categories: [
			// 国内棉花相关栏目
			{ url: 'advise/china_cotton_list', name: '国内棉花', category: '国内棉花' },
			{ url: 'warning/newContent', name: '预警信息', category: '国内棉花' },
			{ url: 'data/priceContent', name: '价格数据', category: '国内棉花' },

			// 纺织信息相关栏目
			{ url: 'advise/textile_cotton_list', name: '纺织资讯', category: '纺织信息' },
			{ url: 'data/cottonDataList', name: '纺织数据', category: '纺织信息' },

			// 国际棉花相关栏目
			{ url: 'advise/inter_cotton_list', name: '国际资讯', category: '国际棉花' },
			{ url: 'data/internationalData', name: '国际数据', category: '国际棉花' }
		],

		selectors: {
			// 使用多个备用选择器，提高容错性
			listContainer: '.container > .u7-l-2 > .special > .special-li, .news-list li',
			titleSelector: 'h3 a, h4 a, .title a, a.news-title',
			linkSelector: 'a[href*="detail"], a[href*="show"], .title a',
			dateSelector: 'span.date, .time, .publish-date',
			contentSelector: '.txt, .article-content, .main-content',
			authorSelector: '.article-time > span:first, .author-info'
		},

		cropKeywords: ['棉花', '皮棉', '籽棉', '棉纺', '棉价'],
		defaultCropId: '4' // 棉花作物ID
	},

	// 中国饲料行业信息网 - 省级标准配置模板
	[INFO_SOURCE.ChinaFeedAnalysis.id]: {
		id: INFO_SOURCE.ChinaFeedAnalysis.id,
		name: '中国饲料行业信息网',
		type: 'province', // 省级机构类型，使用统一的处理策略
		baseUrl: 'https://www.chinafeed.org.cn/hyfx/hyfx_erji/',
		category: '行业分析', // 数据分类标签

		// 统一分页配置 - 模板分页
		pagination: {
			type: 'template', // 分页类型：template, numeric, query, path
			pattern: 'index{_page}.htm', // 分页模式：index.htm, index_1.htm
			pageSize: null // 不适用于模板分页
		},

		// 单一栏目配置
		categories: [
			{
				url: '', // 空URL表示使用baseUrl
				name: '行业分析',
				category: '行业分析'
			}
		],

		selectors: {
			// 使用多个通用选择器，适应不同网站结构
			listContainer: '.content-list li, .list-item, article, .news-item',
			titleSelector: 'h3 a, .title a, h2 a, h4 a, .news-title',
			linkSelector: 'h3 a, .title a, h2 a, h4 a, a[href]',
			dateSelector: '.date, .time, .publish-time, .update-time',
			contentSelector: '.content, .article-content, .main-content, .text-content',
			summarySelector: '.summary, .intro, .excerpt'
		},

		cropKeywords: ['玉米', '饲料', '养殖', '饲料原料', '豆粕', '鱼粉'],
		defaultCropId: '1', // 默认归类为玉米相关

		// 内容过滤 - 只获取包含特定关键词的内容
		contentFilter: ['行业分析', '市场分析', '价格分析']
	},

	// 山东省粮食和物资储备局 - 省级机构标准配置
	[INFO_SOURCE.SDLscb.id]: {
		id: INFO_SOURCE.SDLscb.id,
		name: '山东省粮食和物资储备局',
		type: 'province',
		baseUrl: 'http://lscb.shandong.gov.cn',
		category: '山东粮食收购信息',

		// 带参数的URL模式 - 支持分页查询
		urlPattern: 'col/col15411/index.html?pageNum={page}',

		selectors: {
			listContainer: '#container record, .list-container tr',
			titleSelector: 'a[title], a',
			linkSelector: 'a[href]',
			dateSelector: 'td:last-child, .date',
			contentSelector: 'tbody>tr:nth-child(2)>td>table>tbody>tr:nth-child(2) p, .main-content'
		},

		// 特定关键词过滤 - 只获取收购相关信息
		filterKeyword: '收购进度',
		cropKeywords: ['玉米', '小麦', '收购', '粮食'],
		defaultCropId: '1'
	},

	// 河北省粮食和物资储备局 - 多栏目统一配置
	[INFO_SOURCE.HBLswz.id]: {
		id: INFO_SOURCE.HBLswz.id,
		name: '河北省粮食和物资储备局',
		type: 'complex', // 复杂类型，需要特殊处理策略
		baseUrl: 'https://lswz.hebei.gov.cn',

		// 统一分页配置 - 所有栏目使用相同的分页逻辑
		pagination: {
			type: 'template', // 分页类型：template, numeric, query, path
			pattern: 'index{_page}.html', // 分页模式：index.html, index_1.html
			pageSize: null // 不适用于模板分页
		},

		// 多栏目分类配置
		categories: [
			{
				url: 'lysc/hqsp/',
				name: '粮油市场月度分析报告',
				category: '市场分析'
			},
			{
				url: 'jlzw/tzgg/',
				name: '通知公告',
				category: '政务信息'
			}
		],

		// 统一选择器配置 - 适用于所有栏目
		selectors: {
			listContainer: '.border_tabejbig .rgtbar_erji .list_rgterji li, .news-list li',
			titleSelector: 'a[title], .news-title, .notice-title',
			linkSelector: 'a[href]',
			dateSelector: 'span.date, .publish-time',
			contentSelector: '.contentmain .TRS_Editor, .article-body',
			authorSelector: '.author, .source'
		},

		cropKeywords: ['粮油', '市场分析', '小麦', '玉米', '通知', '公告', '粮食', '政策'],
		defaultCropId: '1'
	},

	// 吉林省粮食和物资储备局 - 收购信息专栏
	[INFO_SOURCE.JLLswz.id]: {
		id: INFO_SOURCE.JLLswz.id,
		name: '吉林省粮食和物资储备局',
		type: 'province',
		baseUrl: 'http://grain.jl.gov.cn/ztzl/jllssgxx/',
		category: '吉林粮食收购信息',

		urlPattern: 'index{_page}.html',

		selectors: {
			listContainer: '.content .erji_content .ul_main li',
			titleSelector: 'a[title]',
			linkSelector: 'a[href]',
			dateSelector: '.time, .date',
			contentSelector: '.TRS_Editor'
		},

		cropKeywords: ['玉米', '水稻', '收购', '东北粮'],
		defaultCropId: '1'
	},

	// 新疆维吾尔自治区粮食和物资储备局
	[INFO_SOURCE.XJLswz.id]: {
		id: INFO_SOURCE.XJLswz.id,
		name: '新疆维吾尔自治区粮食和物资储备局',
		type: 'province',
		baseUrl: 'https://lswz.xinjiang.gov.cn',
		category: '新疆粮食收购信息',

		urlPattern: '/xjgrain/lssgjd/list_left{_page}.html',

		selectors: {
			listContainer: '.index_block .list li',
			titleSelector: 'a[title]',
			linkSelector: 'a[href]',
			dateSelector: 'span.date',
			contentSelector: '.content_p'
		},

		cropKeywords: ['棉花', '小麦', '玉米', '新疆'],
		defaultCropId: '4' // 新疆主要是棉花
	},

	// 内蒙古自治区粮食和物资储备局 - 多品种农产品信息
	[INFO_SOURCE.NMGLswz.id]: {
		id: INFO_SOURCE.NMGLswz.id,
		name: '内蒙古自治区粮食和物资储备局',
		type: 'complex', // 复杂类型，支持多农产品品种
		baseUrl: 'https://lsj.nmg.gov.cn',

		// 统一分页配置 - 所有农产品栏目使用相同的分页逻辑
		pagination: {
			type: 'template', // 分页类型：template, numeric, query, path
			pattern: 'index{_page}.html', // 分页模式：index.html, index_1.html
			pageSize: null // 不适用于模板分页
		},

		// 多农产品分类配置 - 覆盖主要粮食品种
		categories: [
			// 大豆 - 油料油脂类
			{
				url: 'scxx/ylyz/gnsc_7617/',
				name: '大豆国内市场',
				category: '大豆油料'
			},
			{
				url: 'scxx/ylyz/gjsc_7618/',
				name: '大豆国际市场',
				category: '大豆油料'
			},

			// 稻谷 - 稻谷大米类 (原有栏目扩展)
			{
				url: 'scxx/dgdm/gnsc_7621/',
				name: '稻谷国内市场',
				category: '稻谷大米'
			},
			{
				url: 'scxx/dgdm/gjsc_7622/',
				name: '稻谷国际市场',
				category: '稻谷大米'
			},

			// 玉米 - 玉米薯类
			{
				url: 'scxx/ymsl/gnsc/',
				name: '玉米国内市场',
				category: '玉米薯类'
			},
			{
				url: 'scxx/ymsl/gjsc/',
				name: '玉米国际市场',
				category: '玉米薯类'
			},

			// 小麦 - 小麦面粉类
			{
				url: 'scxx/xmmf/gnsc_7625/',
				name: '小麦国内市场',
				category: '小麦面粉'
			},
			{
				url: 'scxx/xmmf/gjsc_7626/',
				name: '小麦国际市场',
				category: '小麦面粉'
			},

			// 粮油期刊 - 综合信息
			{
				url: 'scxx/',
				name: '粮油期刊',
				category: '综合信息'
			}
		],

		// 统一选择器配置 - 适用于所有农产品栏目
		selectors: {
			listContainer: '.yzgl_box .yzgl_list li, .content-list li, .news-list li',
			titleSelector: 'a[title], .title a, h3 a',
			linkSelector: 'a[href]',
			dateSelector: 'span.date, .time, .publish-time',
			contentSelector: '.trs_editor_view, .content, .article-content',
			authorSelector: '.author, .source'
		},

		cropKeywords: [
			'稻谷',
			'大米',
			'水稻',
			'大豆',
			'豆粕',
			'玉米',
			'小麦',
			'面粉',
			'内蒙古',
			'粮油'
		],
		defaultCropId: '1' // 根据内容智能识别，玉米为默认
	},

	// 中国农作物行情网指数分析 - 农产品指数和行情分析
	[INFO_SOURCE.ChinaCropIndex.id]: {
		id: INFO_SOURCE.ChinaCropIndex.id,
		name: '中国农作物行情网指数分析',
		type: 'html', // HTML网页爬取类型
		baseUrl: 'https://www.chinanzxh.com',
		category: '指数分析', // 数据分类标签

		// 统一分页配置 - 模板分页
		pagination: {
			type: 'template', // 分页类型：template, numeric, query, path
			pattern: 'index{_page}.html', // 分页模式：index.html, index_1.html
			pageSize: null // 不适用于模板分页
		},

		// 单一栏目配置
		categories: [
			{
				url: 'shujuzhongxin/zhishufenxi/',
				name: '指数分析',
				category: '指数分析'
			}
		],

		selectors: {
			// 通用选择器，适应常见的中国农业网站结构
			listContainer: '.news-list li, .list-item, .content-list li, .article-list li, ul li',
			titleSelector: 'h3 a, .title a, h2 a, h4 a, .news-title a, a[title]',
			linkSelector: 'h3 a, .title a, h2 a, h4 a, .news-title a, a[href]',
			dateSelector: '.date, .time, .publish-time, .update-time, span[class*="date"]',
			contentSelector: '.content, .article-content, .main-content, .text-content, .TRS_Editor',
			authorSelector: '.author, .source, .writer, .by'
		},

		// 农作物指数相关关键词
		cropKeywords: [
			'指数',
			'行情',
			'价格',
			'农产品',
			'粮食',
			'小麦',
			'玉米',
			'大豆',
			'水稻',
			'棉花',
			'分析'
		],
		defaultCropId: '1', // 默认归类为玉米相关

		// 内容过滤 - 只获取包含特定关键词的内容
		contentFilter: ['指数分析', '行情分析', '价格分析', '市场分析']
	}
}

// =============================================================================
// 配置验证函数 - 确保所有必填字段都存在
// =============================================================================

/**
 * 验证数据源配置完整性
 * @param {Object} config - 数据源配置对象
 * @returns {Array} - 验证错误列表，空数组表示验证通过
 */
function validateConfig(config) {
	const errors = []
	const requiredFields = ['id', 'name', 'type', 'baseUrl', 'selectors']
	const requiredSelectors = ['listContainer', 'titleSelector', 'linkSelector']

	// 检查必填字段
	for (const field of requiredFields) {
		if (!config[field]) {
			errors.push(`缺少必填字段: ${field}`)
		}
	}

	// 检查必填选择器
	if (config.selectors) {
		for (const selector of requiredSelectors) {
			if (!config.selectors[selector]) {
				errors.push(`缺少必填选择器: ${selector}`)
			}
		}
	}

	// 检查爬虫类型
	const validTypes = ['html', 'api', 'province', 'complex']
	if (config.type && !validTypes.includes(config.type)) {
		errors.push(`无效的爬虫类型: ${config.type}，有效值: ${validTypes.join(', ')}`)
	}

	return errors
}

// 导出配置验证函数供外部使用
module.exports.validateConfig = validateConfig
module.exports.CONFIG_TEMPLATE = CONFIG_TEMPLATE
