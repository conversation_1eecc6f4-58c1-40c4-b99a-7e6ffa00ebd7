const express = require('express')
const fs = require('fs')
const path = require('path')
const router = express.Router()
const wechatMsgCtrl = require('../../controllers/wechatMessageController')
const formidable = require('formidable')

const rootDir = path.resolve(__dirname, '../..')
const uploadDir = path.join(rootDir, 'temp')

if (!fs.existsSync(uploadDir)) {
	fs.mkdirSync(uploadDir, { recursive: true })
}

const form = formidable({
	uploadDir,
	keepExtensions: true,
	filename: (name, ext, part) => {
		return `${Date.now()}-${part.originalFilename}`
	}
})

router.post(
	'/',
	(req, res, next) => {
		form.parse(req, (err, fields, files) => {
			if (err) {
				if (files && files.file && files.file.filepath) {
					fs.unlink(files.file.filepath, unlinkErr => {
						if (unlinkErr) {
							console.error('Failed to clean up temp file:', unlinkErr)
						}
					})
				}
				res.status(500).json({ code: 0, message: `请求解析失败: ${err.message}` })
				return
			}

			if (files.file) {
				const file = files.file
				const maxSize = 100 * 1024 * 1024
				const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf']

				if (file.size > maxSize) {
					fs.unlink(file.filepath, () => {})
					res.status(400).json({ code: 0, message: '文件大小不能超过10MB' })
					return
				}

				if (!allowedTypes.includes(file.mimetype)) {
					fs.unlink(file.filepath, () => {})
					res.status(400).json({ code: 0, message: '不支持的文件类型' })
					return
				}
			}

			req.body = fields
			req.body.file = files.file
			next()
		})
	},
	wechatMsgCtrl.receiveMessage
)

module.exports = router
