const ScraperStrategy = require('./ScraperStrategy')
const ScraperStrategyFactory = require('./ScraperStrategyFactory')
const ScraperManager = require('./ScraperManager')

// 导出所有策略类
const AgriStrategy = require('./strategies/AgriStrategy')
const GrainmarketStrategy = require('./strategies/GrainmarketStrategy')
const ChinaSoybeanStrategy = require('./strategies/ChinaSoybeanStrategy')
const ChinaCottonStrategy = require('./strategies/ChinaCottonStrategy')
const ProvinceStrategy = require('./strategies/ProvinceStrategy')

module.exports = {
	// 核心类
	ScraperStrategy,
	ScraperStrategyFactory,
	ScraperManager,

	// 具体策略
	AgriStrategy,
	GrainmarketStrategy,
	ChinaSoybeanStrategy,
	ChinaCottonStrategy,
	ProvinceStrategy
}
