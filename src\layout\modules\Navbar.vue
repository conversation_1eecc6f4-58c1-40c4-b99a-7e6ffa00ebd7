<template>
  <!-- Navbar -->
  <nav class="navbar">
    <!-- Left navbar links -->
    <div class="navbar-left">
      <div class="hamburger-container" @click="toggleSidebar">
        <i v-if="!collapse" class="el-icon-s-fold"></i>
        <i v-else class="el-icon-s-unfold"></i>
      </div>
      <div class="breadcrumb-container">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item v-for="(item, index) in breadcrumbs" :key="index">
            {{ item }}
          </el-breadcrumb-item>
        </el-breadcrumb>
      </div>
    </div>

    <!-- Right navbar links -->
    <div class="navbar-right">
      <el-dropdown class="user-dropdown" @command="handleCommand">
        <div class="user-info">
          <img :src="admin.avatar || defaultAvatar" class="user-avatar" alt="user" />
          <span class="user-name">{{ admin.name || '未知用户' }}</span>
          <i class="el-icon-arrow-down el-icon--right"></i>
        </div>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command="password" disabled>
            <i class="el-icon-lock"></i>
            修改密码
          </el-dropdown-item>
          <el-dropdown-item divided command="logout">
            <i class="el-icon-switch-button"></i>
            注销登录
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
  </nav>
  <!-- /.navbar -->
</template>

<script>
	export default {
		data() {
			return {
				defaultAvatar: require('../../images/logo.png') // 设置默认头像
			}
		},
		computed: {
			admin() {
				return this.$store.state.auth.admin || {}
			},
			collapse() {
				return this.$store.state.layout.collapseMenu
			},
			breadcrumbs() {
				const matched = this.$route.matched.filter(item => item.meta && item.meta.title)
				return matched.map(item => item.meta.title)
			}
		},
		methods: {
			toggleSidebar() {
				this.$store.commit('layout/SIDEBAR_COLLAPSE_CHANGE')
			},
			handleCommand(command) {
				switch (command) {
					case 'password':
						this.$emit('show-password-dialog')
						break
					case 'logout':
						this.handleLogout()
						break
				}
			},
			async handleLogout() {
				try {
					await this.$confirm('确定注销并退出系统吗？', '提示', {
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning'
					})
					await this.$store.dispatch('auth/logout')
					this.$message.success('您已成功退出')
					if (this.$route.path !== '/login') {
						this.$router.push('/login')
					}
				} catch (error) {
					console.error('退出登录失败:', error)
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.navbar {
		height: 60px;
		line-height: 60px;
		width: 100%;
		display: flex;
		justify-content: space-between;
		align-items: center;
		background: #fff;
		position: relative;
		box-sizing: border-box;

		.navbar-left {
			display: flex;
			align-items: center;
			height: 100%;

			.hamburger-container {
				cursor: pointer;
				transition: background 0.3s;
				padding: 0 15px;
				height: 100%;
				display: flex;
				align-items: center;

				&:hover {
					background: rgba(0, 0, 0, 0.025);
				}

				i {
					font-size: 22px;
				}
			}

			.breadcrumb-container {
				margin-left: 15px;
			}
		}

		.navbar-right {
			display: flex;
			align-items: center;

			.user-dropdown {
				cursor: pointer;

				.user-info {
					display: flex;
					align-items: center;
					height: 60px;
					padding: 0 10px;

					&:hover {
						background: rgba(0, 0, 0, 0.025);
					}

					.user-avatar {
						width: 36px;
						height: 36px;
						border-radius: 50%;
						margin-right: 10px;
					}

					.user-name {
						font-weight: 500;
						margin-right: 5px;
						white-space: nowrap;
					}
				}
			}
		}
	}
</style>
